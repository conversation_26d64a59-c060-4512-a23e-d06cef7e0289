﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATFAS.Models
{
    public class GDP
    {
        public int Id { get; set; }

        [Required, StringLength(250)]
        public string Designator { get; set; }

        [Display(Name = "ATM Resource")]
        public int TrafficAreaId { get; set; }

        [ForeignKey("TrafficAreaId"), Display(Name = "ATM Resource")]
        public TrafficArea TrafficArea { get; set; }

        [Required, StringLength(250), Display(Name = "Location Designator")]
        public string Point { get; set; }

        [Display(Name = "Lower Flight Level (F/A)"), Range(0, 460)]
        public int? LowerFlightLevel { get; set; }

        [Display(Name = "Upper Flight Level (F/A)"), Range(0, 460)]
        public int? UpperFlightLevel { get; set; }

        [Display(Name = "Radius (NM)"), Range(1, 100)]
        public int? RadiusNm { get; set; }

        [Display(Name = "Capacity"), Range(0, 120)]
        public int CapacityPerHr { get; set; }

        [Display(Name = "Recovery Capacity"), Range(0, 120)]
        public int CapacityRecoveryPerHr { get; set; }

        [Column(TypeName = "smalldatetime"), Display(Name = "Start"), DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm}")]
        public DateTime StartTime { get; set; }

        [Column(TypeName = "smalldatetime"), Display(Name = "End"), DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm}")]
        public DateTime EndTime { get; set; }

        [Column(TypeName = "smalldatetime"), Display(Name = "End Recovery"), DisplayFormat(DataFormatString = "{0:yyyy-MM-dd HH:mm}")]
        public DateTime EndRecoveryTime { get; set; }

        [StringLength(1000), Display(Name = "Only ADEP"), RegularExpression(@"[0-9a-zA-Z]+(,[0-9a-zA-Z]+)*")]
        public string OnlyADEP { get; set; }

        [StringLength(1000), Display(Name = "Only ADES"), RegularExpression(@"[0-9a-zA-Z]+(,[0-9a-zA-Z]+)*")]
        public string OnlyADES { get; set; }

        [StringLength(1000), Display(Name = "Only Airlines"), RegularExpression(@"[0-9a-zA-Z]+(,[0-9a-zA-Z]+)*")]
        public string OnlyAirlines { get; set; }

        [StringLength(1000), Display(Name = "Exempt ADEP"), RegularExpression(@"[0-9a-zA-Z]+(,[0-9a-zA-Z]+)*")]
        public string ExemptADEP { get; set; }

        [StringLength(1000), Display(Name = "Exempt ADES"), RegularExpression(@"[0-9a-zA-Z]+(,[0-9a-zA-Z]+)*")]
        public string ExemptADES { get; set; }

        [StringLength(1000), Display(Name = "Exempt Airlines"), RegularExpression(@"[0-9a-zA-Z]+(,[0-9a-zA-Z]+)*")]
        public string ExemptAirlines { get; set; }

        [Display(Name = "Graph Monitoring Interval (Minutes)"), Range(1, 1440)]
        public int IntervalMin { get; set; }

        [Display(Name = "IFR")]
        public bool IsIFR { get; set; }

        [Display(Name = "VFR")]
        public bool IsVFR { get; set; }

        [Display(Name = "FPL")]
        public bool IsFPL { get; set; }

        [Display(Name = "SCH")]
        public bool IsSCH { get; set; }

        public bool IsExecuted { get; set; }
        public bool IsActive { get; set; } // active => EndRecoveryTime > DateTime.UtcNow
        public bool IsCancelled { get; set; }
        public DateTime TimeSaved { get; set; }
        public DateTime? TimeExecuted { get; set; }
        public DateTime? TimeUpdated { get; set; }
        public DateTime? TimeCancelled { get; set; }

        [StringLength(20), Display(Name = "Regul"), RegularExpression(@"^[A-Z0-9]{1,20}$")]
        public string Regul { get; set; }

        [StringLength(20), Display(Name = "Recause"), RegularExpression(@"^[A|C|D|E|G|I|M|N|O|P|R|S|T|V|W][A|D|E][\s][\d]{2}$")]
        public string Regcause { get; set; }

        [StringLength(1000), Display(Name = "AFTN Comment"), RegularExpression(@"^[\w\d\\\s\|\?\:\.\,\'\=\+\/]{1,1500}$")]
        public string Comment { get; set; }

        public List<GDPFlight> GDPFlights { get; set; }

        private string ErrorMsg;

        public bool IsValid()
        {
            ErrorMsg = "";
            if (TrafficAreaId != 1 && LowerFlightLevel >= UpperFlightLevel) ErrorMsg += "The field Lower Flight Level must be less than the field Upper Flight Level.\n";
            if (StartTime >= EndTime) ErrorMsg += "The field Start must be less than the field End.\n";
            if (EndTime > EndRecoveryTime) ErrorMsg += "The field End must be less than the field End Recovery.\n";
            if (!IsIFR && !IsVFR)
            {
                ErrorMsg += "The IFR or VFR field is required.\n";
            }
            if (ErrorMsg.Length > 1) return false;
            return true;
        }

        public void MakeValid()
        {
            if (TrafficAreaId == 1)
            {
                LowerFlightLevel = null;
                UpperFlightLevel = null;
                RadiusNm = null;
            }
            else if (TrafficAreaId != 2) RadiusNm = null;
            Point = Point.ToUpper();
            if (OnlyADEP != null) OnlyADEP = OnlyADEP.ToUpper();
            if (OnlyADES != null) OnlyADES = OnlyADES.ToUpper();
            if (OnlyAirlines != null) OnlyAirlines = OnlyAirlines.ToUpper();
            if (ExemptADEP != null) ExemptADEP = ExemptADEP.ToUpper();
            if (ExemptADES != null) ExemptADES = ExemptADES.ToUpper();
            if (ExemptAirlines != null) ExemptAirlines = ExemptAirlines.ToUpper();
        }

        public string GetErrorMsg()
        {
            return ErrorMsg;
        }
    }
}
