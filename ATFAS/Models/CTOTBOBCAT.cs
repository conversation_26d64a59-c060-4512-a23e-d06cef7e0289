﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Threading.Tasks;

namespace ATFAS.Models
{
    public class CTOTBOBCAT
    {
        public int Id { get; set; }
        public int FPLId { get; set; }
        [StringLength(50)]
        public string AircraftId { get; set; }
        [StringLength(100)]
        public string Departure{ get; set; }
        [StringLength(100)]
        public string Arrival { get; set; }
        public DateTime? EOBT { get; set; }
        public DateTime? ETOT { get; set; }
        public DateTime? ELDT { get; set; }
        public DateTime? EIBT { get; set; }
        public DateTime? CTOT { get; set; }
        public DateTime? CTO { get; set; }
        public DateTime Timestamp { get; set; }
        public bool IsCancelled { get; set; }


    }
}
