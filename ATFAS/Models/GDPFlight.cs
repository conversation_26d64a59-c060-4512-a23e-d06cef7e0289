﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations.Schema;

namespace ATFAS.Models
{
    public class GDPFlight
    {
        public int Id { get; set; }
        public int FlightId { get; set; }
        [ForeignKey("FlightId")]
        public Flight Flight { get; set; }

        [Column(TypeName = "smalldatetime")]
        public DateTime? COBT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? CTOT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? CLDT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? CIBT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? COBTSaved { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? CTOTSaved { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? CLDTSaved { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? CIBTSaved { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? NewEOBT { get; set; }
        public string Username { get; set; }
        public string Ip { get; set; }

        public bool IsSent { get; set; }
        public bool IsCancelled { get; set; }
        public bool IsTrial { get; set; }
        public bool IsGdpExempt { get; set; }
        public DateTime TimeSaved { get; set; }
        public DateTime TimeUpdated { get; set; }
        public DateTime TimeSent { get; set; }
        public DateTime TimeCancelled { get; set; }
        public string Comment { get; set; }
        public bool IsOutOfRange { get; set; }   // in case gdpflight out of range

        public List<GDP> GDPs { get; set; }
    }
}
