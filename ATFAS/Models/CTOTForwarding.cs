﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Linq;
using System.Threading.Tasks;
using ATFAS.Areas.Identity.Data;

namespace ATFAS.Models
{
    public class CTOTForwarding
    {
        public int Id { get; set; }
        [Column(TypeName = "VARCHAR")]
        [StringLength(50)]
        public string Originator { get; set; }
        public Nullable<int> FPLId { get; set; }
        [Column(TypeName = "VARCHAR")]
        [StringLength(50)]
        public string AircraftId { get; set; }
        [Column(TypeName = "VARCHAR")]
        [StringLength(50)]
        public string AircraftType { get; set; }
        [Column(TypeName = "VARCHAR")]
        [StringLength(100)]
        public string Departure { get; set; }
        [Column(TypeName = "VARCHAR")]
        [StringLength(100)]
        public string Arrival { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? EOBT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? ETOT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? ELDT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? EIBT { get; set; }
        [Column(TypeName = "smalldatetime")]
        public DateTime? CTOT { get; set; }
        [Column(TypeName = "VARCHAR(MAX)")]
        [MaxLength]
        public string RawMessage { get; set; }
        [Column(TypeName = "VARCHAR(MAX)")]
        [MaxLength]
        public string REGUL { get; set; }
        [Column(TypeName = "VARCHAR(MAX)")]
        [MaxLength]
        public string REASON { get; set; }
        [Column(TypeName = "VARCHAR(MAX)")]
        [MaxLength]
        public string COMMENT { get; set; }
        [Column(TypeName = "VARCHAR(MAX)")]
        [MaxLength]
        public string REGCAUSE { get; set; }
        [Required]
        public bool IsCancelled { get; set; }
        public bool IsUpdated{ get; set; }
        public int? Status { get; set; }
       
        [Column(TypeName = "smalldatetime")]
        public DateTime? NEWEOBT { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? TimeStamp { get; set; }
        public string Username { get; set; }
        public string Ip { get; set; }
        public string UserComment { get; set; }
        public string ATFMUComment { get; set; }

    }

}

