﻿using ATFAS.Areas.Identity.Data;
using ATFAS.Models;
using Microsoft.AspNetCore.Identity.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;

namespace ATFAS.Data
{
    public class ATFASContext : IdentityDbContext<AppUser>
    {
        public ATFASContext(DbContextOptions<ATFASContext> options) : base(options) { }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            base.OnModelCreating(modelBuilder);

            modelBuilder.Entity<AppUser>().Ignore(e => e.FullName);
            modelBuilder.Entity<UserProfile>().<PERSON><PERSON><PERSON>(x => new { x.UserId, x.ProfileName });
            //modelBuilder.Entity<AppUser>().HasQueryFilter(x => x.UserName != "superadmin");

            modelBuilder.Entity<Capacity>().HasIndex(c => c.Point);
            modelBuilder.Entity<Capacity>().Property(c => c.TimeSaved).HasDefaultValueSql("getutcdate()");
            modelBuilder.Entity<CapacityEvent>().Property(c => c.TimeSaved).HasDefaultValueSql("getutcdate()");
        }

        public DbSet<Movie> Movie { get; set; }
        public DbSet<Trajectory> Trajectory { get; set; }
        public DbSet<FlightSource> FlightSource { get; set; }
        public DbSet<FlightState> FlightState { get; set; }
        public DbSet<Flight> Flight { get; set; }
        public DbSet<TrafficDemand> TrafficDemand { get; set; }
        public DbSet<TrafficArea> TrafficArea { get; set; }
        public DbSet<Airport> Airport { get; set; }
        public DbSet<Fix> Fix { get; set; }
        public DbSet<ActionLog> ActionLog { get; set; }
        public DbSet<GeneralConfiguration> GeneralConfiguration { get; set; }
        public DbSet<StaticAirspace> StaticAirspace { get; set; }
        public DbSet<UserDefinedAirspace> UserDefinedAirspace { get; set; }
        public DbSet<Capacity> Capacity { get; set; }
        public DbSet<CapacityInterval> CapacityInterval { get; set; }
        public DbSet<CapacityEvent> CapacityEvent { get; set; }
        public DbSet<FlightTrajectory> FlightTrajectory { get; set; }
        public DbSet<UserProfile> UserProfile { get; set; }
        public DbSet<Announcement> Announcement { get; set; }
        public DbSet<Fileupload> Fileupload { get; set; }
        public DbSet<TDSituationAwareness> TDSituationAwareness { get; set; }
        public DbSet<Adp> Adp { get; set; }
        public DbSet<Atfmu> Atfmu { get; set; }
        public DbSet<GDP> GDP { get; set; }
        public DbSet<GDPFlight> GDPFlight { get; set; }
        public DbSet<AdpData> AdpData { get; set; }
        public DbSet<AdpConstraint> AdpConstraint { get; set; }
        public DbSet<AdpMeasure> AdpMeasure { get; set; }
        public DbSet<ForwardSlotMessageReview> ForwardSlotMessageReview { get; set; }
        public DbSet<CTOTForwarding> CTOTForwarding { get; set; }
        public DbSet<SAMMessage> SAMMessage { get; set; }
        public DbSet<FlightSched> FlightSched { get; set; }
        public DbSet<UserNotification> UserNotification { get; set; }
        public DbSet<Pointofcontact> Pointofcontact { get; set; }
        public DbSet<CTOTBOBCAT> CTOTBOBCAT { get; set; }

    }
}
