{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    "ATFASContext": "Server=*************;Database=ATFAS;User Id=atfm;Password=********;MultipleActiveResultSets=true;Connect Timeout=300",
    "UserDatabaseConnection": "Server=*************;Database=ATFASUser;User Id=atfm;Password=********;MultipleActiveResultSets=true;"
  },
  "SuperAdmin": {
    "Username": "superadmin",
    "Firstname": "superadmin",
    "Lastname": "superadmin",
    "Email": "super@super",
    "Password": "Aaaaa*222"
  },
  "NewUserDefaultPassword": "Aerothai*123",
  "OpenIdConfig": {
    "Authority": "https://*************/auth/realms/ARTSSO",
    "ClientId": "ATFAS",
    "CallbackPath": "/callbacksso",
    "RegisterPageLink": "http://*************/",
    "LogoutRedirect": "https://*************/auth/realms/ARTSSO/protocol/openid-connect/logout?redirect_uri=http%3A%2F%2F*************%2Fpages-full%2Fauthorizedapps"
  },
  "CookieExpireSec": 3600,
  "ElasticIP": "http://*************:9200",
  "ATFMOptimizerIP": "http://*************:5000/", /*"http://*************:5000/",*/
  "ATFMOptimizerManagementIP": "http://*************:5000/", /*http://*************:5000/*/
  "FlightPermissionIP": "http://**************/fpms2/",
  "HECPeriod": 20,
  "MailSettings": {
    "Mail": "<EMAIL>",
    "DisplayName": "ATFAS TEST",
    "Password": "AtF@s2020",
    "AppPassword": "klqbudqphlrkcihj",
    "Host": "smtp.gmail.com",
    "Port": 587,
    "Secure": true
  },
  "idepRegionalIP": "https://localhost:44362",
  "SecretKey": "atfas2020",
  "EnableCTOTtoIdep": false,
  "Alpha": 0.02,
  "FastMaxDelayIndex": 60,
  "TimeLimit": 50,
  "WindowStep" :  5

}
