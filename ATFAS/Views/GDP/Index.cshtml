﻿@model ATFAS.ViewModels.GDPInfo

@{
    ViewData["Title"] = "Manage GDP";
}

<h4>Ground Delay Program (GDP)</h4>
<div class="clearfix">
    <a class="float-left" asp-controller="GDPManage">Manage</a>
    <b id="td-update" class="float-right" style="display:none;">Last Updated: <span class="td-update-time"></span></b>
</div>
<hr />
<div class="row">
    <button type="button" class="btn btn-secondary ml-3 px-0 py-1" style="width:30px;" data-toggle="collapse" href="#td-collapse" data-hover="tooltip" data-placement="right" data-trigger="hover" title="hide / show"><i class="fas fa-exchange-alt fa-lg"></i></button>
    <div id="td-collapse" class="col-md-4 col-xl-3 collapse show">
        <div class="form-group">
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="paramOption" id="measure-radio" value="measure" checked>
                <label class="form-check-label" for="measure-radio">Measure</label>
            </div>
            <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" name="paramOption" id="demand-radio" value="demand">
                <label class="form-check-label" for="demand-radio">Traffic Demand</label>
            </div>
        </div>
        <form id="td-form">
            <div asp-validation-summary="ModelOnly" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="TrafficDemand.Designator" class="control-label"></label>
                <select id="designator" class="form-control" style="display:none;">
                    <option value="-1">&lt;Saved traffic demand designator&gt;</option>
                </select>
                <select id="measure-designator" class="form-control">
                    <option class="saved" value="-1">&lt;Saved measure designator&gt;</option>
                    <option class="executed" value="-1">&lt;Executed measure designator&gt;</option>
                </select>
            </div>
            <fieldset id="td-fieldset">
                <div class="form-group">
                    <label asp-for="TrafficDemand.TrafficAreaId" class="control-label" for="TrafficAreaId"></label>
                    <select asp-for="TrafficDemand.TrafficAreaId" id="TrafficAreaId" class="form-control" asp-items="ViewBag.TrafficAreaId"></select>
                </div>
                <div class="form-group">
                    <label asp-for="TrafficDemand.Point" class="control-label" for="Point"></label>
                    <input asp-for="TrafficDemand.Point" id="Point" class="form-control text-uppercase" />
                    <span asp-validation-for="TrafficDemand.Point" class="text-danger"></span>
                </div>
                <div class="td-param" style="display:none;">
                    <div class="form-group td-airport">
                        <div class="form-check form-check-inline">
                            <label class="form-check-label">
                                <input class="form-check-input" asp-for="TrafficDemand.IsDep" id="IsDep" checked />@Html.DisplayNameFor(model => model.TrafficDemand.IsDep)
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <label class="form-check-label">
                                <input class="form-check-input" asp-for="TrafficDemand.IsArr" id="IsArr" checked />@Html.DisplayNameFor(model => model.TrafficDemand.IsArr)
                            </label>
                        </div>
                        <div class="form-check form-check-inline">
                            <label class="form-check-label">
                                <input class="form-check-input" asp-for="TrafficDemand.IsCombined" id="IsCombined" />@Html.DisplayNameFor(model => model.TrafficDemand.IsCombined)
                            </label>
                        </div>
                    </div>
                </div>
                <div class="form-group td-waypoint td-sector" style="display:none;">
                    <label asp-for="TrafficDemand.LowerFlightLevel" class="control-label" for="LowerFlightLevel"></label>
                    <input asp-for="TrafficDemand.LowerFlightLevel" id="LowerFlightLevel" class="form-control" placeholder="[0-460]" />
                    <span asp-validation-for="TrafficDemand.LowerFlightLevel" class="text-danger"></span>
                </div>
                <div class="form-group td-waypoint td-sector" style="display:none;">
                    <label asp-for="TrafficDemand.UpperFlightLevel" class="control-label" for="UpperFlightLevel"></label>
                    <input asp-for="TrafficDemand.UpperFlightLevel" id="UpperFlightLevel" class="form-control" placeholder="[0-460]" />
                    <span asp-validation-for="TrafficDemand.UpperFlightLevel" class="text-danger"></span>
                </div>
                <div class="form-group td-waypoint" style="display:none;">
                    <label asp-for="TrafficDemand.RadiusNm" class="control-label" for="RadiusNm"></label>
                    <input asp-for="TrafficDemand.RadiusNm" id="RadiusNm" class="form-control" placeholder="[1-100]" />
                    <span asp-validation-for="TrafficDemand.RadiusNm" class="text-danger"></span>
                </div>
                <div class="form-group measure-param">
                    <label class="control-label" for="event-capacity-select">Event Capacity Designator</label>
                    <select class="form-control" id="event-capacity-select" name="event-capacity">
                        <option value="-1">&lt;Saved event capacity&gt;</option>
                    </select>
                </div>
                <div class="form-group td-param" style="display:none;">
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="timeOption" id="td-specify" value="specify" checked>
                        <label class="form-check-label" for="td-specify">Specify</label>
                    </div>
                    <div class="form-check form-check-inline">
                        <input class="form-check-input" type="radio" name="timeOption" id="td-current" value="current">
                        <label class="form-check-label" for="td-current">Current</label>
                    </div>
                </div>
                <div class="form-group td-specify">
                    <label asp-for="GDP.StartTime" class="control-label" for="StartTime"></label>
                    <input asp-for="GDP.StartTime" id="StartTime" class="form-control" type="datetime" autocomplete="off" />
                    <span asp-validation-for="GDP.StartTime" class="text-danger"></span>
                </div>
                <div class="form-group td-specify">
                    <label asp-for="GDP.EndTime" class="control-label" for="EndTime"></label>
                    <input asp-for="GDP.EndTime" id="EndTime" class="form-control" type="datetime" autocomplete="off" />
                    <span asp-validation-for="GDP.EndTime" class="text-danger"></span>
                </div>
                <div class="form-group measure-param">
                    <label asp-for="GDP.EndRecoveryTime" class="control-label" for="EndRecoveryTime"></label>
                    <input asp-for="GDP.EndRecoveryTime" id="EndRecoveryTime" class="form-control" type="datetime" autocomplete="off" />
                    <span asp-validation-for="GDP.EndRecoveryTime" class="text-danger"></span>
                </div>
                <div class="form-group td-current" style="display:none;">
                    <label asp-for="TrafficDemand.AheadHour" class="control-label" for="AheadHour"></label>
                    <input asp-for="TrafficDemand.AheadHour" id="AheadHour" class="form-control" placeholder="[1-48]" data-val-required="The Hours Ahead field is required." />
                    <span asp-validation-for="TrafficDemand.AheadHour" class="text-danger"></span>
                </div>
                <div class="form-group measure-param">
                    <label asp-for="GDP.CapacityPerHr" class="control-label" for="CapacityPerHr">Event Capacity (Per Hour)</label>
                    <input asp-for="GDP.CapacityPerHr" id="CapacityPerHr" class="form-control" placeholder="[0-120]" />
                    <span asp-validation-for="GDP.CapacityPerHr" class="text-danger"></span>
                </div>
                <div class="form-group measure-param">
                    <label asp-for="GDP.CapacityRecoveryPerHr" class="control-label" for="CapacityRecoveryPerHr">Recovery Capacity (Per Hour)</label>
                    <input asp-for="GDP.CapacityRecoveryPerHr" id="CapacityRecoveryPerHr" class="form-control" placeholder="[0-120]" />
                    <span asp-validation-for="GDP.CapacityRecoveryPerHr" class="text-danger"></span>
                </div>
                <div class="form-group">
                    <label asp-for="GDP.IntervalMin" class="control-label measure-param" for="IntervalMin"></label>
                    <label asp-for="TrafficDemand.IntervalMin" class="control-label td-param" for="IntervalMin" style="display:none;"></label>
                    <input asp-for="TrafficDemand.IntervalMin" id="IntervalMin" class="form-control" placeholder="[1-1440]" value="20" />
                    <span asp-validation-for="TrafficDemand.IntervalMin" class="text-danger"></span>
                </div>
                <div class="form-group measure-param">
                    <a class="btn btn-light" data-toggle="collapse" href="#OnlyFilter" role="button">Only Filter In</a>
                </div>
                <div class="collapse" id="OnlyFilter">
                    <div class="form-group measure-param">
                        <label asp-for="GDP.OnlyADEP" class="control-label" for="OnlyADEP"></label>
                        <input asp-for="GDP.OnlyADEP" id="OnlyADEP" class="form-control" placeholder="ICAO airport codes separated by comma (,)">
                        <span asp-validation-for="GDP.OnlyADEP" class="text-danger"></span>
                    </div>
                    <div class="form-group measure-param">
                        <label asp-for="GDP.OnlyADES" class="control-label" for="OnlyADES"></label>
                        <input asp-for="GDP.OnlyADES" id="OnlyADES" class="form-control" placeholder="ICAO airport codes separated by comma (,)">
                        <span asp-validation-for="GDP.OnlyADES" class="text-danger"></span>
                    </div>
                    <div class="form-group measure-param">
                        <label asp-for="GDP.OnlyAirlines" class="control-label" for="OnlyAirlines"></label>
                        <input asp-for="GDP.OnlyAirlines" id="OnlyAirlines" class="form-control" placeholder="ICAO airline codes separated by comma (,)">
                        <span asp-validation-for="GDP.OnlyAirlines" class="text-danger"></span>
                    </div>
                </div>
                <div class="form-group measure-param">
                    <a class="btn btn-light" data-toggle="collapse" href="#Exempt" role="button">Exempt</a>
                </div>
                <div class="collapse" id="Exempt">
                    <div class="form-group measure-param">
                        <label asp-for="GDP.ExemptADEP" class="control-label" for="ExemptADEP"></label>
                        <input asp-for="GDP.ExemptADEP" id="ExemptADEP" class="form-control" placeholder="ICAO airport codes separated by comma (,)">
                        <span asp-validation-for="GDP.ExemptADEP" class="text-danger"></span>
                    </div>
                    <div class="form-group measure-param">
                        <label asp-for="GDP.ExemptADES" class="control-label" for="ExemptADES"></label>
                        <input asp-for="GDP.ExemptADES" id="ExemptADES" class="form-control" placeholder="ICAO airport codes separated by comma (,)">
                        <span asp-validation-for="GDP.ExemptADES" class="text-danger"></span>
                    </div>
                    <div class="form-group measure-param">
                        <label asp-for="GDP.ExemptAirlines" class="control-label" for="ExemptAirlines"></label>
                        <input asp-for="GDP.ExemptAirlines" id="ExemptAirlines" class="form-control" placeholder="ICAO airline codes separated by comma (,)">
                        <span asp-validation-for="GDP.ExemptAirlines" class="text-danger"></span>
                    </div>
                </div>
                <div class="form-group">
                    <label class="control-label mr-3">Flight Rule</label>
                    <div class="form-check form-check-inline">
                        <label class="form-check-label">
                            <input class="form-check-input" asp-for="GDP.IsIFR" id="IsIFR" checked />@Html.DisplayNameFor(model => model.GDP.IsIFR)
                        </label>
                    </div>
                    <div class="form-check form-check-inline">
                        <label class="form-check-label">
                            <input class="form-check-input" asp-for="GDP.IsVFR" id="IsVFR" />@Html.DisplayNameFor(model => model.GDP.IsVFR)
                        </label>
                    </div>
                </div>
                <div class="form-group td-param" style="display:none;">
                    <label class="control-label mr-2">Source</label>
                    <div class="form-check form-check-inline mr-2">
                        <label class="form-check-label" data-toggle="tooltip" data-trigger="hover" title="Flight Schedule">
                            <input class="form-check-input" asp-for="TrafficDemand.IsSCH" id="IsSCH" />@Html.DisplayNameFor(model => model.TrafficDemand.IsSCH)
                        </label>
                    </div>
                    <div class="form-check form-check-inline mr-2">
                        <label class="form-check-label" data-toggle="tooltip" data-trigger="hover" title="Flight Plan">
                            <input class="form-check-input" asp-for="TrafficDemand.IsFPL" id="IsFPL" checked />@Html.DisplayNameFor(model => model.TrafficDemand.IsFPL)
                        </label>
                    </div>
                    <div class="form-check form-check-inline mr-2">
                        <label class="form-check-label" data-toggle="tooltip" data-trigger="hover" title="Ground Delay Program">
                            <input class="form-check-input" asp-for="TrafficDemand.IsATFM" id="IsATFM" checked />@Html.DisplayNameFor(model => model.TrafficDemand.IsATFM)
                        </label>
                    </div>
                    <div class="form-check form-check-inline mr-2">
                        <label class="form-check-label" data-toggle="tooltip" data-trigger="hover" title="Departure Message">
                            <input class="form-check-input" asp-for="TrafficDemand.IsATSMSG" id="IsATSMSG" checked />@Html.DisplayNameFor(model => model.TrafficDemand.IsATSMSG)
                        </label>
                    </div>
                    <div class="form-check form-check-inline td-arr td-waypoint td-sector mr-2">
                        <label class="form-check-label" data-toggle="tooltip" data-trigger="hover" title="Surveillance Data">
                            <input class="form-check-input" asp-for="TrafficDemand.IsSUR" id="IsSUR" checked />@Html.DisplayNameFor(model => model.TrafficDemand.IsSUR)
                        </label>
                    </div>
                    <div class="form-check form-check-inline td-arr td-waypoint td-sector mr-0">
                        <label class="form-check-label">
                            <input class="form-check-input" asp-for="TrafficDemand.IsPassed" id="IsPassed" checked /><span class="td-airport" data-toggle="tooltip" data-trigger="hover" title="Arrival Message">ARR</span><span class="td-waypoint td-sector" data-toggle="tooltip" data-trigger="hover" title="Surveillance Data for Entry" style="display:none;">@Html.DisplayNameFor(model => model.TrafficDemand.IsPassed)</span>
                        </label>
                    </div>
                </div>
            </fieldset>
            <div class="form-group">
                <input type="submit" value="Create" id="measure-submit" class="btn btn-primary" />
                <input type="submit" value="Create" id="td-submit" class="btn btn-primary" style="display:none;" />
                <button type="button" id="td-clear" class="btn btn-link">Clear</button>
            </div>
        </form>
    </div>
    <div id="td-chart-container" class="col-md">
        <div class="float-left mr-lg-3 mb-3" style="position:relative; width:600px; height:300px; display:none;">
            <canvas class="td-chart"></canvas>
            <div class="td-chart-btn position-absolute" style="top:0; right:0;">
                <button type="button" class="td-chart-remove close" data-toggle="tooltip" data-trigger="hover" title="remove">
                    <span>&times;</span>
                </button>
                <button type="button" class="td-chart-save close p-0 mr-2" data-toggle="tooltip" data-trigger="hover" title="save" style="display:none;">
                    <i class="fas fa-save fa-xs"></i>
                </button>
                <button type="button" class="td-chart-change close p-0 mr-2" data-toggle="tooltip" data-trigger="hover" title="change state" style="display:none;">
                    <i class="fas fa-exchange-alt fa-xs"></i>
                </button>
                <button type="button" class="td-chart-export close p-0 mr-2" data-toggle="tooltip" data-trigger="hover" title="export">
                    <i class="fas fa-share-square fa-xs"></i>
                </button>
            </div>
            <span class="td-chart-warning badge badge-pill badge-warning position-absolute" style="top:8px; left:7px; display:none;">Not update</span>
        </div>
    </div>
</div>
<div id="td-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <table class="table table-borderless">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">ACID</th>
                            <th scope="col">ADEP</th>
                            <th scope="col">ADES</th>
                            <th scope="col">OBT</th>
                            <th scope="col">TOT</th>
                            <th scope="col" class="td-waypoint" style="display:none;">TO</th>
                            <th scope="col" class="td-sector" style="display:none;">INB</th>
                            <th scope="col" class="td-sector" style="display:none;">OUTB</th>
                            <th scope="col">LDT</th>
                            <th scope="col" class="td-gdp" style="display:none;">Est. Delay</th>
                        </tr>
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div id="confirm-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary confirm-modal-btn">Confirm</button>
            </div>
        </div>
    </div>
</div>
<div id="save-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form>
                <div class="modal-header">
                    <h5 class="modal-title">Save Traffic Demand Parameter</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Do you want to save the following traffic demand parameter?</p>
                    <div class="form-group form-row">
                        <label class="col-md-5 col-form-label">@Html.DisplayNameFor(model => model.TrafficDemand.Designator)</label>
                        <div class="col-md-7">
                            <input asp-for="TrafficDemand.Designator" class="form-control save-modal-designator" placeholder="designator" />
                            <span asp-validation-for="TrafficDemand.Designator" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.TrafficArea)</div>
                        <div class="col-md-7 save-modal-area">Airport</div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.Point)</div>
                        <div class="col-md-7 save-modal-point">VTBS</div>
                    </div>
                    <div class="form-group form-row td-airport">
                        <div class="offset-md-5 col-md-7">
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-dep" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsDep)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-arr" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsArr)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-combined" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsCombined)
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-row td-waypoint td-sector td-lower">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.LowerFlightLevel)</div>
                        <div class="col-md-7 save-modal-lower">0</div>
                    </div>
                    <div class="form-group form-row td-waypoint td-sector td-upper">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.UpperFlightLevel)</div>
                        <div class="col-md-7 save-modal-upper">0</div>
                    </div>
                    <div class="form-group form-row td-waypoint td-radius">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.RadiusNm)</div>
                        <div class="col-md-7 save-modal-radius">1</div>
                    </div>
                    <div class="form-group form-row">
                        <div class="offset-md-5 col-md-7">
                            <div class="form-check form-check-inline">
                                <input class="form-check-input save-modal-specify" type="radio" disabled />
                                <label class="form-check-label">Specify</label>
                            </div>
                            <div class="form-check form-check-inline">
                                <input class="form-check-input save-modal-current" type="radio" disabled />
                                <label class="form-check-label">Current</label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-row td-specify">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.StartTime)</div>
                        <div class="col-md-7 save-modal-start">2019/01/01 00:00</div>
                    </div>
                    <div class="form-group form-row td-specify">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.EndTime)</div>
                        <div class="col-md-7 save-modal-end">2019/01/01 12:00</div>
                    </div>
                    <div class="form-group form-row td-current">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.AheadHour)</div>
                        <div class="col-md-7 save-modal-ahead">24</div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.IntervalMin)</div>
                        <div class="col-md-7 save-modal-interval">120</div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">Flight Rule</div>
                        <div class="col-md-7">
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input ifr" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsIFR)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input vfr" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsVFR)
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">Source</div>
                        <div class="col-md-7">
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-sch" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsSCH)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-fpl" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsFPL)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-atfm" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsATFM)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-atsmsg" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsATSMSG)
                                </label>
                            </div>
                            <div class="form-check form-check-inline td-arr td-waypoint td-sector">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-sur" type="checkbox" disabled />@Html.DisplayNameFor(model => model.TrafficDemand.IsSUR)
                                </label>
                            </div>
                            <div class="form-check form-check-inline td-arr td-waypoint td-sector">
                                <label class="form-check-label">
                                    <input class="form-check-input save-modal-passed" type="checkbox" disabled /><span class="td-airport">ARR</span><span class="td-waypoint td-sector">@Html.DisplayNameFor(model => model.TrafficDemand.IsPassed)</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <input type="submit" value="Save" class="btn btn-primary" />
                </div>
            </form>
        </div>
    </div>
</div>
<div id="save-gdp-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form>
                <div class="modal-header">
                    <h5 class="modal-title">Save Regulated Demand Parameter</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <p>Do you want to save the following regulated demand parameter?</p>
                    <div class="form-group form-row">
                        <label class="col-md-5 col-form-label">@Html.DisplayNameFor(model => model.GDP.Designator)</label>
                        <div class="col-md-7">
                            <input asp-for="GDP.Designator" class="form-control designator" placeholder="designator" />
                            <span asp-validation-for="GDP.Designator" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.TrafficArea)</div>
                        <div class="col-md-7 area"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.Point)</div>
                        <div class="col-md-7 point"></div>
                    </div>
                    <div class="form-group form-row td-waypoint td-sector td-lower">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.LowerFlightLevel)</div>
                        <div class="col-md-7 lower"></div>
                    </div>
                    <div class="form-group form-row td-waypoint td-sector td-upper">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.UpperFlightLevel)</div>
                        <div class="col-md-7 upper"></div>
                    </div>
                    <div class="form-group form-row td-waypoint td-radius">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.RadiusNm)</div>
                        <div class="col-md-7 radius"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.StartTime)</div>
                        <div class="col-md-7 start"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.EndTime)</div>
                        <div class="col-md-7 end"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.EndRecoveryTime)</div>
                        <div class="col-md-7 end-recovery"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.CapacityPerHr)</div>
                        <div class="col-md-7 capacity"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.CapacityRecoveryPerHr)</div>
                        <div class="col-md-7 capacity-recovery"></div>
                    </div>
                    <div class="form-group form-row td-only-adep">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.OnlyADEP)</div>
                        <div class="col-md-7 only-adep"></div>
                    </div>
                    <div class="form-group form-row td-only-ades">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.OnlyADES)</div>
                        <div class="col-md-7 only-ades"></div>
                    </div>
                    <div class="form-group form-row td-only-airlines">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.OnlyAirlines)</div>
                        <div class="col-md-7 only-airlines"></div>
                    </div>
                    <div class="form-group form-row td-adep">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.ExemptADEP)</div>
                        <div class="col-md-7 adep"></div>
                    </div>
                    <div class="form-group form-row td-ades">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.ExemptADES)</div>
                        <div class="col-md-7 ades"></div>
                    </div>
                    <div class="form-group form-row td-airlines">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.ExemptAirlines)</div>
                        <div class="col-md-7 airlines"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.IntervalMin)</div>
                        <div class="col-md-7 interval"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-5">Flight Rule</div>
                        <div class="col-md-7">
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input ifr" type="checkbox" disabled /> @Html.DisplayNameFor(model => model.GDP.IsIFR)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input vfr" type="checkbox" disabled /> @Html.DisplayNameFor(model => model.GDP.IsVFR)
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                    <input type="submit" value="Save" class="btn btn-primary" />
                </div>
            </form>
        </div>
    </div>
</div>
<div id="change-state-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form>
                <div class="modal-header">
                    <h5 class="modal-title">Change Regulated Demand State</h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.Designator)</div>
                        <div class="col-md-6 designator"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.TrafficArea)</div>
                        <div class="col-md-6 area"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.Point)</div>
                        <div class="col-md-6 point"></div>
                    </div>
                    <div class="form-group form-row td-waypoint td-sector td-lower">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.LowerFlightLevel)</div>
                        <div class="col-md-6 lower"></div>
                    </div>
                    <div class="form-group form-row td-waypoint td-sector td-upper">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.UpperFlightLevel)</div>
                        <div class="col-md-6 upper"></div>
                    </div>
                    <div class="form-group form-row td-waypoint td-radius">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.RadiusNm)</div>
                        <div class="col-md-6 radius"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.StartTime)</div>
                        <div class="col-md-6 start"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.EndTime)</div>
                        <div class="col-md-6 end"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.EndRecoveryTime)</div>
                        <div class="col-md-6 end-recovery"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.CapacityPerHr)</div>
                        <div class="col-md-6 capacity"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.CapacityRecoveryPerHr)</div>
                        <div class="col-md-6 capacity-recovery"></div>
                    </div>
                    <div class="form-group form-row td-only-adep">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.OnlyADEP)</div>
                        <div class="col-md-6 only-adep"></div>
                    </div>
                    <div class="form-group form-row td-only-ades">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.OnlyADES)</div>
                        <div class="col-md-6 only-ades"></div>
                    </div>
                    <div class="form-group form-row td-only-airlines">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.OnlyAirlines)</div>
                        <div class="col-md-6 only-airlines"></div>
                    </div>
                    <div class="form-group form-row td-adep">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.ExemptADEP)</div>
                        <div class="col-md-6 adep"></div>
                    </div>
                    <div class="form-group form-row td-ades">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.ExemptADES)</div>
                        <div class="col-md-6 ades"></div>
                    </div>
                    <div class="form-group form-row td-airlines">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.ExemptAirlines)</div>
                        <div class="col-md-6 airlines"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.IntervalMin)</div>
                        <div class="col-md-6 interval"></div>
                    </div>
                    <div class="form-group form-row">
                        <div class="col-md-6">Flight Rule</div>
                        <div class="col-md-6">
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input ifr" type="checkbox" disabled /> @Html.DisplayNameFor(model => model.GDP.IsIFR)
                                </label>
                            </div>
                            <div class="form-check form-check-inline">
                                <label class="form-check-label">
                                    <input class="form-check-input vfr" type="checkbox" disabled /> @Html.DisplayNameFor(model => model.GDP.IsVFR)
                                </label>
                            </div>
                        </div>
                    </div>
                    <div class="form-group form-row regul-row">
                        <label class="col-md-6 col-form-label">@Html.DisplayNameFor(model => model.GDP.Regul)</label>
                        <div class="col-md-6">
                            <!--<span class="regul-text" style="display: none;"></span>-->
                            <input asp-for="GDP.Regul" class="form-control regul" placeholder="Identifier of a Regulation concerning a flight" required />
                            <span asp-validation-for="GDP.Regul" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="form-group form-row regcause-row">
                        <label class="col-md-6 col-form-label">@Html.DisplayNameFor(model => model.GDP.Regcause)</label>
                        <div class="col-md-6">
                            <!--<span class="regcause-text" style="display: none;"></span>-->
                            <input asp-for="GDP.Regcause" class="form-control regcause" placeholder="The CFMU and IATA coded designators indicating the reason for a regulation" required />
                            <span asp-validation-for="GDP.Regcause" class="text-danger"></span>
                        </div>
                    </div>
                    <div class="form-group form-row comment-row">
                        <label class="col-md-6 col-form-label">@Html.DisplayNameFor(model => model.GDP.Comment)</label>
                        <div class="col-md-6">
                            <!--<span class="comment-text" style="display: none;"></span>-->
                            <input asp-for="GDP.Comment" class="form-control comment text-uppercase" placeholder="AFTN comment in free text without hyphen" />
                            <span asp-validation-for="GDP.Comment" class="text-danger"></span>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-danger mr-auto undo-btn" data-state="-1">Unsave</button>
                    <button type="button" class="btn btn-secondary cancel-btn" data-dismiss="modal">Cancel</button>
                    <input type="submit" value="Execute" class="btn btn-primary execute-btn" data-state="1" />
                </div>
            </form>
        </div>
    </div>
</div>

@section Stylesheets {
    <link rel="stylesheet" href="~/lib/jquery-ui/jquery-ui.min.css" />
    <style>
        .ui-sortable-placeholder {
            width: 600px;
            opacity: 0.1;
        }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/lib/chart.js/Chart.bundle.min.js"></script>
    <script src="~/lib/jquery-ui/jquery-ui.js"></script>
    <script src="~/js/TrafficDemand/traffic-demand.js"></script>
    <script src="~/js/GDP/gdp.js"></script>
}
