@{
    ViewData["Title"] = "CTOT Forwarding Review";
}

<h4>CTOT Forwarding Review</h4>
<div class="clearfix">
    <div class="message text-danger"></div>
    <b class="float-right last-update"></b>
</div>
<hr />
<div class="row justify-content-between">
    <div class="col-xl-8 col-lg-9">

        <div class="table-flight-wrapper" style="overflow: auto; height: 600px; ">
            <table id="table-flight" class="table table-hover">
                <thead>
                    <tr>
                        <th>
                            <label class="checkbox-inline">
                                <input type="checkbox"> <b>All</b>
                            </label>
                        </th>
                        <th>Type</th>
                        <th>ACID</th>
                        <th>ADEP</th>
                        <th>ADES</th>
                        <th>EOBT</th>
                        <th>CTOT</th>
                        <th>New CTOT</th>
                        <th>Originator</th>
                        <th>REGUL</th>
                        <th>REGCAUSE</th>
                    </tr>
                </thead>
                <tbody>
                </tbody>
            </table>
        </div>

        <div class="form-group row">
            <label for="inputCommment" class="col-sm-1 col-form-label">Comment</label>
            <div class="col-sm-11">
                <input type="text" class="form-control" id="input-comment" value="@ViewData["comment"]" placeholder="Comment to be appended in AFTN and email messages" style='text-transform:uppercase'>
            </div>
        </div>

        <button type="button" id="btn-send" class="btn btn-primary">Send email & AFTN</button>
    </div>
    <div class="col-xl-4 col-lg-3">
        <div class="card">
            <div class="card-header bg-info" style="font-weight:bold;color:#fff">
                Airspace User New CTOT Request
            </div>
            <div class="table-request-wrapper" style="overflow: auto; height: 500px; ">
                <table class="table table-hover table-info" id="table-request">
                    <thead>
                        <tr>
                            <th style="padding-left:1.5em;">ACID</th>
                            <th>REQ EOBT</th>
                            <th>REQ CTOT</th> <!-- CALCULATED BY NEW EOBT + STT  -->
                            <th style="padding-right:1.5em;">STATUS</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- <tr class="new-request">
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span class="request" >CTOT Revision Request</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span><i class="far fa-clock text-warning"></i>Request In Progress</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span class="text-success" ><i class="far fa-check-circle text-success"></i>New CTOT Delivered</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span class="text-danger"><i class="fa fa-ban text-danger"></i>Unable to Process</span></td>
                         </tr>
                         TEST
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-circle "></i> CTOT Revision Request</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-clock text-warning"></i> Request In Progress</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-check-circle text-success"></i> New CTOT Delivered</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-ban text-danger"></i> Unable To Process</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-circle "></i> CTOT Revision Request</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-clock text-warning"></i> Request In Progress</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-check-circle text-success"></i> New CTOT Delivered</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-ban text-danger"></i> Unable To Process</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-circle "></i> CTOT Revision Request</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-clock text-warning"></i> Request In Progress</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-check-circle text-success"></i> New CTOT Delivered</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-ban text-danger"></i> Unable To Process</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-circle "></i> CTOT Revision Request</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-clock text-warning"></i> Request In Progress</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-check-circle text-success"></i> New CTOT Delivered</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-ban text-danger"></i> Unable To Process</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-circle "></i> CTOT Revision Request</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-clock text-warning"></i> Request In Progress</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-check-circle text-success"></i> New CTOT Delivered</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-ban text-danger"></i> Unable To Process</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-circle "></i> CTOT Revision Request</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-clock "></i> Request In Progress</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="far fa-check-circle text-success"></i> New CTOT Delivered</span></td>
                         </tr>
                         <tr>
                             <td class="acid">THA123</td>
                             <td class="new-eobt">28 /12:12</td>
                             <td class="status"><span style="font-size:small;"><i class="fa fa-ban text-danger"></i> Unable To Process</span></td>
                         </tr>-->

                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Button trigger modal
    <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#RequestModal">
        Launch demo modal
    </button> -->
</div>
<!-- MODAL SECTION -->
<div id="RequestModal" class="modal fade" tabindex="-1" data-backdrop="static">
    <div class="modal-dialog modal-xl modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-info" style="color:#fff">
                <h5 class="modal-title">New CTOT Request Details</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>

            <div class="modal-body" style=" margin-bottom: 1em;">
                <h6 style="margin-bottom: 1.5em;">Request Date : <span class="request-date">2022-02-22 12:14 UTC</span></h6>

                <div class="table-responsive card">
                    <table class="table table-sm" id="table-request-modal">
                        <thead>
                            <tr>
                                <th>ACID</th>
                                <th>ADEP</th>
                                <th>ADES</th>
                                <th>EOBT</th>
                                <th class="new-eobt">REQ EOBT</th>
                                <th class="new-ctot">REQ CTOT</th> 
                                <th>Current CTOT</th>
                                <th>Originator</th>
                                <th>REGUL</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            @*<tr>
                        <td>ABC123</td>
                        <td>WSSS</td>
                        <td>VTBD</td>
                        <td>12:02 / 28</td>
                        <td class="new-eobt" style="font-weight:bold;">12:12 / 28</td>
                        <td>12:20 / 28</td>
                        <td>WSSSATFM</td>
                        <td>77792236</td>
                        <td>CTOT Revision Request</td>
                    </tr>*@
                        </tbody>
                    </table>
                </div>  <!--Table responsive -->
             
                <div class="originator-wrapper" style="margin-top:1em;">
                    <h6 class="og-header" style="text-decoration: underline;">Originator Details</h6>
                    <div class="originator-details">
                        <div><span class="og-name" style="font-weight: 500"></span></div>
                        <div><div class="og-label"><i class="fa fa-phone" aria-hidden="true"></i> Tel </div><span class="og-tel"></span></div>
                        <div><div class="og-label"><i class="fa fa-envelope" aria-hidden="true"></i> Email </div><span class="og-mail"></span></div>
                    </div>
                </div>
                <!-- ADD REASON FOR TERMINATE BY ATFMU 
                <div>
                    <label for="comment">Reasons</label>
                    <textarea class="form-control comment" rows="2"></textarea>
                                </div>-->

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger terminate-modal-btn request-btn"><i class="fa fa-ban fa-flip-horizontal"></i>Terminate Request</button>
                <button type="button" class="btn btn-primary confirm-modal-btn request-btn"><i class="far fa-check-circle"></i>Accept Request</button>
                @*<button type="button" class="btn btn-primary close-btn" data-dismiss="modal"><i class="far fa-check-circle"></i>Close</button>*@
            </div>

        </div>
    </div>
</div>

@section Stylesheets {
    <link rel="stylesheet" href="~/css/CTOTForwardReview/main.css" />
<style>
    .og-label {
        width: 80px;
        display: inline-block;
        font-weight: 500;
    }
</style>
}

@section Scripts {
    <script src="~/lib/moment.js"></script>
    <script src="~/js/CTOTForwardReview/main.js"></script>

}

