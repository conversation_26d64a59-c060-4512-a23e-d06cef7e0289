﻿@using FeatureAuthorize
@using PermissionParts

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>@ViewData["Title"] - ATFAS</title>
    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/lib/fontawesome/css/all.min.css" />
    <link rel="stylesheet" href="~/lib/jquery-datetimepicker/jquery.datetimepicker.min.css" />
    @*<link rel="stylesheet" href="~/lib/adminlte/plugins/overlayScrollbars/css/OverlayScrollbars.min.css"/>*@
    <link rel="stylesheet" href="~/css/site.css" />
    @RenderSection("Stylesheets", required: false)
</head>
<body>
    <header>
        <nav class="navbar navbar-expand-sm navbar-toggleable-sm navbar-dark bg-dark border-bottom box-shadow mb-3 px-xl-5">
            <div class="container-fluid">
                <a class="navbar-brand" asp-area="" asp-controller="Home" asp-action="Index">ATFAS</a>
                <button class="navbar-toggler" type="button" data-toggle="collapse" data-target=".navbar-collapse" aria-controls="navbarSupportedContent"
                        aria-expanded="false" aria-label="Toggle navigation">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="navbar-collapse collapse d-sm-inline-flex flex-sm-row-reverse">
                    <ul class="navbar-nav flex-grow-1">
                        @if (User.UserHasThisPermission(Permissions.CtotChange))
                        {
                            <li class="nav-item">
                                <a class="nav-link" asp-area="" asp-controller="TrafficDemand" asp-action="Index">Traffic Demand</a>
                            </li>

                            <li class="nav-item">
                                <div class="dropdown">
                                    <a class="nav-link dropdown-toggle" role="button" id="dropdownGdp" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        GDP
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownGdp">
                                        <a class="nav-link text-dark" asp-area="" asp-controller="GDP" asp-action="Index">GDP</a>
                                        <a class="nav-link text-dark" asp-area="" asp-controller="GDPManage" asp-action="Index">GDP Management</a>
                                        <a class="nav-link text-dark" asp-area="" asp-controller="GDPHistory" asp-action="Index">GDP History</a>
                                        <a class="nav-link text-dark" asp-area="" asp-controller="ExemptManage" asp-action="Index">Flight-Specific Exemption/Exclusion Management</a>
                                    </div>
                                </div>
                            </li>
                        }
                        @if (User.Identity.IsAuthenticated)
                        {
                            <li class="nav-item">
                                <div class="dropdown">
                                    <a class="nav-link dropdown-toggle" role="button" id="dropdownCtot" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        CTOT
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdownCtot">
                                        <a class="nav-link text-dark" asp-area="" asp-controller="CTOTDistributor" asp-action="Index">CTOT Distributor</a>
                                        @if (User.UserHasThisPermission(Permissions.CtotChange))
                                        {
                                            <a class="nav-link text-dark" asp-area="" asp-controller="CTOTForwardReview" asp-action="Index">CTOT Forwarding Review</a>
                                            <a class="nav-link text-dark" asp-area="" asp-controller="CTOTForwardReport" asp-action="Index">CTOT Forwarding Report</a>
                                        }
                                    </div>
                                </div>
                            </li>
                        }

                        @if (User.UserHasThisPermission(Permissions.CtotChange))
                        {@if (User.UserHasThisPermission(Permissions.UploadFlightSched))
                            {
                                <li class="nav-item">
                                    <a class="nav-link" asp-area="" asp-controller="UploadFlightsched" asp-action="Index">Upload Flight Schedule</a>
                                </li>
                            }
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="StaticAirspaceDesign" asp-action="Index">Static Airspaces</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="UserdefinedAirspaceDesign" asp-action="Index">User Defined Airspaces</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Capacity" asp-action="Index">Capacity</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" asp-area="" asp-controller="Adp" asp-action="Index">ADP</a>
                        </li>
                    }
                        @if (User.UserHasThisPermission(Permissions.UserRead) || User.UserHasThisPermission(Permissions.RoleRead))
                        {
                            <li class="nav-item">
                                <div class="dropdown">
                                    <a class="nav-link dropdown-toggle" role="button" id="dropdown2MenuButton" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        Users
                                    </a>
                                    <div class="dropdown-menu" aria-labelledby="dropdown2MenuButton">
                                        @if (User.UserHasThisPermission(Permissions.UserChange))
                                        {
                                            <a class="nav-link text-dark" asp-area="" asp-controller="User" asp-action="PendingUser">Pending User</a>
                                        }
                                        @if (User.UserHasThisPermission(Permissions.UserRead))
                                        {
                                            <a class="nav-link text-dark" asp-area="" asp-controller="User" asp-action="Index">All Users</a>
                                        }
                                        @if (User.UserHasThisPermission(Permissions.RoleRead))
                                        {
                                            <a class="nav-link text-dark" asp-area="" asp-controller="Role" asp-action="Index">All Roles</a>
                                        }
                                        @if (User.UserHasThisPermission(Permissions.UserChange))
                                        {
                                            <a class="nav-link text-dark" asp-area="" asp-controller="UserProfile" asp-action="Index">User Profiles</a>
                                        }
                                    </div>
                                </div>

                            </li>
                        }
                    </ul>

                </div>


                <div class="sidebarnav navbar-collapse collapse justify-content-end">
                    @if (User.UserHasThisPermission(Permissions.CtotRequest) && User.UserHasThisPermission(Permissions.CtotNotice))
                    {
                        <ul class="navbar-nav">
                            <!--CTOT NOTIFICATION-->
                            <li class="nav-item">
                                <div class="dropdown" id="dropdownNoticeWrapper" style="margin-right: 14px;">
                                    <a class="nav-link dropdown-toggle " role="button" id="dropdownNotice" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                        <div class="icon-badge-container">
                                            <i class="fas fa-bell icon-badge-icon"></i>
                                            <div class="icon-badge" style="display:none"></div>
                                        </div>
                                    </a>
                                    <div class="dropdown-menu dropdown-menu-right" aria-labelledby="dropdownNotice" style="z-index:9999">
                                        @*<a class="nav-link text-dark">CTOT Update by Superadmin</a>
                                <a class="nav-link text-dark">CTOT Update by Superadmin</a>
                                <a class="nav-link text-dark">CTOT Update by Superadmin</a>
                                <a class="nav-link text-dark">CTOT Update by Superadmin</a>*@
                                    </div>
                                </div>
                            </li>
                            @*<button type="button" class="btn btn-primary dropdown-toggle" id="dropdownMenu" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                        CTOT Notifications <span class="badge badge-light">4</span>
                    </button>
                    <div class="dropdown-menu" aria-labelledby="dropdownMenu">
                        <div class="dropdown-item" type="button">Action</div>
                        <div class="dropdown-item" type="button">Another action</div>
                        <div class="dropdown-item" type="button">Something else here</div>
                    </div>*@
                        </ul>
                    }
                    <partial name="_LoginPartial.cshtml" />
                    <!-- Side widget for Homepage-->
                    <!--End Side Widget for Homepage -->
                </div>
                @if (User.Identity.IsAuthenticated)
                {
                    <div class='navbar-custom-menu'>
                        <ul class='nav navbar-nav'>
                            <li><a class='nav-link' style='color:#fff;' data-slide='true' href='/Management' target='_blank' role='button'><i class='fas fa-cogs'></i></a></li>
                        </ul>
                    </div>
                    }
                </div>
                
</nav>
    </header>
    <div class="container-fluid">
        <main role="main" class="pb-3 px-3 px-xl-5">
            @RenderBody()
            <div id="success-toast" class="toast position-fixed ml-auto mr-auto" role="alert" data-delay="10000" style="top:4rem; left:0; right:0; font-size:1.2rem;">
                <div class="toast-header">
                    <span class="rounded mr-2 bg-success" style="width:20px; height:20px;"></span>
                    <strong class="mr-auto">Success</strong>
                    <small class="text-muted toast-time"></small>
                    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="toast-body"></div>
            </div>
            <div id="error-toast" class="toast position-fixed ml-auto mr-auto" role="alert" data-delay="10000" style="top:4rem; left:0; right:0; font-size:1.2rem;">
                <div class="toast-header">
                    <span class="rounded mr-2 bg-danger" style="width:20px; height:20px;"></span>
                    <strong class="mr-auto">Error</strong>
                    <small class="text-muted toast-time"></small>
                    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="toast-body"></div>
            </div>
            <div id="warning-toast" class="toast position-fixed ml-auto mr-auto" role="alert" data-delay="10000" style="top:4rem; left:0; right:0; font-size:1.2rem;">
                <div class="toast-header">
                    <span class="rounded mr-2 bg-warning" style="width:20px; height:20px;"></span>
                    <strong class="mr-auto">Warning</strong>
                    <small class="text-muted toast-time"></small>
                    <button type="button" class="ml-2 mb-1 close" data-dismiss="toast">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="toast-body"></div>
            </div>
        </main>
    </div>
    <footer class="border-top footer text-muted px-3 px-xl-5">
        <div class="container-fluid">
            &copy; 2020 - ATFAS - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <div id="loading-overlay" style="display:none;"></div>
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/lib/jquery-datetimepicker/jquery.datetimepicker.full.min.js"></script>
    @*<script src="~/lib/adminlte/plugins/overlayScrollbars/js/OverlayScrollbars.min.js"></script>*@
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/js/notification.js"></script>

    @RenderSection("Scripts", required: false)
</body>
</html>
