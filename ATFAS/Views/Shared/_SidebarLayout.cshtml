﻿@*@using static ATFAS.Helpers.NavigationIndicatorHelper;*@
@using FeatureAuthorize
@using PermissionParts


<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>ATFAS Configuration Management</title>

    <link rel="stylesheet" href="~/lib/bootstrap/dist/css/bootstrap.min.css" />
    <link rel="stylesheet" href="~/lib/fontawesome/css/all.min.css" />
    <link rel="stylesheet" href="~/css/site.css" />
    <link rel="stylesheet" href="~/lib/jquery-ui/jquery-ui.css" />
    <link rel="stylesheet" href="~/lib/adminlte/dist/css/adminlte.min.css" />
    <link rel="stylesheet" href="~/lib/adminlte/plugins/overlayScrollbars/css/OverlayScrollbars.min.css" />
    <link rel="stylesheet" href="~/css/shared/_SidebarLayout.css" />
    @RenderSection("Stylesheets", required: false)
</head>
<body class="hold-transition sidebar-mini layout-fixed">
    <!-- Site wrapper -->
    <div class="wrapper">
        <!-- Navbar -->
        <nav class="main-header navbar navbar-expand navbar-white navbar-light">
            <!-- Left navbar links -->
            <ul class="navbar-nav">
                <li class="nav-item">
                    <a class="nav-link" data-widget="pushmenu" href="#" role="button"><i class="fas fa-bars"></i></a>
                </li>
                <li class="nav-item d-none d-sm-inline-block">
                    <a asp-area="Management" asp-controller="Home" asp-action="Index" class="nav-link">Management</a>
                </li>
            </ul>
            <div class="sidebarnav ml-auto">
                <partial name="_LoginPartial.cshtml" />
            </div>
        </nav>
        <!-- /.navbar -->
        <!-- Main Sidebar Container -->
        <aside class="main-sidebar sidebar-dark-primary elevation-4">
            <!-- Brand Logo -->
            <a asp-area="" asp-controller="Home" asp-action="Index" class="brand-link">
                <span class="brand-text font-weight-normal">ATFAS</span>
            </a>

            <!-- Sidebar -->
            <div class="sidebar" style=" font-size: 0.9rem; font-weight: 400;">
                <!-- Sidebar Menu -->
                <nav class="mt-2">
                    <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">
                        <!-- Add icons to the links using the .nav-icon class
                        with font-awesome or any other icon font library -->
                        <li class="nav-item">
                            <a href="" class="nav-link">
                                <i class="fas fa-home"></i>
                                <p>
                                    Home
                                    <i class="right fas fa-angle-right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a asp-area="Management" asp-controller="Announcement" asp-action="Index" class="nav-link @*@Url.MakeActiveClass(" Announcement","Index")*@">
                                        <i class="fas fa-edit"></i>
                                        <p>Manage Announcement</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a asp-area="Management" asp-controller="Announcement" asp-action="Add" class="nav-link @*@Url.MakeActiveClass(" Announcement","Add"*@)">
                                        <i class="fas fa-file-alt"></i>
                                        <p>Add Announcementt</p>
                                    </a>
                                </li>
                            </ul>

                        </li>
                        <li class="nav-item">
                            <a href="" class="nav-link">
                                <i class="fas fa-cog"></i>
                                <p>
                                    Configuration
                                    <i class="right fas fa-angle-right"></i>
                                </p>
                            </a>
                            <ul class="nav nav-treeview">
                                <li class="nav-item">
                                    <a asp-area="Management" asp-controller="Configuration" asp-action="Airport" class="nav-link">
                                        <i class="fas fa-plane-departure"></i>
                                        <p>Airport Configuration</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a asp-area="Management" asp-controller="Configuration" asp-action="Ctot" class="nav-link">
                                        <i class="fas fa-clock"></i>
                                        <p>CTOT Configuration</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a asp-area="Management" asp-controller="Configuration" asp-action="Atfmu" class="nav-link">
                                        <i class="fa fa-map-marker"></i>
                                        <p>ATFMU Configuration</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a asp-area="Management" asp-controller="Configuration" asp-action="PointofContact" class="nav-link">
                                        <i class="fa fa-user"></i>
                                        <p>POC Configuration</p>
                                    </a>
                                </li>
                                <li class="nav-item">
                                    <a asp-area="Management" asp-controller="Configuration" asp-action="Resources" class="nav-link">
                                        <i class="fas fa-file-alt"></i>
                                        <p>References Configuration</p>
                                    </a>
                                </li>
                            </ul>

                        </li>
                       

                    </ul>
                </nav>
                <!-- /.sidebar-menu -->
                @*<nav class="mt-2">
                        <ul class="nav nav-pills nav-sidebar flex-column" data-widget="treeview" role="menu" data-accordion="false">

                            <li class="nav-header">Home Management</li>
                            <li class="nav-item">
                                <a class="nav-link">
                                    <i class="nav-icon fas fa-bullhorn"></i>
                                    <p>
                                        News & Announncement
                                        <i class="fas fa-angle-left right"></i>
                                    </p>
                                </a>
                                <ul class="nav nav-treeview">
                                    <li class="nav-item">
                                        <a class="nav-link" asp-area="" asp-controller="Management" asp-action="Announcement">
                                            <i class="fas fa-file-alt"></i>
                                            <p>Announcement Management</p>
                                        </a>
                                    </li>
                                    <li class="nav-item">
                                        <a class="nav-link" asp-area="Management" asp-controller="Announcement" asp-action="Add">
                                            <i class="fas fa-file-alt"></i>
                                            <p>Add Announncement</p>
                                        </a>
                                    </li>
                                </ul>
                            </li>
                        </ul>
                    </nav>*@
                <!-- /.sidebar-menu -->
            </div>
            <!-- /.sidebar -->
        </aside>

        <!-- Content Wrapper. Contains page content -->
        <div class="content-wrapper bg-white">
            <section class="content-header">
                <div class="container-fluid">
                    <div class="row mb-2">
                        <div class="col">
                            <h5> <i class="fas fa-cogs" style="margin: 0.5em;"></i> ATFAS Configuration Management</h5>
                        </div>
                    </div>
                    <hr />
                </div><!-- /.container-fluid -->
            </section>
            <section class="content">
                <div class="container-fluid container-limited">
                    @RenderBody()
                </div>
            </section>
        </div>
        <!-- /.content-wrapper -->

    </div>
    <footer class="border-top  main-footer text-muted " style="padding: 0 1em !important;">
        <div class="container-fluid" style="line-height: 60px;">
            &copy; 2020 - ATFAS - <a asp-area="" asp-controller="Home" asp-action="Privacy">Privacy</a>
        </div>
    </footer>
    <div id="loading-overlay" style="display:none;"></div>
    <!-- ./wrapper -->
    <script src="~/lib/jquery/dist/jquery.min.js"></script>
    <script src="~/lib/bootstrap/dist/js/bootstrap.bundle.min.js"></script>
    <script src="~/js/site.js" asp-append-version="true"></script>
    <script src="~/lib/jquery-ui/jquery-ui.js"></script>
    <script src="~/lib/moment.js"></script>
    <script src="~/lib/adminlte/dist/js/adminlte.js"></script>
    <script src="~/lib/adminlte/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js"></script>
    <script src="~/lib/shared/_SidebarLayout.js"></script>
    @RenderSection("Scripts", required: false)
</body>
</html>

