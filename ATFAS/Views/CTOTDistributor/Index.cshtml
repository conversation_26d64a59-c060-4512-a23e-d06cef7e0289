﻿@{
    ViewData["Title"] = "CTOT Distributor";
}

<h4>CTOT Information from Bangkok ATFMU</h4>
<div class="clearfix">
    <div class="message text-danger"></div>
    <b class="float-right last-update"></b>
</div>
<hr />
<div class="row justify-content-between">
    <!-- <button type="button" class="btn btn-secondary ml-3 px-0 py-1" style="width:30px;" data-toggle="collapse" href="#chart-collapse" data-hover="tooltip" data-placement="right" data-trigger="hover" title="hide / show"><i class="fas fa-exchange-alt fa-lg"></i></button>-->
    <!--Fliter Options-->
    <form id="FormFilter" style="padding: 0.5rem 1rem" class="search-ctot">
        <div class="form-row">
            <div class="form-group">
                <div class="form-check form-check-all" style="margin-bottom: 0.75rem;">
                    <input class="form-check-input" type="radio" name="filterradio" id="allRadio" value="all" checked>
                    <label class="form-check-label" for="allRadio">
                        All Flights
                    </label>
                </div>
                <div class="form-check form-check-inline" style=" margin-right: 1.75rem;">
                    <input class="form-check-input input-radio" type="radio" name="filterradio" id="acidRadio" value="acid">
                    <label class="form-check-label" for="acidRadio">
                        ACID
                    </label>
                </div>
                <div class="form-check form-check-inline" style=" margin-right: 1.75rem;">
                    <input class="form-check-input input-radio " type="radio" name="filterradio" id="adepRadio" value="adep">
                    <label class="form-check-label" for="adepRadio">
                        ADEP
                    </label>
                </div>
                <div class="form-check form-check-inline" style=" margin-right: 1.75rem;">
                    <input class="form-check-input input-radio" type="radio" name="filterradio" id="adesRadio" value="ades">
                    <label class="form-check-label" for="adesRadio">
                        ADES
                    </label>
                </div>
            </div>

            <div class="form-group col-auto" style="display:inline-block;">
                <label class="control-label" for="CtotPoint"></label>
                <input id="CtotPoint" class="form-control text-uppercase ctot-input" placeholder="values separated by comma (,)" disabled />
                <span class="text-danger ctot-point-error"></span>
            </div>

            <div class="form-group col-auto">
                <button type="button" class="btn btn-primary mb-2 ctot-input filter-btn" id="SubmitSearch" disabled>Search</button>
            </div>

        </div>
    </form>

    <div class="form-group col-auto">
        <button type="button" class="btn btn-outline-primary mb-2 filter-btn-ack search-ctot" id="SubmitAck">Acknowledge</button>
    </div>
</div>

<div class="table-wrapper search-ctot">
    <table class="table td-table" id="CtotTable" data-row-style="rowStyle"  data-height="600">
        <thead>
            <tr class="tr-title">
                <th data-formatter="runningFormatter">#</th>
                <th data-field="flight.callsign" data-cell-style="callsignCellStyle" data-sortable="true">ACID</th>
                <th data-field="flight.airportDeparture" data-sortable="true">ADEP</th>
                <th data-field="flight.airportArrival" data-sortable="true">ADES </th>
                <th data-field="flight.eobt" data-formatter="dateTimeFormatter" data-sortable="true">EOBT (FPL) <br /></th>
                <th data-field="flight" data-formatter="ctotFormatter" data-events="eventTableBtn" data-sortable="true" class="td-ctot">CTOT <br /></th>
                @*<th class="td-waypoint">CTO </th>
                    <th class="td-sector" style="display:none;">INB </th>
                    <th class="td-sector" style="display:none;">OUTB </th>*@
                <th data-field="flight.cldt" data-formatter="dateTimeFormatter" data-sortable="true">CLDT <br /></th>
                <th data-field="measureString" data-sortable="true" class="td-measure">ATFM Measure</th>
                <th data-field="comment" data-formatter="commentFormatter" data-sortable="true" class="td-comment">Reason</th>
            </tr>
        </thead>

    </table>
</div>
<!-- MODAL -->
<div id="alert-modal" class="modal fade" tabindex="-1" data-backdrop="static" style="z-index: 1065 !important;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header" style="border-bottom: none;">
            </div>
            <div class="modal-body" style=" margin: 0em 2em;">
                <div class="info-box">
                    <div class="info-box-icon">
                    </div>
                    <div class="info-box-content" style="padding:0em 2em;">
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary confirm-modal-btn" data-dismiss="modal">OK</button>
            </div>
        </div>
    </div>
</div>
<div id="confirm-modal" class="modal fade" tabindex="-1" data-backdrop="static" style="z-index: 1055 !important;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary confirm-modal-cancel-btn position-absolute" data-dismiss="modal" style="left: 1em;">Cancel</button>
                <button type="button" class="btn btn-primary confirm-modal-btn">Confirm</button>
            </div>
        </div>
    </div>
</div>
<div id="edit-flight-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body edit-flight-body" style="padding:2em;">
                <div class="form-group row">
                    <label for="EobtEdt" class="col-sm-3 col-form-label control-label font-weight-bold">New OBT</label>
                    <div class="col-sm-9">
                        <input class="form-control" id="EobtFlightEdt" type="datetime" value="@DateTime.UtcNow.ToString("yyyy-MM-dd / HH:mm")" autocomplete="off" />
                    </div>
                </div>
                <span class="eobt-error text-danger"></span>
                <hr />
                <div class="flight-info">
                    <h6><u>Flight Information</u></h6>

                    <div class="row">
                        <div class="col-md-6"><span class="text-500">Departure</span><span class="dep-info"></span></div>
                        <div class="col-md-6"><span class="text-500">Arrival</span><span class="arr-info"></span></div>
                    </div>

                    <div class="row">
                        <div class="col-md-6"><span class="text-500 eobt-title">EOBT</span><span class="eobt-info"></span></div>
                        <div class="col-md-6"><span class="text-500 etot-title">ETOT</span><span class="etot-info"></span></div>
                    </div>

                    <div class="row">
                        <div class="col-md-6"><span class="text-500 ctot-title">CTOT</span><span class="ctot-info"></span></div>
                        <div class="col-md-6"><span class="text-500 cldt-title">CLDT</span><span class="cldt-info"></span></div>
                    </div>
                </div>
                <hr />
                <div class="form-group">
                    <label for="comment">Reasons</label>
                    <textarea class="form-control comment" id="Comment" rows="3"></textarea>
                    <span class="comment-error text-danger"></span>
                </div>
            </div>
            <div class="modal-footer edit-flight-btn">
                <button type="button" class="btn btn-secondary position-absolute" data-dismiss="modal" style="left: 1em;">Cancel</button>
                <button type="button" class="btn btn-primary confirm-edit-flight-btn" style="padding-left:1.5rem;padding-right:1.5rem;">OK</button>
                @*<a class="btn btn-primary confirm-edit-flight-btn" data-toggle="modal" href="#confirm-modal" style="padding-left:1.5rem;padding-right:1.5rem;">OK</a>*@
            </div>
        </div>
    </div>
</div>
<div id="ctot-modal" class="modal fade" tabindex="-1" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                @*<button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>*@
            </div>

            <div class="modal-body">
                @*<h6>New EOBT : 15 / 10:12</h6>
                    <hr />*@
                <div class="alert alert-danger expire-alert" role="alert">CTOT Expires In </div>
                <div class="alert alert-info ctot-alert" role="alert"></div>
                <form class="form ctot-confirm-form">
                    <span class="text-danger ctot-radio-error"></span>
                    <hr />
                </form>
                <div class="form-group">
                    <label for="comment">Reasons</label>
                    <textarea class="form-control comment" rows="2"></textarea>
                    <span class="comment-error text-danger"></span>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary position-absolute back-modal-btn" data-dismiss="modal" style="left: 1em;">Back</button>
                <button type="button" class="btn btn-danger cancel-ctot-btn">Cancel Request</button>
                <button type="button" class="btn btn-primary confirm-modal-btn">Confirm new CTOT</button>
            </div>

        </div>
    </div>
</div>

@section Stylesheets {
    <link rel="stylesheet" href="~/lib/jquery-ui/jquery-ui.min.css" />
    <link rel="stylesheet" href="~/lib/bootstrap-table/dist/bootstrap-table.css" />
    <style>
        .search-ctot {
            display: none;
        }

        #CtotPoint {
            width: 250px;
        }

            #CtotPoint::-webkit-input-placeholder {
                text-transform: lowercase;
            }

        .ui-sortable-placeholder {
            width: 600px;
            opacity: 0.1;
        }

        .filter-btn {
            margin-top: 1.5em;
            margin-left: 1em;
        }

        .filter-btn-ack {
            margin-top: 2em;
        }

        .expired-time {
            font-size: smaller;
            color: darkred;
            font-style: italic;
            font-weight: 400;
        }

        .td-table-title, .td-table-subtitle {
            font-weight: 600;
            text-align: center;
        }

        .no-padding {
            padding: 0;
        }

        .text-500 {
            font-weight: 500;
            padding-right: 1em;
        }

        /*.tr-title > th {
            border-bottom: none !important; 2px solid #dee2e6;
        }*/
        .table-bordered td, .table-bordered th {
            border: none;
        }

        .tr-filter > th {
            padding: 0 .05rem .75rem 0.5rem !important;
            border-top: none !important;
        }

        td.td-comment > span {
            white-space: nowrap;
            width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
        }

            td.td-comment > span.show {
                white-space: break-spaces;
                overflow: unset;
            }

            td.td-comment > span > i.comment-append {
                margin-left: 2px;
                font-weight :normal !important;
                color: #000 !important;
            }

        .td-ctot {
            font-weight: bold;
            color: #1e6fc7;
        }
        /* td.td-comment > span.show > span {
                display: block;
            }*/
        .row-focus {
            background-color: aquamarine;
        }
        #alert-modal .info-box {
            border-radius: 0.25rem;
            display: -ms-flexbox;
            display: flex;
            min-height: 80px;
            position: relative;
            width: 100%;
        }
        #alert-modal .info-box-icon {
            border-radius: 0.25rem;
            -ms-flex-align: center;
            align-items: center;
            display: -ms-flexbox;
            display: flex;
            font-size: 3.5rem;
            -ms-flex-pack: center;
            justify-content: center;
            text-align: center;
            width: 70px;
        }
        .status-submit {
            font-weight: 500;
        }

       .req-info-title{
       font-size: small;
       margin-bottom: 5px;
       }

        .req-info-time {
            margin-bottom: 5px;        
        }

            .req-info-time.info-obt, .req-info-title.title-obt {
                color: #007bff;
        }

            .req-info-time.info-obt {
         font-weight: 600;
        }
        /* .ctot-point-error {
            border-color: red !important;
        }*/
    </style>
}
@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}

    <script src="~/lib/jquery-ui/jquery-ui.js"></script>
    <script src="~/lib/bootstrap-table/dist/bootstrap-table.min.js"></script>
    <script src="~/lib/moment.js"></script>
    <script src="~/js/TrafficDemand/traffic-demand.js"></script>
    <script src="~/js/CTOT/main.js"></script>
}