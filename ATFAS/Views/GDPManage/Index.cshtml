﻿@model ATFAS.ViewModels.GDPInfo;

@{
    ViewData["Title"] = "GDP Information Management";
}

<h4>Ground Delay Program (GDP) Information Management</h4>
<div class="clearfix">
    <a class="float-left" asp-controller="GDP">Create GDP</a><span>&nbsp;|&nbsp;</span><a asp-controller="ExemptManage">Manage Flight-Specific Exemption</a>
    <b id="td-update" class="float-right" style="display:none;">Last Updated: <span class="td-update-time"></span></b>
</div>
<hr />
<div class="row">
    <!-- <button type="button" class="btn btn-secondary ml-3 px-0 py-1" style="width:30px;" data-toggle="collapse" href="#chart-collapse" data-hover="tooltip" data-placement="right" data-trigger="hover" title="hide / show"><i class="fas fa-exchange-alt fa-lg"></i></button>-->
    <div class="search-gdp">
        <!--Fliter Options-->
        <form id="FormFilter">
            <div class="form-row">
                <div class="form-group col-auto">
                    <label asp-for="GDP.TrafficAreaId" class="control-label" for="TrafficAreaId"></label>
                    <select asp-for="GDP.TrafficAreaId" id="TrafficAreaId" class="form-control" asp-items="ViewBag.TrafficAreaId">
                        <option disabled selected>--- SELECT ---</option>
                    </select>
                </div>
                <div class="form-group col-auto">
                    <label class="control-label" for="Point"> @Html.DisplayNameFor(model => model.TrafficDemand.Point)</label>
                    <input id="Point" class="form-control text-uppercase" />
                </div>
                <div class="form-group col-auto">
                    <label asp-for="TrafficDemand.StartTime" class="control-label" for="StartTimeFilter">Start</label>
                    @*<input asp-for="TrafficDemand.StartTime" id="StartTimeFilter" class="form-control" type="datetime" value="@DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm")" data-date="" data-date-format="DD MMMM YYYY" autocomplete="off" />*@
                    <input asp-for="TrafficDemand.StartTime" id="StartTimeFilter" class="form-control" type="datetime" data-date="" data-date-format="DD MMMM YYYY" autocomplete="off" />
                    <span asp-validation-for="TrafficDemand.StartTime" class="text-danger"></span>
                </div>
                <div class="form-group col-auto">
                    <label class="control-label" for="EndTimeFilter">End</label>
                    <input id="EndTimeFilter" class="form-control" type="datetime" max="@DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm")" autocomplete="off">
                    <span class="text-danger end-time-err" style="display:none;">end time less than start time</span>
                </div>
                <!--timeline options-->
                <div class="form-group col-auto">
                    <label asp-for="TrafficDemand.Designator" class="control-label"></label>
                    <select id="measure-designator-filter" class="form-control">
                        <option class="executed" value="-1">&lt;Executed measure designator&gt;</option>
                    </select>
                </div>
                <div class="form-group col-auto">
                    <label asp-for="TrafficDemand.IntervalMin" class="control-label measure-param" for="IntervalMinFilter"></label>
                    <input asp-for="TrafficDemand.IntervalMin" id="IntervalMinFilter" class="form-control" placeholder="[1-1440]" value="20" />
                    <span asp-validation-for="TrafficDemand.IntervalMin" class="text-danger"></span>
                </div>
                <div class="form-group col-auto" style=" margin-top: 1.7em; margin-left: 1em;">
                    @*<div class="form-row ">*@
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="CheckGraph">
                        <label class="form-check-label" for="CheckGraph">
                            Graph
                        </label>
                    </div>
                    @*</div>*@
                    @*<div class="form-row">*@
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="CheckTable">
                        <label class="form-check-label" for="CheckTable">
                            Timeline
                        </label>
                    </div>
                    @*</div>*@
                </div>

                <div class="form-group col-auto">
                    <button type="button" class="btn btn-primary mb-2 filter-btn" id="SubmitAdd">Add</button>
                </div>
                <button type="button" class="btn btn-link info-gdp" style="display:none;"><i class="far fa-question-circle gdp-info-icon"></i></button>
            </div>
        </form>
    </div>
</div>
<hr />

<div class="row">
    <button type="button" class="btn btn-secondary ml-3 px-0 py-1" style="width:30px; display:none;" data-toggle="collapse" href="#chart-collapse" data-hover="tooltip" data-placement="right" data-trigger="hover" title="hide / show chart" id="HideChartBtn"><i class="fas fa-exchange-alt fa-lg"></i></button>
    <div class="col-md-auto mgm-chart collapse show" id="chart-collapse" style="max-width:650px;">
        <!-- gdp chart -->
        <div id="td-chart-container" class="col-md">
            <div class="float-left mr-lg-3 mb-3" style="position:relative;  width:600px; display:none;">
                <canvas class="td-chart"></canvas>
                <span class="td-chart-warning badge badge-pill badge-warning position-absolute" style="top:8px; left:7px; display:none;">Not update</span>
                <div class="td-chart-btn position-absolute" style="top:0; right:0;">
                    <button type="button" class="td-chart-remove close" data-toggle="tooltip" data-trigger="hover" title="remove">
                        <span>&times;</span>
                    </button>
                    @*<button type="button" class="td-chart-table close p-0 mr-2 permission-btn" data-toggle="tooltip" data-trigger="hover" title="display table" style="display:none;">
                            <i class="fa fa-table fa-xs text-danger"></i>
                        </button>*@
                    @*<button type="button" class="td-chart-change close p-0 mr-2 permission-btn" data-toggle="tooltip" data-trigger="hover" title="change state" style="display:none;">
                            <i class="fas fa-exchange-alt fa-xs"></i>
                        </button>*@
                    <button type="button" class="td-chart-export close p-0 mr-2" data-toggle="tooltip" data-trigger="hover" title="export">
                        <i class="fas fa-share-square fa-xs"></i>
                    </button>
                    <button type="button" class="td-chart-edit close p-0 mr-2 permission-btn" data-toggle="tooltip" data-trigger="hover" title="edit gdp">
                        <i class="fas fa-edit fa-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div id="timeline-collapse" class="col-md mgm-table collapse show" style="width: calc(100% - 50px);">
        <!-- gdp table -->
        <div id="td-table-container" class="row">
            <div class="col-md-auto float-left mr-lg-3 mb-3" style="display:none;">
                <div class="td-table-header" style="margin-top:1.5em;margin-bottom:1em;">
                    <div class="td-table-title" style=" font-size: small;">Main Title</div>
                    <div class="td-table-subtitle" style=" font-size: smaller;">Table Title</div>
                </div>
                <button type="button" class="td-acknowledge btn btn-outline-primary btn-sm">Acknowledge</button>
                <input class="td-table-filter form-control form-control-sm text-uppercase" type="text" placeholder="filter callsign...">

                <table class="table table-borderless td-table">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">ACID</th>
                            <th scope="col">TOT</th>
                            <th scope="col" class="td-waypoint" style="display:none;">TO</th>
                            <th scope="col" class="td-sector" style="display:none;">INB</th>
                            <th scope="col" class="td-sector" style="display:none;">OUTB</th>
                            <th scope="col">LDT</th>
                            <th scope="col" class="td-new-eobt">New OBT</th>
                            <th scope="col" class="td-status">Status</th>
                            <th scope="col" class="td-edit"></th>
                            <th scope="col" class="td-comment">Reason</th>
                            @*<th scope="col" data-formatter="runningFormatter">#</th>
                                <th scope="col" data-field="flight.callsign">ACID</th>
                                <th scope="col" data-field="totStr">TOT</th>
                                <th scope="col" class="td-waypoint" style="display:none;" data-field="toStr">TO</th>
                                <th scope="col" class="td-sector" style="display:none;" data-field="inbStr">INB</th>
                                <th scope="col" class="td-sector" style="display:none;" data-field="outbStr">OUTB</th>
                                <th scope="col" data-field="ldtStr">LDT</th>
                                <th scope="col" class="td-new-eobt" data-field="newEobt">New OBT</th>
                                <th scope="col" class="td-status">Status</th>
                                <th scope="col" class="td-edit"></th>
                                <th scope="col" class="td-comment">Comment</th>*@
                        </tr>
                    </thead>
                    <tbody>
                    </tbody>
                </table>

                <span class="td-table-warning badge badge-pill badge-warning position-absolute" style="top:8px; left:7px; display:none;">Not update</span>
                <div class="td-table-btn position-absolute" style="top:0; right:0;">
                    <button type="button" class="td-table-remove close" data-toggle="tooltip" data-trigger="hover" title="remove">
                        @*<i class="fa fa-eye-slash fa-xs text-danger" aria-hidden="true"></i>*@
                        <span>&times;</span>
                    </button>
                    @*<button type="button" class="td-table-hide close p-0 mr-2 permission-btn" data-toggle="tooltip" data-trigger="hover" title="hide">
                            <i class="fa fa-eye-slash" aria-hidden="true"></i>
                        </button>*@
                    @*<button type="button" class="td-table-change close p-0 mr-2 permission-btn" data-toggle="tooltip" data-trigger="hover" title="change state" style="display:none;">
                            <i class="fas fa-exchange-alt fa-xs"></i>
                        </button>*@
                    <button type="button" class="td-table-export close p-0 mr-2" data-toggle="tooltip" data-trigger="hover" title="export">
                        <i class="fas fa-share-square fa-xs"></i>
                    </button>
                    <button type="button" class="td-table-edit close p-0 mr-2 permission-btn" data-toggle="tooltip" data-trigger="hover" title="edit gdp">
                        <i class="fas fa-edit fa-xs"></i>
                    </button>

                </div>
            </div>
        </div>

    </div>
    <button type="button" class="btn btn-primary ml-3 px-0 py-1" style="width:30px; display:none;" data-toggle="collapse" href="#timeline-collapse" data-hover="tooltip" data-placement="right" data-trigger="hover" title="hide / show timeline" id="HideTimelineBtn"><i class="fas fa-exchange-alt fa-lg"></i></button>
</div>

<!--// Modal // -->
<div id="td-modal" class="modal fade" tabindex="-1" style="z-index:1052 !important;">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <table class="table table-borderless table-hover">
                    <thead>
                        <tr>
                            <th scope="col">#</th>
                            <th scope="col">ACID</th>
                            <th scope="col">ADEP</th>
                            <th scope="col">ADES</th>
                            <th scope="col">OBT</th>
                            <th scope="col">TOT</th>
                            <th scope="col" class="td-waypoint" style="display:none;">TO</th>
                            <th scope="col" class="td-sector" style="display:none;">INB</th>
                            <th scope="col" class="td-sector" style="display:none;">OUTB</th>
                            <th scope="col">LDT</th>
                        </tr>
                        @*<tr>
                                <th scope="col" data-formatter="runningFormatter">#</th>
                                <th scope="col" data-formatter="flight.callsign">ACID</th>
                                <th scope="col" data-formatter="flight.airportDeparture">ADEP</th>
                                <th scope="col" data-formatter="flight.airportArrival">ADES</th>
                                <th scope="col" data-formatter="obtStr">OBT</th>
                                <th scope="col" data-formatter="totStr">TOT</th>
                                <th scope="col" class="td-waypoint" style="display:none;" data-formatter="toStr">TO</th>
                                <th scope="col" class="td-sector" style="display:none;" data-formatter="inbStr">INB</th>
                                <th scope="col" class="td-sector" style="display:none;" data-formatter="outbStr">OUTB</th>
                                <th scope="col" data-formatter="ldtStr">LDT</th>
                            </tr>*@
                    </thead>
                    <tbody></tbody>
                </table>
            </div>
        </div>
    </div>
</div>
<div id="confirm-modal" class="modal fade" tabindex="-1" data-backdrop="static" style="z-index: 1055 !important;">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <p></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary confirm-modal-cancel-btn position-absolute" data-dismiss="modal" style="left: 1em;">Cancel</button>
                <button type="button" class="btn btn-primary confirm-modal-btn">Confirm</button>
            </div>
        </div>
    </div>
</div>

<div id="edit-flight-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body edit-flight-body" style="padding:2em;">
                <div class="form-group row form-group-new-eobt">
                    <label for="EobtEdt" class="col-sm-3 col-form-label control-label font-weight-bold">New OBT</label>
                    <div class="col-sm-9">
                        <input class="form-control" id="EobtFlightEdt" type="datetime" value="@DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm")" autocomplete="off" />
                    </div>
                </div>
                <span class="eobt-error text-danger form-group-new-eobt"></span>
                <hr />
                <div class="flight-info">
                    <h6><u>Flight Information</u></h6>

                    <div class="row">
                        <div class="col-md-6"><span class="text-500">Departure</span><span class="dep-info"></span></div>
                        <div class="col-md-6"><span class="text-500">Arrival</span><span class="arr-info"></span></div>
                    </div>

                    <div class="row">
                        <div class="col-md-6"><span class="text-500">EOBT</span><span class="eobt-info"></span></div>
                        <div class="col-md-6"><span class="text-500">EIBT</span><span class="eibt-info"></span></div>
                    </div>
                    <div class="row">
                        <div class="col-md-6"><span class="text-500">ETOT</span><span class="etot-info"></span></div>
                        <div class="col-md-6"><span class="text-500">ELDT</span><span class="eldt-info"></span></div>
                    </div>
                    <div class="row">
                        <div class="col-md-6"><span class="text-500">CTOT</span><span class="ctot-info"></span></div>
                        <div class="col-md-6"><span class="text-500">CLDT</span><span class="cldt-info"></span></div>
                    </div>
                </div>
                <hr />
                <div class="form-group">
                    <label for="comment">Reasons</label>
                    <textarea class="form-control comment" id="Comment" rows="3"></textarea>
                    <span class="comment-error text-danger"></span>
                </div>
            </div>
            <div class="modal-footer edit-flight-btn">
             @*    <button type="button" class="btn btn-danger position-absolute remove-flight-btn" style="left: 1em;" removestr="exclude">Exclude</button>
                <button type="button" class="btn btn-outline-danger position-absolute remove-flight-btn" style="left: 6em;" removestr="exempt">Exempt</button> *@
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                @*<a class="btn btn-primary confirm-edit-flight-btn" data-toggle="modal" href="#confirm-modal">Change</a>*@
                <button type="button" class="btn btn-primary confirm-edit-flight-btn">Change</button>

            </div>
        </div>
    </div>
</div>
<div id="edit-gdp-modal" class="modal fade" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form id="EditGdpForm">
                <div asp-validation-summary="ModelOnly" class="text-danger"></div>
                <div class="modal-header">
                    <h5 class="modal-title">Edit GDP Measure Parameter </h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div class="container-fluid">
                        <div class="row">
                            <div class="col-md">
                                <div class="alert alert-danger" role="alert" style="display:none;">
                                    error
                                </div>
                                <p>Do you want to edit the following GDP measure parameter?</p>
                                <input type="hidden" id="GdpId" name="gdpId" value="0">
                                <div class="form-group form-row">
                                    <label class="col-md-5 col-form-label">@Html.DisplayNameFor(model => model.GDP.Designator)</label>
                                    <div class="col-md-7">
                                        <input asp-for="GDP.Designator" class="form-control edit-modal-designator" id="DesignatorEdt" />
                                        <span asp-validation-for="GDP.Designator" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.TrafficArea)</div>
                                    <div class="col-md-7 edit-modal-area">
                                        <select asp-for="TrafficDemand.TrafficAreaId" id="TrafficAreaIdEdt" class="form-control" asp-items="ViewBag.TrafficAreaId"></select>
                                    </div>
                                </div>
                                <div class="form-group form-row">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.Point)</div>
                                    <div class="col-md-7 edit-modal-point">
                                        <input asp-for="TrafficDemand.Point" id="PointEdt" class="form-control text-uppercase" />
                                        <span asp-validation-for="TrafficDemand.Point" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="form-group form-row td-waypoint td-sector td-lower">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.LowerFlightLevel)</div>
                                    <div class="col-md-7 edit-modal-lower">
                                        <input asp-for="TrafficDemand.LowerFlightLevel" id="LowerFlightLevelEdt" class="form-control" placeholder="[0-460]" />
                                        <span asp-validation-for="TrafficDemand.LowerFlightLevel" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row td-waypoint td-sector td-upper">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.UpperFlightLevel)</div>
                                    <div class="col-md-7 edit-modal-upper">
                                        <input asp-for="TrafficDemand.UpperFlightLevel" id="UpperFlightLevelEdt" class="form-control" placeholder="[0-460]" />
                                        <span asp-validation-for="TrafficDemand.UpperFlightLevel" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row td-waypoint td-radius">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.TrafficDemand.RadiusNm)</div>
                                    <div class="col-md-7 edit-modal-radius">
                                        <input asp-for="TrafficDemand.RadiusNm" id="RadiusNmEdt" class="form-control" placeholder="[1-100]" />
                                        <span asp-validation-for="TrafficDemand.RadiusNm" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="form-group form-row measure-param">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.CapacityRecoveryPerHr)</div>
                                    <div class="col-md-7 edit-modal-capacity-recovery">
                                        <input asp-for="GDP.CapacityRecoveryPerHr" id="CapacityRecoveryPerHrEdt" class="form-control" placeholder="[0-120]" />
                                        <span asp-validation-for="GDP.CapacityRecoveryPerHr" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row measure-param edit-modal-event-capacity-row">
                                    <div class="col-md-5">
                                        <label class="control-label" for="event-capacity-select">Event Capacity</label>
                                    </div>
                                    <div class="col-md-7 edit-modal-event-capacity">
                                        <select class="form-control" id="event-capacity-select" name="event-capacity">
                                            <option value="-1">&lt;Saved event capacity&gt;</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group form-row td-specify">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.StartTime)</div>
                                    <div class="col-md-7 edit-modal-start">
                                        <input asp-for="GDP.StartTime" id="StartTimeEdt" class="form-control" type="datetime" autocomplete="off" />
                                        <span asp-validation-for="GDP.StartTime" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row td-specify">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.EndTime)</div>
                                    <div class="col-md-7 edit-modal-end">
                                        <input asp-for="GDP.EndTime" id="EndTimeEdt" class="form-control" type="datetime" autocomplete="off" />
                                        <span asp-validation-for="GDP.EndTime" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row td-specify">
                                    <div class="col-md-5">@Html.DisplayNameFor(model => model.GDP.EndRecoveryTime)</div>
                                    <div class="col-md-7 edit-modal-end">
                                        <input asp-for="GDP.EndRecoveryTime" id="EndRecoveryTimeEdt" class="form-control" type="datetime" autocomplete="off" />
                                        <span asp-validation-for="GDP.EndRecoveryTime" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="form-group form-row measure-param">
                                    <div class="col-md-5">
                                        <label asp-for="GDP.CapacityPerHr" class="control-label" for="CapacityPerHr"></label>
                                    </div>
                                    <div class="col-md-7 edit-modal-capacity">
                                        <input asp-for="GDP.CapacityPerHr" id="CapacityPerHrEdt" class="form-control" placeholder="[0-120]" />
                                        <span asp-validation-for="GDP.CapacityPerHr" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row measure-param">
                                    <div class="col-md-5">
                                        <label asp-for="GDP.ExemptADEP" class="control-label" for="ExemptADEP"></label>
                                    </div>
                                    <div class="col-md-7 edit-modal-exempt-adep">
                                        <input asp-for="GDP.ExemptADEP" id="ExemptADEPEdt" class="form-control" placeholder="ICAO airport codes separated by comma (,)">
                                        <span asp-validation-for="GDP.ExemptADEP" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row measure-param">
                                    <div class="col-md-5">
                                        <label asp-for="GDP.ExemptADES" class="control-label" for="ExemptADES"></label>
                                    </div>
                                    <div class="col-md-7 edit-modal-exempt-ades">
                                        <input asp-for="GDP.ExemptADES" id="ExemptADESEdt" class="form-control" placeholder="ICAO airport codes separated by comma (,)">
                                        <span asp-validation-for="GDP.ExemptADES" class="text-danger"></span>
                                    </div>
                                </div>
                                <div class="form-group form-row measure-param">
                                    <div class="col-md-5">
                                        <label asp-for="GDP.ExemptAirlines" class="control-label" for="ExemptAirlines"></label>
                                    </div>
                                    <div class="col-md-7 edit-modal-exempt-airline">
                                        <input asp-for="GDP.ExemptAirlines" id="ExemptAirlinesEdt" class="form-control" placeholder="ICAO airline codes separated by comma (,)">
                                        <span asp-validation-for="GDP.ExemptAirlines" class="text-danger"></span>
                                    </div>
                                </div>

                                <div class="gdp-edit-btn-group">
                                    @*<hr />*@
                                    <button type="button" class="btn btn-danger position-absolute remove-gdp" style="left: 1em;">Cancel GDP</button>
                                    @*<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>*@
                                    <button type="button" class="btn btn-info preview-gdp">Preview</button>
                                    @*<input type="button" value="Preview" class="btn btn-info preview-gdp" />*@
                                </div>
                            </div>
                            <div class="col-md-6 preview-container" style="display:none;">
                                <canvas class="td-chart-preview"></canvas>
                                <div class="gdp-chart-btn-group">
                                    <button type="button" class="btn btn-danger position-absolute remove-gdp" style="left: 1em;">Cancel GDP</button>
                                    <button type="button" class="btn btn-outline-info edit-form" style="margin-right: 0.5em;">Edit</button>
                                    @*<button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>*@
                                    <input type="submit" value="Save" class="btn btn-primary" />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

            </form>
        </div>
    </div>
</div>
<div id="change-state-modal" class="modal fade" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Change Regulated Demand State</h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body">
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.Designator)</div>
                    <div class="col-md-6 designator"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.TrafficArea)</div>
                    <div class="col-md-6 area"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.Point)</div>
                    <div class="col-md-6 point"></div>
                </div>
                <div class="form-group form-row td-waypoint td-sector td-lower">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.LowerFlightLevel)</div>
                    <div class="col-md-6 lower"></div>
                </div>
                <div class="form-group form-row td-waypoint td-sector td-upper">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.UpperFlightLevel)</div>
                    <div class="col-md-6 upper"></div>
                </div>
                <div class="form-group form-row td-waypoint td-radius">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.RadiusNm)</div>
                    <div class="col-md-6 radius"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.StartTime)</div>
                    <div class="col-md-6 start"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.EndTime)</div>
                    <div class="col-md-6 end"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.EndRecoveryTime)</div>
                    <div class="col-md-6 end-recovery"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.CapacityPerHr)</div>
                    <div class="col-md-6 capacity"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.CapacityRecoveryPerHr)</div>
                    <div class="col-md-6 capacity-recovery"></div>
                </div>
                <div class="form-group form-row td-adep">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.ExemptADEP)</div>
                    <div class="col-md-6 adep"></div>
                </div>
                <div class="form-group form-row td-ades">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.ExemptADES)</div>
                    <div class="col-md-6 ades"></div>
                </div>
                <div class="form-group form-row td-airlines">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.ExemptAirlines)</div>
                    <div class="col-md-6 airlines"></div>
                </div>
                <div class="form-group form-row">
                    <div class="col-md-6">@Html.DisplayNameFor(model => model.GDP.IntervalMin)</div>
                    <div class="col-md-6 interval"></div>
                </div>
                <div class="form-group form-row">
                    <label class="col-md-6 col-form-label">@Html.DisplayNameFor(model => model.GDP.Regul)</label>
                    <div class="col-md-6">
                        <input asp-for="GDP.Regul" class="form-control regul" placeholder="Identifier of a Regulation concerning a flight" required />
                        <span asp-validation-for="GDP.Regul" class="text-danger"></span>
                    </div>
                </div>
                <div class="form-group form-row">
                    <label class="col-md-6 col-form-label">@Html.DisplayNameFor(model => model.GDP.Regcause)</label>
                    <div class="col-md-6">
                        <input asp-for="GDP.Regcause" class="form-control regcause" placeholder="The CFMU and IATA coded designators indicating the reason for a regulation" required />
                        <span asp-validation-for="GDP.Regcause" class="text-danger"></span>
                    </div>
                </div>
                <div class="form-group form-row">
                    <label class="col-md-6 col-form-label">@Html.DisplayNameFor(model => model.GDP.Comment)</label>
                    <div class="col-md-6">
                        <input asp-for="GDP.Comment" class="form-control comment text-uppercase" placeholder="AFTN comment in free text without hyphen" />
                        <span asp-validation-for="GDP.Comment" class="text-danger"></span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-danger mr-auto undo-btn" data-state="-1">Unsave</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary execute-btn" data-state="1">Execute</button>
            </div>
        </div>
    </div>
</div>
<div id="ctot-modal" class="modal fade" tabindex="-1" data-backdrop="static">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"></h5>
                @*<button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>*@
            </div>

            <div class="modal-body">
                @*<h6>New EOBT : 15 / 10:12</h6>
                    <hr />*@
                <div class="alert alert-danger expire-alert" role="alert">CTOT Expires In </div>
                <div class="alert alert-info ctot-alert" role="alert"></div>
                <form class="form ctot-confirm-form">
                    <div class="form-check">
                        <input class="form-check-input" type="radio" name="ctotradio" id="exemptRadio" value="exempt">
                        <label class="form-check-label" for="exemptRadio">
                            Exempt This Flight
                        </label>
                    </div>

                    <!-- Mannual Ctot
                       <div class="form-check">
                        <input class="form-check-input" type="radio" name="ctotradio" id="mannualRadio" value="mannual">
                        <label class="form-check-label" for="mannualRadio">
                            Mannual Assign CTOT
                        </label>
                    </div>
                    <div class="form-group mannual-ctot-group" style="margin-left: 1.25em;display:none;">
                        <label for="exampleFormControlInput1">Maximum Delays  </label>
                        <input type="number" class="form-control" id="exampleFormControlInput1" style="width:70px;margin: 0 0.5em;display: inline-block;" min="0" value="0"> Minutes
                        <button type="button" class="btn btn-link get-mannual-ctot" style="margin-left:3em;">Get Ctot</button>
                        <div class="mannual-ctot-result"></div>
                    </div>-->
                    <hr />
                </form>
                <div class="form-group">
                    <label for="comment">Reasons</label>
                    <textarea class="form-control comment" rows="2"></textarea>
                    <span class="comment-error text-danger"></span>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-danger position-absolute exclude-flight-btn" style="left: 1em;">Exclude</button>
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary confirm-modal-btn">Confirm</button>
            </div>

        </div>
    </div>
</div>

@section Stylesheets {
    <link rel="stylesheet" href="~/lib/jquery-ui/jquery-ui.min.css" />
    <link rel="stylesheet" href="~/lib/bootstrap-table/dist/bootstrap-table.min.css" />
    <style>
        .ui-sortable-placeholder {
            width: 600px;
            opacity: 0.1;
        }

        .filter-btn {
            margin-top: 2em;
            margin-left: 1em;
        }

        .td-acknowledge {
            margin-top: 0.5em;
            margin-bottom: 0.5em;
            float: right;
        }

        .td-table-title, .td-table-subtitle {
            font-weight: 600;
            text-align: center;
        }

        .no-padding {
            padding: 0;
        }

        .modal:nth-of-type(even) {
            /* z-index: 1052 !important;*/
        }

        .modal-backdrop.show:nth-of-type(odd) {
            /* z-index: 1054 !important;*/
        }

        .border-thick {
            border-width: 3px !important;
        }

        .gdp-edit-btn-group, .gdp-chart-btn-group {
            display: flex;
            align-items: center;
            -ms-flex-pack: end;
            justify-content: flex-end;
        }

        .text-500 {
            font-weight: 500;
            padding-right: 1em;
        }

        .expired-time {
            font-size: smaller;
            color: darkred;
            font-style: italic;
        }

        td.td-comment > span {
            white-space: nowrap;
            width: 75px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: inline-block;
        }

            td.td-comment > span.show {
                white-space: break-spaces;
                overflow: unset;
            }

        .not-active {
            opacity: 0.5;
        }

            .not-active > canvas, .not-active > table, .not-active > div.td-acknowledge {
                pointer-events: none;
            }

        tbody > tr.tr-activate {
            background-color: rgba(30, 64, 156, 0.6) !important;
        }

        tbody > tr.tr-remove {
            background-color: #f5c6cb !important;
        }

        /* tbody > tr:hover {
            background-color: rgba(30, 64, 156, 0.6) !important;
        }
        tbody > tr.tr-remove:hover {
            background-color: #ed969e !important;
        }*/

        tbody > tr.tr-activate > td.td-edit > button {
            color: #ffffff;
        }

        .info-gdp {
            text-align: right;
            font-size: 1.7rem;
            margin-top: 0.2em;
            margin-left: 1.5em;
        }

        .info-tooltip {
            text-align: left;
        }

        .popover-body > span {
            margin-right: 1em;
            font-weight: 500;
        }
        .comment-error-text{
           color: red;
        }

        .req-info-title {
            font-size: small;
            margin-bottom: 5px;
        }

        .req-info-time {
            margin-bottom: 5px;
        }

            .req-info-time.info-obt, .req-info-title.title-obt {
                color: #007bff;
            }

            .req-info-time.info-obt {
                font-weight: 600;
            }
    </style>
}

@section Scripts {
    @{await Html.RenderPartialAsync("_ValidationScriptsPartial");}
    <script src="~/lib/chart.js/Chart.bundle.min.js"></script>
    <script src="~/lib/jquery-ui/jquery-ui.js"></script>
    <script src="~/lib/bootstrap-table/dist/bootstrap-table.min.js"></script>
    <script src="~/lib/moment.js"></script>
    <script src="~/js/TrafficDemand/traffic-demand.js"></script>
    <script src="~/js/GDPManage/gdpmanage.js"></script>
    @*<script src="~/js/GDPManage/gdpmanage.js"></script>*@
    @*<script src="~/js/GDP/gdp.js"></script>*@
}
