﻿@{
    ViewData["Title"] = "ATFAS Home Page";
}
@section Scripts {

    <link rel="stylesheet" href="@Url.Content("~/lib/jquery-ui/jquery-ui.css")" />
    <link rel="stylesheet" href="@Url.Content("~/css/Home/main.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/adminlte/dist/css/adminlte.min.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/adminlte/plugins/overlayScrollbars/css/OverlayScrollbars.min.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/fontawesome/css/all.min.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/leaflet/leaflet.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/adminlte/plugins/select2/css/select2.min.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/adminlte/plugins/colorselector/dist/bootstrap-colorselector.min.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/jquery-timepicker-1.3.5/jquery.timepicker.css")" />
    <link rel="stylesheet" href="@Url.Content("~/lib/bootstrap-table/dist/bootstrap-table.css")" />


    <script src="@Url.Content("~/lib/jquery-ui/jquery-ui.js")"></script>
    <script src="@Url.Content("~/lib/adminlte/plugins/jquery-mousewheel/jquery.mousewheel.js")"></script>
    <script src="@Url.Content("~/lib/adminlte/dist/js/adminlte.js")"></script>
    <script src="@Url.Content("~/lib/adminlte/plugins/overlayScrollbars/js/jquery.overlayScrollbars.min.js")"></script>
    <script src="@Url.Content("~/lib/adminlte/plugins/jquery-knob/jquery.knob.min.js")"></script>
    <script src="@Url.Content("~/lib/adminlte/plugins/select2/js/select2.min.js")"></script>
    <script src="@Url.Content("~/lib/adminlte/plugins/colorselector/dist/bootstrap-colorselector.min.js")"></script>
    <script src="@Url.Content("~/lib/jquery-timepicker-1.3.5/jquery.timepicker.js")"></script>
    <script src="@Url.Content("~/lib/bootstrap-table/dist/bootstrap-table.js")"></script>
    <script src="@Url.Content("~/lib/chart.js/Chart.bundle.min.js")"></script>
    <script src="@Url.Content("~/lib/leaflet/leaflet.js")"></script>
    <script src="@Url.Content("~/lib/leaflet/plugins/leaflet-RotateMarker.js")"></script>
    <script src="@Url.Content("~/lib/leaflet/plugins/L.KML.js")"></script>
    <script src="@Url.Content("~/js/terraformer-1.0.12.min.js")"></script>
    <script src="@Url.Content("~/js/terraformer-wkt-parser-1.2.1.min.js")"></script>
    <script src="@Url.Content("~/lib/moment.js")"></script>
    @*<script src="@Url.Content("~/js/Home/main.js")"></script>*@
    <script src="@Url.Content("~/js/Home/main.js")"></script>
    <script src="https://cdn.jsdelivr.net/npm/lodash@4.17.20/lodash.min.js"></script>

    <style>
    </style>
    <script>

        $(function () {

            //getTrafficDemand();
            //GetAdp();
            ///*Select2*/
            //$('.select2').select2();

            ///*Color Selector*/
            //$('#colorselector').colorselector();
        })

    </script>
}

<!-- Content Wrapper. Contains page content -->
<div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <div class="content-header no-padding">
        <!--Second Header -->
        @*   <nav class="navbar navbar-expand navbar-transparent no-padding">
        <!-- Left navbar links -->
        @*
        <div class="btn-group btn-group-toggle" data-toggle="buttons" style="width: 100%;margin-left: 0px;">
        <label class="disabled btn btn-app nav-btn nav-strat">
        <input type="radio" name="options" id="option_a5" autocomplete="off">
        <i class="fas fa-clipboard-list"></i>Strategic
        </label>
        <label class="disabled btn btn-app nav-btn nav-pre-tactical">
        <input type="radio" name="options" id="option_a4" autocomplete="off">
        <i class="fas fa-plane-departure"></i>Pre-Tactical
        </label>
        <label class="btn btn-app active nav-btn nav-tactical">
        <input type="radio" name="options" id="option_a3" autocomplete="off" checked="">
        <i class="fas fa-plane"></i>Tactical
        </label>
        <label class="disabled btn btn-app nav-btn nav-resource" style="margin-left: 0px;">
        <input type="radio" name="options" id="option_a1" autocomplete="off">
        <i class="fas fa-cloud-sun"></i>Weather
        </label>
        <label class="disabled btn btn-app nav-btn nav-post-ops">
        <input type="radio" name="options" id="option_a2" autocomplete="off">
        <i class="fas fa-chart-bar"></i>Post-Operations
        </label>
        </div>
        </nav> *@
        <div class="container-fluid">

            <div class="row mb-2" style="justify-content: flex-end;padding: 1em 1em 0em 0em;">

                <!--Current Date-->
                <div class="card card-danger dt-widget dt-live">
                    <div class="card-header" style="text-align: center;padding: 0.2rem 1.25rem;">
                        <span class="small-note text-center" id="livedate"></span>
                    </div>
                    <div class="card-body text-center" id="livetime" style="padding: 0.5rem;font-size: 1.3em;font-weight: 700;">
                        15:04:00 UTC
                    </div>
                    <!-- /.card-body -->
                </div>
                <!--Target Date-->
                @*      <div class="info-box dt-widget dt-target">
                <div class="info-box-icon" style="width: 100px; background: #fd7e14; border-radius: .25rem 0 0 .25rem;">
                <span style="font-size:1.8rem;font-weight: 700;line-height: 0.7;" id="targeticon">
                D<br>
                <span class="small-note">tactical</span>
                </span>
                </div>

                <div class="info-box-content">
                <div class="form-group">
                <label style="font-size: 0.8em; font-weight: 600;">Target Date</label>
                <div class="input-group">
                <input type="text" class="form-control" id="targetDate">
                <div class="input-group-append">
                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                </div>
                </div>
                </div>
                </div>
                <!-- /.info-box-content -->
                </div> *@
            </div><!-- /.row -->
            <div class="row mb-2 last-update" style="justify-content: flex-start; padding: 0 1em 1em 1.5em; font-size: small; color: #6c757d;">
                @*Last Update : 121212*@
            </div>
        </div><!-- /.container-fluid -->
    </div>
    <!-- /.content-header -->
    <!-- Main content -->
    <div class="content" id="maincontent">
        <div class="container-fluid">
            <div class="row">
                <section class="col-md-6 connectedSortable">
                    <div class="row">
                        <div class="col-md-9 mb-3 btn-group">
                            <div class="dropdown mr-3" style="width: 47%">
                                <button class="btn btn-default dropdown-toggle mr-3" type="button" data-toggle="dropdown" style="width:100%; background:white">
                                    Select Airports
                                </button>
                                <ul class="dropdown-menu" id="dropdownAirports" style="z-index: 9999;width:100%;padding-bottom:0px;">
                                    <li class="dropdown-search px-2">
                                        <input type="text" class="form-control" id="searchBoxAirport" placeholder="Search...">
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <!-- Dynamic checkboxes go here -->
                                    <li><hr class="dropdown-divider" style="display:none;"></li>
                                    <li class="px-3 py-2 bg-white" style="position: sticky; bottom: 0; z-index: 100; padding-top: 8px; border-top: 1px solid #dee2e6;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllCheckboxAirport">
                                            <label class="form-check-label" for="selectAllCheckboxAirport">Select All</label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                            <div class="dropdown " style="width: 47%">
                                <button class="btn btn-default dropdown-toggle" type="button" data-toggle="dropdown" style="width:100%; background:white">
                                    Select Sectors
                                </button>
                                <ul class="dropdown-menu" id="dropdownSectors" style="z-index: 9999;width:100%;padding-bottom:0px;">
                                    <li class="dropdown-search px-2">
                                        <input type="text" class="form-control" id="searchBoxSector" placeholder="Search...">
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <!-- Dynamic checkboxes go here -->
                                    <li><hr class="dropdown-divider" style="display:none;"></li>
                                    <li class="px-3 py-2 bg-white" style="position: sticky; bottom: 0; z-index: 100; padding-top: 8px; border-top: 1px solid #dee2e6;">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="selectAllCheckboxSector">
                                            <label class="form-check-label" for="selectAllCheckboxSector">Select All</label>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div class="col-md-3 d-flex justify-content-end mb-3">
                            <button class="btn btn-primary mr-2" id="applyBtn" style="width:46%">Apply</button>
                            <button class="btn btn-default" id="resetBtn" style="width:46%">Reset</button>
                        </div>

                    </div><!--map filter-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card hp-widget hp-widget-tactical" name="Situation Map" id="MapWidget">
                                <div class="card-header">
                                    <h3 class="card-title">Situation Map</h3>

                                    <div class="card-tools">
                                        <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        @*<button type="button" class="btn btn-tool" data-card-widget="maximize">
                                        <i class="fas fa-expand"></i>
                                        </button>*@
                                        <button type="button" class="btn btn-tool" data-card-widget="remove">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body" style="padding:0">
                                    <div id="mapid" style="height:750px;"></div>
                                </div>
                                <!-- /.card-body -->
                            </div>
                            <!-- /.card -->
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <a class="info-box bg-teal hp-widget hp-widget-tactical btn modal-demand" name="Airport demand/Capacity" id="AirportDemandWidget" data-toggle="modal" data-target="#modalCapacity">
                                <span class="info-box-icon sortable"><i class="fas fa-plane-departure"></i></span>

                                <div class="info-box-content">
                                    <span class="info-box-number">Airport Demand / Capacity</span>
                                    <span class="info-box-text">Display Airport Demand/Capacity 12 hrs onwards</span>
                                </div>
                            </a>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <a class="info-box bg-primary hp-widget hp-widget-tactical btn modal-demand" name="Sector demand/Capacity" id="SectorDemandWidget" data-toggle="modal" data-target="#modalCapacity">
                                <span class="info-box-icon sortable"><i class="fas fa-chart-pie"></i></span>

                                <div class="info-box-content">
                                    <span class="info-box-number">Sector Demand / Capacity</span>
                                    <span class="info-box-text">Display Sector Demand/Capacity 12 hrs onwards</span>
                                </div>
                            </a>
                        </div>
                    </div>
                </section>
                @*  <section class="col-md-4 connectedSortable">
                </section> *@
                <section class="col-md-6 connectedSortable">
                    @*<div class="row">
                    <div class="col-md-6">
                    <div class="info-box bg-success hp-widget hp-widget-tactical" name="MET Data" id="MetWidget" style="display:none;">
                    <span class="info-box-icon"><i class="fas fa-cloud-sun"></i></span>

                    <div class="info-box-content">
                    <span class="info-box-number">MET Data</span>
                    <span class="info-box-text">Display MET Data</span>
                    </div>

                    </div>
                    </div>
                    <div class="col-md-6">
                    <div class="info-box bg-indigo hp-widget hp-widget-tactical" name="Flight Track" id="TrackWidget" style="display:none;">
                    <span class="info-box-icon"><i class="fas fa-plane-departure"></i></span>

                    <div class="info-box-content">
                    <span class="info-box-number">Flight Track</span>
                    <span class="info-box-text">Display Flight Track</span>
                    </div>

                    </div>
                    </div>
                    </div>*@

                    @*   <div class="row">
                    <div class="col-md-6 connectedSortable">
                    <div class="card card-info hp-widget hp-widget-tactical" name="Totals Flights" id="FlightWidget">
                    <div class="card-header" style="padding:0">
                    <div class="small-box bg-info" style="margin:0">
                    <div class="inner">
                    <h3 id="flightTTL"></h3>

                    <p>Flights</p>
                    </div>
                    <div class="icon">
                    <i class="fas fa-plane"></i>
                    </div>
                    </div>

                    </div>
                    <!-- /.card-header STATS -->
                    <div class="card-body stat-body">
                    <div class="info-box-content" style="display: flex;-ms-flex-direction: column;flex-direction: column;
                    -ms-flex-pack: center;justify-content: center;line-height: 1.8;-ms-flex: 1;flex: 1;padding: 0 10px;">
                    <span class="info-box-number" id="flightLanded"></span>
                    <div class="progress" style=" background-color: rgba(0,0,0,.125); height: 2px; margin: 5px 0;">
                    <div class="progress-bar" id="flightLandedProgress" style=" background-color: #17a2b8;"></div>
                    </div>
                    <span class="progress-description" id="flightLandedProgressTxt">

                    </span>
                    <span class="info-box-number" id="flightAirborne">

                    </span>
                    <div class="progress" style=" background-color: rgba(0,0,0,.125); height: 2px; margin: 5px 0;">
                    <div class="progress-bar" id="flightAirborneProgress" style="background-color: #17a2b8;"></div>
                    </div>
                    <span class="progress-description" id="flightAirborneProgressTxt">

                    </span>
                    </div>
                    </div>
                    <!-- /.card-body width: 70%; -->
                    </div>
                    </div>
                    <div class="col-md-6 connectedSortable">
                    <div class="card card-warning hp-widget hp-widget-tactical" name="Total Delays" id="DelaysWidget">
                    <div class="card-header" style="padding:0">
                    <div class="small-box bg-purple" style="margin:0">
                    <div class="inner">
                    <h3 id="flightAvgDLT"></h3>
                    <!-- PENDING TO CHANGE CALCULATED FROM CTOT F.  main.js -->
                    <p>Average ATFM Minute(s) Delay</p>
                    </div>
                    <div class="icon">
                    <i class="fas fa-hourglass-half"></i>
                    </div>
                    </div>

                    </div>
                    <!-- /.card-header -->
                    <div class="card-body stat-body">
                    <div class="info-box-content" style="display: flex;-ms-flex-direction: column;flex-direction: column;
                    -ms-flex-pack: center;justify-content: center;line-height: 1.8;-ms-flex: 1;flex: 1;padding: 0 10px;">
                    <span class="info-box-number" id="flightTtlDLT"></span>
                    <span class="progress-description">
                    Total ATFM Minute(s) Delay
                    </span>
                    <span class="info-box-number" id="flightTtlDL"></span>
                    <div class="progress" style=" background-color: rgba(0,0,0,.125); height: 2px; margin: 5px 0;">
                    <div class="progress-bar" id="fdelay" style="background-color: #6f42c1;"></div>
                    </div>
                    <span class="progress-description" id="fdelaytxt">

                    </span>
                    </div>
                    </div>
                    <!-- /.card-body -->
                    </div>
                    </div>
                    </div> *@<!--Stats Widget-->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card hp-widget hp-widget-tactical" name="ATFM Measure" id="AtfmWidget">
                                <div class="card-header">
                                    <h3 class="card-title">Active ATFM Measure</h3>

                                    <div class="card-tools">
                                        <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <button type="button" class="btn btn-tool" data-card-widget="remove">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body no-padding" style="font-size: 0.85rem;">
                                    <ul class="list-group measure-ul list-group-flush">
                                    </ul>

                                </div>
                                <!-- /.card-body -->
                                @*<div class="card-footer text-center footer-widget">
                                <a href="javascript:void(0)" class="uppercase">View More</a>
                                </div>*@
                            </div>
                        </div>
                    </div><!--Measure row -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card hp-widget hp-widget-tactical" name="ADP" id="AdpWidget">
                                <div class="card-header">
                                    <h3 class="card-title">ADP</h3>

                                    <div class="card-tools">
                                        <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <button type="button" class="btn btn-tool" data-card-widget="remove">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body no-padding" style="font-size: 0.85rem;">
                                    @*<div class="info-box adp-local-box" style="display: inline-block;min-height: unset;padding: 1em;border-left: 3px solid #007bff;font-weight: 600;">
                                    BKK ATFMU
                                    <span  style="float: right;">
                                    <a href="#" class="btn btn-xs  btn-outline-warning adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                    <a href="#" class="btn btn-xs  btn-outline-primary adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                    <a href="#" class="btn btn-xs  btn-outline-success adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                    </span>
                                    </div>*@
                                    <div class="card-body no-padding" style="font-size: 0.85rem;">

                                        <!-- 🔍 SEARCH BOX -->
                                        <div class="input-group input-group-sm p-2 mb-2">
                                            <input id="adpSearch" class="form-control" placeholder="Search ADP…">
                                            <div class="input-group-append">
                                                <button id="adpSearchBtn" class="btn btn-outline-secondary" type="button">
                                                    <i class="fas fa-search"></i>
                                                </button>
                                            </div>
                                        </div>
                                        <ul class="list-group adp-list list-group-flush">
                                            @*<li class="list-group-item border-bottom bg-light adp-local" >
                                            <span class="local-pin">
                                            <i class="fas fa-map-marker-alt" ></i>
                                            </span>
                                            Hong Kong ATFMU
                                            <span style="float: right;">
                                            <a href="#" class="btn btn-xs  btn-warning adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            <a href="#" class="btn btn-xs  btn-primary adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            <a href="#" class="btn btn-xs  btn-success adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            </span>
                                            </li>
                                            <li class="list-group-item border-bottom">
                                            Singapore ATFMU
                                            <span style="float: right;">
                                            <a href="#" class="btn btn-xs  btn-warning adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            <a href="#" class="btn btn-xs  btn-primary adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            <a href="#" class="btn btn-xs  btn-success adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            </span>
                                            </li>
                                            <li class="list-group-item border-bottom">
                                            Sanya ATFMU
                                            <span style="float: right;">
                                            <a href="#" class="btn btn-xs  btn-warning adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            <a href="#" class="btn btn-xs  btn-primary adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            <a href="#" class="btn btn-xs  btn-success adp-btn"><span class="adp-dl-content">(Unavailable)</span></a>
                                            </span>
                                            </li>*@
                                        </ul>
                                    </div>
                                    <!-- /.card-body -->
                                    <div class="card-footer text-center footer-widget">
                                        <a onclick="getAllAdp();" href="javascript:void(0)" class="uppercase" data-toggle="modal" data-target="#modalAdpAll">View More</a>
                                    </div>
                                </div>
                                <!-- /.card -->
                                <!-- /.card -->
                            </div>
                        </div>
                    </div><!--ADP row -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card hp-widget hp-widget-tactical" name="Announcement" id="AnnounceWidget">
                                <div class="card-header">
                                    <h3 class="card-title">News and Announcement</h3>

                                    <div class="card-tools">
                                        <a href="/Management/Announcement" target="_blank" class="btn btn-tool" id="Newsedit" aria-expanded="false">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                            <i class="fas fa-minus"></i>
                                        </button>
                                        <button type="button" class="btn btn-tool" data-card-widget="remove">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="card-body">
                                    @*   <div style="display:flex;justify-content:center;margin-bottom:1.25em;">
                                    <div class="btn-group btn-group-toggle" data-toggle="buttons">
                                    <label class="btn btn-sm btn-outline-info btn-flat active">
                                    <input type="radio" name="options" id="option_a1" autocomplete="off" checked="" value="0"> Latest
                                    </label>
                                    <label class="btn btn-sm btn-outline-info btn-flat">
                                    <input type="radio" name="options" id="option_a2" autocomplete="off" value="1"> Ongoing
                                    </label>
                                    </div>
                                    </div> *@
                                    <div id="announceBox"></div>
                                </div>
                                <!-- /.card-body -->
                                <div class="card-footer text-center footer-widget">
                                    <a href="javascript:void(0)" class="uppercase" data-toggle="modal" data-target="#modalAnnounceAll">View More</a>
                                </div>
                            </div>
                            <!-- /.card -->
                        </div>
                    </div>
                    <!-- ─── RESOURCES  (directly below News) ───────────────────────────────── -->
                    <div class="row">
                        <div class="col-md-12">
                            <div class="card hp-widget hp-widget-tactical" name="Resources" id="ResourceWidget">
                                <div class="card-header">
                                    <h3 class="card-title">References</h3>

                                    <div class="card-tools">
                                        <button type="button" class="btn btn-tool" data-card-widget="collapse"><i class="fas fa-minus"></i></button>
                                        <button type="button" class="btn btn-tool" data-card-widget="remove"><i class="fas fa-times"></i></button>
                                    </div>
                                </div>

                                <div class="card-body no-padding" style="font-size:.9rem">
                                    <ul class="list-group resource-list list-group-flush"></ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-12">
                            <!--  <div class="card hp-widget hp-widget-tactical" name="ADP" id="AdpWidget">
                                  <div class="card-header">
                                      <h3 class="card-title">ADP</h3>

                                      <div class="card-tools">
                                          <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
                                              <i class="fas fa-minus"></i>
                                          </button>
                                          <button type="button" class="btn btn-tool" data-card-widget="remove">
                                              <i class="fas fa-times"></i>
                                          </button>
                                      </div>
                                  </div>
                                  <div class="card-body">

                                      <div id="accordion">
                                          <div class="card card-primary">
                                              <div class="card-header">
                                                  <h4 class="card-title w-100">
                                                      <a class="d-block w-100 collapsed" data-toggle="collapse" href="#collapseOne" aria-expanded="false">
                                                          Local ADP <span class="badge badge-light" style="float:right;">3</span>
                                                      </a>
                                                  </h4>
                                              </div>
                                              <div id="collapseOne" class="collapse" data-parent="#accordion" style="">
                                                  <div class="card-body">
                                                      <p><a href="#">Local ADP 10/10/2020</a></p>
                                                      <p><a href="#">Local ADP 11/10/2020</a></p>
                                                      <p><a href="#">Local ADP 13/10/2020</a></p>
                                                  </div>
                                              </div>
                                          </div>
                                          <div class="card card-gray">
                                              <div class="card-header">
                                                  <h4 class="card-title w-100">
                                                      <a class="d-block w-100" data-toggle="collapse" href="#collapseTwo">
                                                          International ADP <span class="badge badge-light" style="float:right;">0</span>
                                                      </a>
                                                  </h4>
                                              </div>
                                              <div id="collapseTwo" class="collapse" data-parent="#accordion">
                                                  <div class="card-body">
                                                      - none -
                                                  </div>
                                              </div>
                                          </div>

                                      </div>
                                  </div>

                                  <div class="card-footer text-center footer-widget">
                                      <a href="javascript:void(0)" class="uppercase">View More</a>
                                  </div>
                              </div>
                            /.card -->
                        </div>
                    </div>

                </section>
            </div>
            <!-- SAMPLE ROW WITH CARD  -->
            @*<div class="row">
            <div class="col-md-4">
            <div class="card">
            <div class="card-header">
            <h3 class="card-title">General</h3>

            <div class="card-tools">
            <button type="button" class="btn btn-tool" data-card-widget="collapse" title="Collapse">
            <i class="fas fa-minus"></i>
            </button>
            <button type="button" class="btn btn-tool" data-card-widget="remove">
            <i class="fas fa-times"></i>
            </button>
            </div>
            </div>
            <div class="card-body">
            body
            </div>

            <div class="card-footer text-center footer-widget">
            <a href="javascript:void(0)" class="uppercase">View More</a>
            </div>
            </div>

            </div>
            <div class="col-md-4">

            </div>
            <div class="col-md-4">

            </div>
            </div>*@

            <!--   MODAL  -->
            <div class="modal fade" id="modalCapacity">
                <div class="modal-dialog modal-lg modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">  </h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body">

                            <div class="info-box-text text-small update-td-table"></div>
                            <table class="table table-hover">
                                @*<thead >
                                <tr class="capacity-thead"></tr>
                                </thead>*@
                                <tbody class="capacity-tbody">
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!-- /.modal-content -->
                </div>
                <!-- /.modal-dialog -->
            </div>



            <div class="modal fade" id="modalAnnounceAll">
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">News and Announcement Archive</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body" style="min-height:700px;">
                            <div class="table-responsive" style="padding:0 1em; width:100%;">
                                <table id="atable" class="table" data-height="450" data-id-field="id" data-search="true">
                                    <thead>
                                        @*class="thead-dark"*@
                                        <tr>
                                            @*<th data-checkbox="true" class="view-item">View</th>*@
                                            <th data-field="header" data-sortable="true">Announcement List</th>
                                            <th data-field="fileIds" data-formatter="filecolFormat"></th>
                                            <th data-field="timeStamp" data-sortable="true" data-formatter="dateFormat">Publish Date</th>
                                            <th data-field="id" data-formatter="viewcolFormat" data-halign="center"></th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>

                    </div>
                    <!-- /.modal-content -->
                </div>
                <!-- /.modal-dialog -->
            </div>


            <div class="modal fade" id="modalAnnounceById">
                <div class="modal-dialog modal-dialog-centered">
                    <div class="modal-content">
                        <div class="modal-header" id="modalAnnounceHeader">
                            <h5 class="modal-title">News and Announcement</h5>
                            @*<button type="button" class="close" data-dismiss="modal" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                            </button>*@
                        </div>
                        <div class="modal-body" style=" padding: 2em; min-height: 500px;">
                            @*<button type="button" class="btn btn-default" onclick="GetAnnouncementById();">
                            gettest
                            </button>*@
                            <div id="modalAnnouncedate" class="announce-modal-body"></div>
                            <div id="modalAnnounceUser" class="announce-modal-body"></div>
                            <div style="padding-bottom:2em;margin-top:1em;">
                                <div id="modalAnnouncetitle" class="announce-modal-body"></div>
                                <hr />
                                <div id="modalAnnouncemsg" class="announce-modal-body"></div>
                            </div>

                            <div id="modalAnnouncevalid" class="announce-modal-body"></div>
                            <div id="modalAnnouncefile" class="announce-modal-body"></div>
                        </div>
                        <div class="modal-footer btn justify-content-center" data-dismiss="modal" id="modalAnnounceFooter">
                            <i class="fas fa-times"></i><span style="font-weight:600">  Close</span>
                        </div>
                    </div>
                    <!-- /.modal-content -->
                </div>
                <!-- /.modal-dialog -->
            </div>

            <!--ADP Modal-->

            <div class="modal fade" id="modalAdpAll">
                <div class="modal-dialog modal-dialog-centered modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">ADP Archive</h5>
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                        <div class="modal-body" style="min-height:700px;">
                            <div class="table-responsive" style="padding:0 1em; width:100%;">
                                <table id="adptable" class="table" data-height="450" data-id-field="id" data-search="true">
                                    <thead>
                                        @*class="thead-dark"*@
                                        <tr>
                                            @*<th data-checkbox="true" class="view-item">View</th>*@
                                            <th data-field="atfmu.name" data-sortable="true">ATFMU</th>
                                            <th data-field="adpHeader" data-sortable="true">ADP</th>
                                            <th data-field="adpDate" data-formatter="dateFormatAdp" data-sortable="true">Announce Date</th>
                                            <th data-field="id" data-formatter="downloadcolFormat" data-align="center"></th>
                                        </tr>
                                    </thead>
                                </table>
                            </div>
                        </div>

                    </div>
                    <!-- /.modal-content -->
                </div>
                <!-- /.modal-dialog -->
            </div>

            <div class="modal fade" id="modalAtfmu">
                <div class="modal-dialog modal-dialog-centered modal-sm">
                    <div class="modal-content bg-light">
                        <div class="modal-header" id="modalAtfmuHeader" style="border-bottom: none;">

                            <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                                <span aria-hidden="true" style="color: white;">×</span>
                            </button>
                        </div>
                        <div class="modal-body atfmu-detail no-padding">
                            <div class="bg-pink atfmu-body-header">
                                <h5 class="text-white">Bangkok ATFMU</h5>
                                <div class="text-white-50 text-sm">Contact Information</div>
                            </div>
                            <div class="atfmu-contact-wrapper">
                                <div>
                                    <ul class="ml-4 mb-0 fa-ul contact-ul">
                                        <li><span class="fa-li"><i class="far fa-envelope"></i></span> <EMAIL></li>
                                        <li><span class="fa-li"><i class="fas fa-phone-alt"></i></span> 081 0005698</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </div><!-- /.container-fluid -->

    </div>
    <!-- /.content -->
</div>
<!-- /.content-wrapper -->
<!-- Control Sidebar -->
<aside class="control-sidebar control-sidebar-dark">
    <!-- Control sidebar content goes here -->
    <div class="p-3">
        <h5 style="display:inline">Customize Widget</h5>
        <button type="button" data-widget="control-sidebar" data-slide="true" class="btn btn-link" style="padding-top:0;">
            <span style="color:white; font-size:large;"><i class="fa fa-times"></i></span>
        </button>
        <div class="sidelist-wrapper">
            @*<div class="mb-1"><input type="checkbox" value="1" class="mr-1"><span>ADP</span></div>
            <div class="mb-1"><input type="checkbox" value="2" class="mr-1"><span>Flight Stat</span></div>
            <div class="mb-1"><input type="checkbox" value="3" class="mr-1"><span>AIM</span></div>
            <div class="mb-1"><input type="checkbox" value="4" class="mr-1"><span>Current Map</span></div>
            <div class="mb-1"><input type="checkbox" value="5" class="mr-1"><span>News and Announcement</span></div>
            <div class="mb-1"><input type="checkbox" value="6" class="mr-1"><span>Demand/Capacity</span></div>*@
        </div>

    </div>
</aside>
<div id="loading-overlay" style="display:none;"></div>
<!-- /.control-sidebar -->
