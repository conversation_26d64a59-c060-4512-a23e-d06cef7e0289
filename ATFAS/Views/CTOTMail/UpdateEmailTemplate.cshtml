﻿@{ ATFAS.Controllers.CTOTHelpers.CTOTGenerateMail mail = (ATFAS.Controllers.CTOTHelpers.CTOTGenerateMail)Model; }
<div style="font-family: Arial, Helvetica, sans-serif">
    <p><b>CTOT Update from Bangkok ATFMU</b></p>
    Dear @Model.RecipientName<br />
    <p>After CTOT management process for flights operating in the following ATFM Measure(s): </p>
    @{var h = 1; }
    @foreach (var g in mail.Params)
    {
        <p>@h) @g.Designator </p>
        h += 1;
    }
    <p>,the following flights have been updated:</p>
    <table border="1" cellspacing="0" cellpadding="2">
        <thead>
            <tr>
                <th>Flight</th>
                <th>Callsign</th>
                <th>Departure</th>
                <th>Arrival</th>
                <th>EOBT (UTC)</th>
                <th>CTOT (UTC)</th>
                <th>CLDT (UTC)</th>
                <th>NEW OBT (UTC)</th>
            </tr>
        </thead>
        <tbody>
            @{ var i = 1; }
            @foreach (var f in mail.Generates)
            {
                <tr style="color: black">
                    <td style="color: black">@i</td>
                    <td style="color: black">@f.Flight.Callsign</td>
                    <td style="color: black">@f.Flight.AirportDeparture</td>
                    <td style="color: black">@f.Flight.AirportArrival</td>
                    <td style="color: black">@(((DateTime)f.Flight.EOBT).ToString("dd / HH:mm"))</td>
                    @if (f.Flight.CTOT != null)
                    {
                        <td style="color: black">@(((DateTime)f.Flight.CTOT).ToString("dd / HH:mm"))</td>
                    }
                    else
                    {
                        <td style="color: black">Cancelled</td>
                    }
                    @if (f.Flight.CLDT != null)
                    {
                        <td style="color: black">@(((DateTime)f.Flight.CLDT).ToString("dd / HH:mm"))</td>
                    }
                    else
                    {
                        <td style="color: black">Cancelled</td>
                    }
                    @if (f.Flight.EOBTAirline != null)
                    {
                        <td style="color: black">@(((DateTime)@f.Flight.EOBTAirline).ToString("dd / HH:mm"))</td>
                    }
                    else
                    {
                        <td style="color: black"> - </td>

                    }
                </tr>
                i += 1;
            }
        </tbody>
    </table>
</div>