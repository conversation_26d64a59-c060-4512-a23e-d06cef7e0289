<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <OutputType>WinExe</OutputType>
    <UserSecretsId>69d5861a-9e86-4973-a604-bc8b5c588e73</UserSecretsId>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Migrations\20200422053115_UpdateFlightSourceIdentity.cs" />
    <Compile Remove="Migrations\20200422053115_UpdateFlightSourceIdentity.Designer.cs" />
    <Compile Remove="Migrations\20200422054109_UpdateTrajectoryForeignKey2.cs" />
    <Compile Remove="Migrations\20200422054109_UpdateTrajectoryForeignKey2.Designer.cs" />
    <Compile Remove="Migrations\20200422054243_UpdateTrajectoryForeignKey2.cs" />
    <Compile Remove="Migrations\20200422054243_UpdateTrajectoryForeignKey2.Designer.cs" />
    <Compile Remove="Migrations\20200422054812_UpdateTrajectoryForeignKey2.cs" />
    <Compile Remove="Migrations\20200422054812_UpdateTrajectoryForeignKey2.Designer.cs" />
    <Compile Remove="Migrations\20200430073206_TrafficDemandUpdate.cs" />
    <Compile Remove="Migrations\20200430073206_TrafficDemandUpdate.Designer.cs" />
    <Compile Remove="Migrations\20200520062251_TrajectoryUpdate.cs" />
    <Compile Remove="Migrations\20200520062251_TrajectoryUpdate.Designer.cs" />
    <Compile Remove="Migrations\20200610044224_FlightUpdate4.cs" />
    <Compile Remove="Migrations\20200610044224_FlightUpdate4.Designer.cs" />
    <Compile Remove="Migrations\20200610050548_FlightUpdate4.cs" />
    <Compile Remove="Migrations\20200610050548_FlightUpdate4.Designer.cs" />
    <Compile Remove="Migrations\20200610051016_FlightDelete.cs" />
    <Compile Remove="Migrations\20200610051016_FlightDelete.Designer.cs" />
    <Compile Remove="Migrations\20200610051622_FlightDelete2.cs" />
    <Compile Remove="Migrations\20200610051622_FlightDelete2.Designer.cs" />
    <Compile Remove="Migrations\20200610052000_FlightUpdate4.cs" />
    <Compile Remove="Migrations\20200610052000_FlightUpdate4.Designer.cs" />
    <Compile Remove="Migrations\20200717011950_ChangeGeoDatatype.cs" />
    <Compile Remove="Migrations\20200717011950_ChangeGeoDatatype.Designer.cs" />
    <Compile Remove="Migrations\20201001044335_CapacityAdd.cs" />
    <Compile Remove="Migrations\20201001044335_CapacityAdd.Designer.cs" />
    <Compile Remove="Migrations\20201203074138_CapacityUpdate10.cs" />
    <Compile Remove="Migrations\20201203074138_CapacityUpdate10.Designer.cs" />
    <Compile Remove="Migrations\20201203074309_CapacityUpdate10.cs" />
    <Compile Remove="Migrations\20201203074309_CapacityUpdate10.Designer.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="bundleconfig.json" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="bootstrap" Version="5.3.3" />
    <PackageReference Include="CsvHelper" Version="27.2.1" />
    <PackageReference Include="Elastic.Clients.Elasticsearch" Version="8.0.4" />
    <PackageReference Include="Microsoft.AspNet.WebApi.Client" Version="5.2.7" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Identity.UI" Version="9.0.0" />
    <PackageReference Include="MailKit" Version="2.9.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.0" />
    <PackageReference Include="Microsoft.CodeAnalysis.Common" Version="4.8.0" />
    <PackageReference Include="Microsoft.Data.SqlClient" Version="5.2.2" />
    <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer.NetTopologySuite" Version="9.0.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.ML" Version="1.7.1" />
    <PackageReference Include="Microsoft.ML" Version="1.7.1" />
    <PackageReference Include="Microsoft.SqlServer.Types" Version="160.1000.6" />
    <PackageReference Include="NetCore.AutoRegisterDi" Version="2.1.0" />
    <PackageReference Include="PdfSharp" Version="6.2.0" />
    <PackageReference Include="RazorEngineCore" Version="2022.1.2" />
    <PackageReference Include="Serilog.Formatting.Elasticsearch" Version="8.4.1" />
    <PackageReference Include="Serilog.Sinks.Elasticsearch" Version="8.4.1" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.0" />
    <PackageReference Include="System.Drawing.Common" Version="6.0.0" />
    <PackageReference Include="System.Text.Encoding.CodePages" Version="6.0.0" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="ImportFlightschedLib">
      <HintPath>..\Lib\ImportFlightschedLib.dll</HintPath>
    </Reference>

    <Reference Include="SlotMessageLibrary">
      <HintPath>Lib\SlotMessageLibrary.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <None Include="bundleconfig.json" />
    <None Include="Views\User\Index.cshtml" />
    <None Include="wwwroot\lib\fontawesome\webfonts\fa-brands-400.svg" />
    <None Include="wwwroot\lib\fontawesome\webfonts\fa-brands-400.woff2" />
    <None Include="wwwroot\lib\fontawesome\webfonts\fa-regular-400.svg" />
    <None Include="wwwroot\lib\fontawesome\webfonts\fa-regular-400.woff2" />
    <None Include="wwwroot\lib\fontawesome\webfonts\fa-solid-900.svg" />
    <None Include="wwwroot\lib\fontawesome\webfonts\fa-solid-900.woff2" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Areas\Management\Data\" />
    <Folder Include="Areas\Management\Models\" />
    <Folder Include="wwwroot\files\resources\" />
    <Folder Include="wwwroot\model\" />
    <Folder Include="wwwroot\files\" />
    <Folder Include="wwwroot\temp\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\DataLayer\DataLayer.csproj" />
  </ItemGroup>

</Project>
