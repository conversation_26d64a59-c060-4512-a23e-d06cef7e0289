﻿/* Please see documentation at https://docs.microsoft.com/aspnet/core/client-side/bundling-and-minification
for details on configuring this project to bundle and minify static web assets. */

a.navbar-brand {
  white-space: normal;
  text-align: center;
  word-break: break-all;
}

/* Provide sufficient contrast against white background */
a {
  color: #0366d6;
}

.btn-primary {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

.nav-pills .nav-link.active, .nav-pills .show > .nav-link {
  color: #fff;
  background-color: #1b6ec2;
  border-color: #1861ac;
}

/* Sticky footer styles
-------------------------------------------------- */
html {
  font-size: 14px;
}
@media (min-width: 768px) {
  html {
    font-size: 16px;
  }
}

.border-top {
  border-top: 1px solid #e5e5e5;
}
.border-bottom {
  border-bottom: 1px solid #e5e5e5;
}

.box-shadow {
  box-shadow: 0 .25rem .75rem rgba(0, 0, 0, .05);
}

button.accept-policy {
  font-size: 1rem;
  line-height: inherit;
}

/* Sticky footer styles
-------------------------------------------------- */
html {
  position: relative;
  min-height: 100%;
}

body {
  /* Margin bottom by footer height */
  margin-bottom: 60px;
}
.footer {
  position: absolute;
  bottom: 0;
  width: 100%;
  white-space: nowrap;
  line-height: 60px; /* Vertically center the text there */
}

/*-------------------- Loading Overlay ---------------------*/

/* Absolute Center Spinner */
#loading-overlay {
    position: fixed;
    z-index: 1100;
    height: 2em;
    width: 2em;
    overflow: visible;
    margin: auto;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
}
    /* Transparent Overlay */
    #loading-overlay:before {
        content: '';
        display: block;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0,0,0,0.3);
    }
    /* :not(:required) hides these rules from IE9 and below */
    #loading-overlay:not(:required) {
        /* hide "loading..." text */
        font: 0/0 a;
        color: transparent;
        text-shadow: none;
        background-color: transparent;
        border: 0;
    }
        #loading-overlay:not(:required):after {
            content: '';
            display: block;
            font-size: 10px;
            width: 1em;
            height: 1em;
            margin-top: -0.5em;
            -webkit-animation: spinner 1500ms infinite linear;
            -moz-animation: spinner 1500ms infinite linear;
            -ms-animation: spinner 1500ms infinite linear;
            -o-animation: spinner 1500ms infinite linear;
            animation: spinner 1500ms infinite linear;
            border-radius: 0.5em;
            -webkit-box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.5) -1.5em 0 0 0, rgba(0, 0, 0, 0.5) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
            box-shadow: rgba(0, 0, 0, 0.75) 1.5em 0 0 0, rgba(0, 0, 0, 0.75) 1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) 0 1.5em 0 0, rgba(0, 0, 0, 0.75) -1.1em 1.1em 0 0, rgba(0, 0, 0, 0.75) -1.5em 0 0 0, rgba(0, 0, 0, 0.75) -1.1em -1.1em 0 0, rgba(0, 0, 0, 0.75) 0 -1.5em 0 0, rgba(0, 0, 0, 0.75) 1.1em -1.1em 0 0;
        }

/* Animation */
@-webkit-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-moz-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@-o-keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}
@keyframes spinner {
    0% {
        -webkit-transform: rotate(0deg);
        -moz-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        -o-transform: rotate(0deg);
        transform: rotate(0deg);
    }
    100% {
        -webkit-transform: rotate(360deg);
        -moz-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        -o-transform: rotate(360deg);
        transform: rotate(360deg);
    }
}

/*-------------------- jQuery UI ---------------------*/

.ui-autocomplete.ui-front {
    z-index: 2000;
}
/**------------------Notification Bar----------------*/
.icon-badge-container {
 /*   margin-top: 20px;*/
    position: relative;
}

.icon-badge-icon {
    font-size: 1.5rem;
    position: relative;
}

.icon-badge {
    background-color: red;
    font-size: 12px;
    color: white;
    text-align: center;
    width: 20px;
    height: 20px;
    border-radius: 35%;
    position: absolute; /* changed */
    top: -5px; /* changed */
    left: 14px; /* changed */
}
#dropdownNotice.dropdown-toggle::after {
    display: none;
}
#dropdownNotice {
    color: #fff;
    cursor: pointer;
}
    #dropdownNotice:hover, #dropdownNotice:focus {
        color:#17a2b8 ;
    }
#dropdownNoticeWrapper > .dropdown-menu {
    max-height: 500px;
    overflow-y: auto; 
    box-shadow: 0 4px 8px 0 rgba(0, 0, 0, 0.2), 0 6px 20px 0 rgba(0, 0, 0, 0.19);
    padding-top: 0;
}
    #dropdownNoticeWrapper > .dropdown-menu > .dropdown-item:hover {
        color: #fff !important;
        text-decoration: none;
        background-color: #17a2b8 !important;
    }

.date-item {
    font-weight: bold !important;
    padding: 1rem 1.5rem;
    font-size: large;
    background-color: #17a2b8;
    color: white;
}
.header-item {
    font-weight: bold !important;
    padding: 0.25rem 1.5rem;
}
.itm-time {
    margin-right: 1.5em;
}

/*-------------------- Chart.js ---------------------*/

/* External tooltip container */
#chartjs-tooltip {
    position: absolute;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s;
    transform: translate(-50%, 0);
    background: rgba(0, 0, 0, 0.8);
    border-radius: 4px;
    font-family: "Helvetica Neue", "Helvetica", "Arial", sans-serif;
    font-size: 12px;
    font-style: normal;
    line-height: 1.2em;
    white-space: nowrap;
    color: white;
    padding: 6px 8px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.2);
}

    /* Tooltip arrow */
    #chartjs-tooltip::after {
        content: "";
        position: absolute;
        top: -4px; /* arrow above the tooltip box */
        left: 50%;
        transform: translateX(-50%);
        border-left: 5px solid transparent;
        border-right: 5px solid transparent;
        border-bottom: 5px solid rgba(0, 0, 0, 0.8); /* same as background */
    }
