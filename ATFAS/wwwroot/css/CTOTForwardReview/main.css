﻿#wrapper footer {
    text-align: center;
    margin-top: 100px;
}


.container {
    margin-top: 40px;
}


#ctot-list {
    margin-top: 40px;
}


.section {
    margin-top: 20px;
}


.modal-body .ctot-param {
    width: 400px;
}

.modal-body .ctot-delay {
    width: 350px;
}


.modal-dialog {
    width: 1200px;
}


#table-flight thead, #table-flight tbody tr {
/*    display: table;
    width: 100%;
    table-layout: fixed;*/
}

#table-flight thead {
   /* width: calc(100% - 1.5em);*/
}

#table-flight tbody {
/*    display: block;
    height: 500px;
    overflow-y: scroll;*/
}
#table-flight .checkbox-inline {
    margin-bottom: 0px !important;
}
#table-flight thead th {
    border-top: none !important;
    position: sticky;
    top: 0;
    background-color: #fff;
}
#table-flight td:first-child, #table-flight th:first-child {
    width: 70px;
}
#post-modal .post-plot {
    height: 150px;
}

#post-modal .progress {
    margin-bottom: 0;
}
.ctot-badge {
    margin-left: 4px;
    font-size: small;
}

    #post-modal .progress .progress-bar {
        color: #000;
        font-weight: bold;
        background-image: none;
        background-color: mediumseagreen;
    }
#table-request, #table-flight {
    border-collapse: separate !important;
    border-spacing: 0 !important;
}
    #table-request thead th {
        position: sticky;
        top: 0;
        background-color: #eef7f9 ;/*rgba(190, 229, 235,0.5);*/
    }
    #table-request tbody tr {
        cursor: pointer;
    }
    #table-request.table-info {
        background-color: #eef7f9  ;/* rgba(190, 229, 235,0.2) !important; */
    }
#table-request td.acid {
    padding-left: 1.5em;
    max-width:150px;
}
#table-request td.status {
    padding-right: 1.5em;
    width: 220px;
    font-size:small;

}
    #table-request span.request {
        background-color: #007bff;
        border-radius: 4px;
        padding: 4px 8px;
        color: white;
    }
    #table-request td.status > span > i {
      margin-right:0.5em;
    }
    #table-request tr.new-request {
        font-weight: 500;
    }
#table-request-modal th {
    background-color: #dee2e6;
}
/*.text-status {
    color :dimgrey;
}*/

/**MODAL*/
#RequestModal .modal-footer > button > i {
    margin-right: 0.5em;
}
#RequestModal .new-eobt, #RequestModal .new-ctot {
    font-weight: bold;
    color: #0f63bd;
}
#RequestModal i {
    margin-right: 5px;
}
#RequestModal .close {
    color: #fff;
}
/*Tooltip*/
.tooltip-inner {
    text-align: left;
    padding:1.25em;
    min-width: 200px;
}
    .tooltip-inner > hr {
        border-top: 1px solid white !important;
    }
    .tooltip-inner > p {
        margin-bottom: 0.25rem !important;
    }