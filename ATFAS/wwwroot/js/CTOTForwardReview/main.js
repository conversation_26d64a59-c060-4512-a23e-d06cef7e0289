﻿/* CTOT Forward Review*/
$(document).ready(function () {
    ctotForwardReview.init();
});


ctotForwardReview = {

    $btnSend: null,
    $inputComment: null,
    $loadingOverlay: null,
    $tableFlight: null,
    $tableRequest: null,
    $reqModal: null,


    init: function () {
        ctotForwardReview.$tableFlight = $('#table-flight');
        ctotForwardReview.$tableRequest = $('#table-request');
        ctotForwardReview.$btnSend = $('#btn-send');
        ctotForwardReview.$inputComment = $('#input-comment');
        ctotForwardReview.$loadingOverlay = $('#loading-overlay');
        ctotForwardReview.$reqModal = $('#RequestModal');

        ctotForwardReview.createTableFlight();
        ctotForwardReview.addEventBtnSend();
        ctotForwardReview.addEventCheckboxAll();
        ctotForwardReview.createTableRequest();
    },

    addEventBtnSend: function () {
        ctotForwardReview.$btnSend.on('click', function () {
            if (!ctotForwardReview.isCommentValidate()) {
                alert("Invalid comment for AFTN message: Please revise your comment.");
                return;
            }
            var $inputChecked = ctotForwardReview.$tableFlight.children('tbody').find('input:checked');
            var ids = [];
            $inputChecked.each(function () { ids.push($(this).closest('tr').data('id')); });
            if (ids.length < 1) return;
            ctotForwardReview.$loadingOverlay.show();
            $.ajax({
                type: 'POST',
                url: '/CTOTForwardReview/ForwardSlotMessage',
                data: {
                    idString: JSON.stringify(ids),
                    comment: ctotForwardReview.$inputComment.val().toUpperCase()
                },
                success: function (result, status, xhr) {
                    alert('Successful message forwarding!');
                    location.reload();
                },
                error: function () { alert('Server problem: Please try forwarding messages again.'); },
                complete: function () { ctotForwardReview.$loadingOverlay.hide(); }
            });
        });
    },


    addEventCheckboxAll: function () {
        ctotForwardReview.$tableFlight.children('thead').find('input').on('change', function () {
            var $this = $(this);
            var $tbodyInput = ctotForwardReview.$tableFlight.children('tbody').find('input:not(:disabled)')
            if ($this.is(':checked')) $tbodyInput.prop('checked', true);
            else $tbodyInput.prop('checked', false);
        });
    },
    createTableRequest: function () {
        $.ajax({
            type: 'GET',
            url: '/CTOTForwardReview/GetCTOTForwarding',
            success: function (result, status, xhr) {
                var $tbody = ctotForwardReview.$tableRequest.children('tbody');
                $tbody.html('');
                for (var i = 0; i < result.length; i++) {
                    ctotForwardReview.createTrRequestHelper(result[i], $tbody);
                }
                if (ctotForwardReview.$reqModal.hasClass('show')) {
                    //var $trmodal = $('#table-request-modal').children('tbody').find('tr');
                    $tbody.find('tr').each(function () {
                        //console.log('tr id = ' + $(this).data('info').id);
                        //console.log('modal fid = ' + ctotForwardReview.$reqModal.data('fid'));
                        if ($(this).data('info').id == ctotForwardReview.$reqModal.data('fid')) {
                            ctotForwardReview.createTrRequestModal($(this));
                        }
                    });
                }

            },
            error: function () { alert('Server problem: Please refresh the page to see updated messages.'); },
            complete: function () { setTimeout(function () { ctotForwardReview.createTableRequest(); }, 30000); }
        });
    },

    createTableFlight: function () {
        $('#loading-overlay').show();
        $.ajax({
            type: 'POST',
            url: '/CTOTForwardReview/GetForwardSlotMessageList',
            success: function (result, status, xhr) {
                $('#loading-overlay').hide();
                if (result == null)
                    return;
                var $tbody = ctotForwardReview.$tableFlight.children('tbody');
                var messages = result.messages;
                var messageTimes = result.messageTimes;
                for (var i = 0; i < messages.length; i++) {

                    ctotForwardReview.createTrHelper(messages[i], messageTimes[i], $tbody);
                }

            },
            error: function () { alert('Server problem: Please refresh the page to see updated messages.'); },
            complete: function () { setInterval(function () { ctotForwardReview.updateTableFlight(); }, 30000); }
        });
    },

    createTrRequestHelper: function (result, $tbody) { // create tbody of requests 
        var ctotreq = result.forwarding;
        var poc = result.poc;
        var user = result.userDetails
        var title = "";
        var stt = result.stt;
        ctotreq.stt = stt;
        var $tr = (ctotreq.status == 1 && !ctotreq.isUpdated) ? $('<tr class="new-request" id="' + ctotreq.id + '"></tr>') : $('<tr id="' + ctotreq.id + '"></tr>');
        //$tr.data('id', ctotreq.id);
        $tr.data('info', ctotreq);


        $tr.append($('<td class="acid"></td>').text(ctotreq.aircraftId));
        $tr.append($('<td class="new-eobt"></td>').text(moment(ctotreq.neweobt).format('DD / HH:mm')));
        $tr.append($('<td class="new-ctot"></td>').text(moment(ctotreq.neweobt).add(stt, 'minutes').format('DD / HH:mm')));
        var status = "";
        if (ctotreq.status == 1) status = '<span class="request" >CTOT Revision Request</span>';
        if (ctotreq.status == 2 && !ctotreq.isUpdated) status = '<span><i class="far fa-clock text-warning"></i>Request In Progress</span>';
        if (ctotreq.status == 2 && ctotreq.isUpdated) status = '<span class="text-success" ><i class="fa fa-check text-success"></i>New CTOT Delivered</span>';
        if (ctotreq.status == 3) status = '<span class="text-danger"><i class="fa fa-times text-danger"></i>Unable to Process</span>';

        $tr.data('status', status);
        $tr.append($('<td class="status"></td>').html(status));
        //if (isUpdate)
        //    $tbody.prepend($tr);
        //else
        $tbody.append($tr);
        ctotForwardReview.eventCtotRequest($tr);

        /* Tooltip */
        title = '<p>Requested by ' + ctotreq.username + '</p>';
        title += '<p>Email : ' + ((user.email == null) ? '-' : user.email )+ ' </p>';
        title += '<p>Tel : ' + ((user.phoneNumber == null) ? '-' : user.phoneNumber) + '</p><hr/>';
        title += '<p>Contact ' + poc.airline + '</p>';
        title += '<p>Email : ' + ((poc.emailAddress == null) ? '-' : poc.emailAddress ) + '</p>';
        title += '<p>Tel : ' + ((poc.phoneNumber == null) ? '-' : poc.phoneNumber) + '</p>';
        $tr.tooltip({
            title: title,
            placement: 'right',
            html: true
        });


    },
    createTrHelper: function (message, messageTime, $tbody) {
        var $tr = $('<tr></tr>');
        $tr.data('id', message.id);
        $tr.data('rawMessage', message.rawMessage);
        $tr.append($('<td><input type="checkbox"></td>'));
        if (message.isReviewed) {
            $tr.addClass('table-secondary');
            $tr.find('input').attr('disabled', 'true');
        }
        $tr.append($('<td></td>').text(message.messageType));
        $tr.append($('<td class="acid"></td>').text(message.aircraftId));
        $tr.append($('<td></td>').text(message.departure));
        $tr.append($('<td></td>').text(message.arrival));
        $tr.append($('<td></td>').text(messageTime.eobt));
        if (messageTime.isCtotFound && !message.isReviewed) {
            $tr.append($('<td></td>').html(messageTime.ctot + '<i class="fas fa-copyright ctot-badge"></i>'));
            $tr.addClass('table-secondary');
            $tr.addClass('text-muted');
            $tr.find('input').attr('disabled', 'true');
        }
        else
            $tr.append($('<td></td>').text(messageTime.ctot));
        $tr.append($('<td></td>').text(messageTime.newctot));
        $tr.append($('<td></td>').text(message.originator));
        $tr.append($('<td></td>').text(message.regul != null ? message.regul : '-'));
        $tr.append($('<td></td>').text(message.regcause != null ? message.regcause : '-'));
        $tbody.append($tr);
        $tr.children('.acid').tooltip({
            container: 'body',
            title: $tr.data('rawMessage')
        });
        $tr.find('.ctot-badge').tooltip({
            container: 'body',
            title: 'CTOT already exist'
        });
    },

    eventCtotRequest: function ($tr) {

        $tr.on('click', function () {
            var id = $(this).attr('id');
            ctotForwardReview.$reqModal.modal('show');
            ctotForwardReview.$reqModal.data('fid', id);
            var approveBtn = ctotForwardReview.$reqModal.find('.confirm-modal-btn');
            var terminateBtn = ctotForwardReview.$reqModal.find('.terminate-modal-btn');
            var atfmucomment = null//ctotForwardReview.$reqModal.find('.comment'); 

            ctotForwardReview.createTrRequestModal($tr);

            approveBtn.off('click').on('click', function () {
                var data = { cid: id, status: 2, atfmuComment: atfmucomment };
                //console.log('approvebtn  ' + JSON.stringify(data));
                ctotForwardReview.responseEobtAirline(data);
            });
            terminateBtn.off('click').on('click', function () {
                var data = { cid: id, status: 3, atfmuComment: atfmucomment };
                //console.log('terminatebtn  ' + JSON.stringify(data));
                ctotForwardReview.responseEobtAirline(data);
            });
        });
    },
    createTrRequestModal: function ($tr) {
        var ctot = $tr.data('info');
        var $tbodymodal = $('#table-request-modal').children('tbody');
        var $trmodal = $('<tr></tr>');
        var originator = ''; //cmpk
        $.ajax({
            type: 'GET',
            url: '/CTOTForwardReview/GetAtfmuByOriginator?originator=' + ctot.originator,
            success: function (result, status, xhr) {
                console.log("res +" + JSON.stringify(result));
                if (result != null) {
                    $('.og-name').text(result.name + ' (' + ctot.originator + ')');
                    $('.og-tel').text(result.phoneNumber);
                    $('.og-mail').text(result.emailAddress);
                }
                else {
                    $('.og-name').text('-');
                    $('.og-tel').text('-');
                    $('.og-mail').text('-');
                }
            },
            error: function () { console.log('cannot get originator'); }
        });
       
        $trmodal.data('id', $tr.data('id'));
        $trmodal.append($('<td></td>').text(ctot.aircraftId));
        $trmodal.append($('<td></td>').text(ctot.departure));
        $trmodal.append($('<td></td>').text(ctot.arrival));
        $trmodal.append($('<td></td>').text(moment(ctot.eobt).format('DD / HH:mm')));
        $trmodal.append($('<td class="new-eobt"></td>').text(moment(ctot.neweobt).format('DD / HH:mm')));
        $trmodal.append($('<td class="new-ctot"></td>').text(moment(ctot.neweobt).add(ctot.stt,'minutes').format('DD / HH:mm')));
        $trmodal.append($('<td></td>').text(moment(ctot.ctot).format('DD / HH:mm')));
        $trmodal.append($('<td></td>').text(ctot.originator));
        $trmodal.append($('<td></td>').text(ctot.regul));
        $trmodal.append($('<td></td>').html($tr.data('status')));
        $tbodymodal.html($trmodal);

       // $('.comment').html(ctot.atfmuComment);
        $('.request-date').html(moment(ctot.timeStamp).format('DD-MM-YYYY HH:mm') + ' UTC');
        if (ctot.status > 1)
            ctotForwardReview.$reqModal.find('.modal-footer').hide();
        else
            ctotForwardReview.$reqModal.find('.modal-footer').show();
    },
    responseEobtAirline: function (data) {
        $.ajax({
            type: 'POST',
            url: '/CTOTForwardReview/ResponseEobtairline',
            dataType: 'json',
            data: data,
            success: function (result) {
                var $reqModal = $('#RequestModal');
                var $tbody = ctotForwardReview.$tableRequest.children('tbody');
                $tbody.html('');
                for (var i = 0; i < result.length; i++) {
                    ctotForwardReview.createTrRequestHelper(result[i], $tbody);
                }
                $reqModal.modal('hide');
            },
            error: function (req, status, error) {
                console.log(error);
            }

        });
    },


    isCommentValidate: function () {
        var commentExp = /^[\w\d\\\s\|\?\:\.\,\'\=\+\/]{1,1500}$/
        var inputCommentVal = ctotForwardReview.$inputComment.val();
        if (inputCommentVal != '' && inputCommentVal.toUpperCase().search(commentExp) == -1) return false;
        return true;
    },
    /* updateTableRequest: function () {
         $.ajax({
             type: 'GET',
             url: '/CTOTForwardReview/GetCTOTForwarding',
             success: function (result, status, xhr) {
                 var $tbody = ctotForwardReview.$tableRequest.children('tbody');
                 var messages = result;
                 var idSet = {};
                 var lastId = 0;
                 for (var i = 0; i < messages.length; i++) idSet[messages[i].id] = true;
                 $tbody.children('tr').each(function () {
                     var $this = $(this);
                     var id = $this.data('id');
                     lastId = id;
                     if (!idSet[id]) $this.remove();
                 });
                 for (var i = 0; i < messages.length; i++) {
                     if (messages[i].id > lastId) ctotForwardReview.createTrRequestHelper(messages[i], $tbody, true)
                 }
             },
             error: function () { alert('Server problem: Please refresh the page to see updated messages.'); }
         });
 
     },*/

    updateTableFlight: function () {
        //$('#loading-overlay').show();
        $.ajax({
            type: 'POST',
            url: '/CTOTForwardReview/GetForwardSlotMessageList',
            success: function (result, status, xhr) {
                //$('#loading-overlay').hide();
                $('.last-update').html('Last Updated : ' + moment.utc().format('HH:mm:ss') + ' UTC');
                if (result != null) {
                    var messages = result.messages;
                    var messageTimes = result.messageTimes;
                    var idSet = {};
                    var $tbody = ctotForwardReview.$tableFlight.children('tbody');
                    var lastId = 0;
                    for (var i = 0; i < messages.length; i++) idSet[messages[i].id] = true;
                    $tbody.children('tr').each(function () {
                        var $this = $(this);
                        var id = $this.data('id');
                        lastId = id;
                        if (!idSet[id]) $this.remove();
                        $(this).removeData('tooltip');
                    });
                    $('.tooltip').remove();
                    for (var i = 0; i < messages.length; i++) {
                        if (messages[i].id > lastId) ctotForwardReview.createTrHelper(messages[i], messageTimes[i], $tbody);
                    }
                }
            },
            error: function () { console.log('Server problem: ctotForwardReview.updateTableFlight'); }
        });
    }

}