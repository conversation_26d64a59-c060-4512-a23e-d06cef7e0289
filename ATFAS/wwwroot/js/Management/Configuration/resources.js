﻿/* --------------------------------------------------------------
   Resources page – add / edit via modal dialog
-------------------------------------------------------------- */
$(function () {

    /* DATA ----------------------------------------------------- */
    let currentMode = 'add';          // 'add' | 'edit'
    let currentRow = null;           // row obj while editing

    window.attachmentFormatter = (val, row) => {
        // File mode → link to the stored file
        if (row.type === 'file') {
            return val
                ? `<a href="/${row.fileRelPath}" target="_blank">${val}</a>`
                : '';
        }
        // Link mode → clickable URL (opens in new tab)
        if (row.type === 'link') {
            return `<a href="${row.linkUrl}" target="_blank">${row.linkUrl}</a>`;
        }
        return '';
    };

    /* DELETE (🗑) ---------------------------------------------- */
    window.delEvents = {
        'click .del-btn': function (e, value, row) {
            if (!confirm('Delete this resource?')) return;
            $('#loading-overlay').show();

            $.ajax({
                url: '/Management/Configuration/DeleteResource/' + row.id,
                type: 'DELETE',
                success: loadResources,
                error: ajaxError
            });
        }
    };

    /* EDIT (✏) ------------------------------------------------- */
    window.editEvents = {
        'click .edit-btn': function (e, value, row) {
            currentMode = 'edit';
            currentRow = row;
            openModal(row);
        }
    };

    /* TABLE FORMATTERS ---------------------------------------- */
    window.dateFormatter = val =>
        val ? moment(val).utc().format('YYYY-MM-DDTHH:mm:ss') : '';

    window.fileNameFormatter = (v, row) =>
        v ? `<a href="/${row.fileRelPath}" target="_blank">${v}</a>` : '';

    window.editFormatter = () =>
        '<button class="btn btn-sm btn-outline-info edit-btn">' +
        '<i class="fas fa-pencil-alt"></i></button>';

    window.delFormatter = () =>
        '<button class="btn btn-sm btn-outline-danger del-btn">' +
        '<i class="fas fa-trash-alt"></i></button>';


    /* BOOTSTRAP-TABLE ----------------------------------------- */
    loadResources();

    function loadResources() {
        $.getJSON('/Management/Configuration/GetResources')
            .done(rows => {
                $('#loading-overlay').hide();

                $('#resource-table').bootstrapTable('destroy')
                    .bootstrapTable({
                        data: rows,
                        sortName: 'serial',
                        sortOrder: 'asc'
                    });
            })
            .fail(ajaxError);
    }

    /* ADD (+) -------------------------------------------------- */
    $(document).on('click', '.btn-add-resource', () => {
        currentMode = 'add';
        currentRow = null;
        openModal();
    });


    /* MODAL ---------------------------------------------------- */
    const $modal = $('#resourceModal');
    const $form = $('#resourceForm')[0];
    const $dz = $('#dz');
    const $fileInput = $dz.find('input[type=file]');

    function openModal(row = null) {
        // reset form
        $form.reset();
        if (currentMode === 'edit') {
            $('#resourceType').val(row.type).trigger('change');
            if (row.type === 'link') $form.linkUrl.value = row.linkUrl;
            // file branch stays unchanged
        } else {
            $('#resourceType').val('file').trigger('change');
        }
        $dz.removeClass('hover')
            .find('.dz-text').text('Drag & drop file here or click to browse');
        $('.current-file').addClass('d-none');

        if (currentMode === 'add') {
            $('.modal-title').text('Add Resource');
            $form.file.required = true;

        } else {                       // ---- EDIT ----
            $('.modal-title').text('Edit Resource');
            $form.id.value = row.id;
            $form.displayName.value = row.displayName;
            $form.file.required = false;

            if (row.fileName) {
                $('.current-file').removeClass('d-none')
                    .find('span').text(row.fileName);
            }
        }
        $modal.modal('show');
    }

    /* Drop-zone UX */
    $dz.on('dragover', e => { e.preventDefault(); $dz.addClass('hover'); })
        .on('dragleave dragend', () => $dz.removeClass('hover'))
        .on('drop', e => {
            e.preventDefault(); $dz.removeClass('hover');
            if (e.originalEvent.dataTransfer.files.length)
                $fileInput[0].files = e.originalEvent.dataTransfer.files;
            showPickedFileName();
        });
    $fileInput.on('change', showPickedFileName);
    function showPickedFileName() {
        const f = $fileInput[0].files[0];
        if (f) $dz.find('.dz-text').text(f.name);
    }

    /* SAVE (create / update) ---------------------------------- */
    $('#resourceForm').on('submit', function (e) {
        e.preventDefault();

        const url = currentMode === 'add'
            ? '/Management/Configuration/CreateResource'
            : '/Management/Configuration/UpdateResource';

        const data = new FormData(this);                 // grabs id / name / file
        if (currentMode === 'edit' && !data.has('file'))
            data.set('file', new Blob());                  // always send key

        $('#loading-overlay').show();
        $.ajax({
            url,
            type: 'POST',
            data,
            processData: false,
            contentType: false
        }).done(() => {
            $modal.modal('hide');
            loadResources();
        }).fail(ajaxError);
    });

    /* toggle between File and Link mode ------------------------- */
    $('#resourceType').on('change', function () {

        const mode = this.value;              // 'file' | 'link'

        // show / hide the two form-groups
        $('.file-group').toggleClass('d-none', mode !== 'file');
        $('.url-group').toggleClass('d-none', mode !== 'link');

        // HTML5 “required” flags
        $form.file.required = (mode === 'file');
        $form.linkUrl.required = (mode === 'link');
    });

    /* GENERIC AJAX ERROR -------------------------------------- */
    function ajaxError(xhr) {
        $('#loading-overlay').hide();
        alert(xhr.responseText || 'Server error');
        console.error(xhr);
    }
});


