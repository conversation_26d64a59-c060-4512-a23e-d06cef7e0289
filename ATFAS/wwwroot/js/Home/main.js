﻿/* Home page*/
var announce = [];
var fileid = null;
var latest = "";
var ongoing = "";
var td = [];
var refreshRate = 60000;
var tooltipThreshold = 7;
var baseMap = L.tileLayer('https://server.arcgisonline.com/ArcGIS/rest/services/Canvas/World_Light_Gray_Base/MapServer/tile/{z}/{y}/{x}',
    {
        attribution: 'Tiles &copy; Esri &mdash; Esri, DeLorme, NAVTEQ',
    });// L.tileLayer('http://tile.openstreetmap.org/{z}/{x}/{y}.png', { atribution: 'Map data &copy; OSM.org' });
var adpLayer = L.featureGroup();
var capaLayer = L.featureGroup();
var trackLayer = L.featureGroup();
var sectorLayer = L.featureGroup();
var map = L.map('mapid', {
    center: [13.756, 100.5],
    zoom: 5, //4
    layers: [baseMap, sectorLayer, capaLayer, adpLayer, trackLayer]
});
var baseLayers = {
    "default": baseMap
};

var overlays = {
    "Demand/Capacity": capaLayer,
    "Sector": sectorLayer,
    "ADP": adpLayer,
    "Flight Track": trackLayer
};

var LeafIcon = L.Icon.extend({
    options: {
        iconSize: [24, 24],
        iconAnchor: [12, 12],
        popupAnchor: [-3, -3],
    }
});
var mapLayerGroups = [];

var greenIcon = new LeafIcon({ iconUrl: '/images/traffic-green-round.png' }),
    redIcon = new LeafIcon({ iconUrl: '/images/traffic-red-round.png' }),
    grayIcon = new LeafIcon({ iconUrl: '/images/traffic-none-round.png' }),
    yellowIcon = new LeafIcon({ iconUrl: '/images/traffic-yellow-round.png' }),
    adpIcon = new LeafIcon({ iconUrl: '/images/adp-local.png' });
adplocalIcon = new LeafIcon({ iconUrl: '/images/adp-local.png' });

var mapAtfmu = [];
var tdAirports = [];
var tdSectors = [];
var tdCapacities = [];
var capacityModalTimeout;

//filter Airoirt / Sector
let airportDataset = [];
let airportAllSelected = false;
let airportPreselected = JSON.parse(localStorage.getItem('selectedAirports') || '[]');

let sectorDataset = [];
let sectorAllSelected = false;
let sectorPreselected = JSON.parse(localStorage.getItem('selectedSectors') || '[]');


$(document).ready(function () {
    map.invalidateSize();

    displayTime();
    getAnnouncement();
    displayWidget();
    getTrafficDemandAirport();
    getTrafficDemandSector();
    loadResourcesWidget();
    setInterval(loadResourcesWidget, 300_000);


    L.control.layers(baseLayers, overlays).addTo(map);
    L.icon = function (options) {
        return new L.Icon(options);
    };

    map.on('zoomend', function () {
        if (map.getZoom() < tooltipThreshold) {
            $(".kml-popup").css("display", "none")
        } else {
            $(".kml-popup").css("display", "block")
        }
    })

    /*Select2*/
    $('.select2').select2();

    /*Color Selector*/
    $('#colorselector').colorselector();
    $("body").tooltip({
        selector: '[data-toggle="tooltip"]'
    });

    /*Sortable*/
    $('.connectedSortable').sortable({
        placeholder: 'sort-highlight',
        connectWith: '.connectedSortable',
        handle: '.card-header, .nav-tabs,.sortable',
        forcePlaceholderSize: true,
        zIndex: 999999
    });
    $('.connectedSortable .card-header ,.connectedSortable .sortable').css('cursor', 'move');

    $('#modalAnnounceAll').on('shown.bs.modal', function () {
        $('#atable').bootstrapTable('resetView')
    });
    $('#modalAdpAll').on('shown.bs.modal', function () {
        $('#adptable').bootstrapTable('resetView')
    });

    var current = moment.utc();

    //Initialize Select2 Elements

    $("#targetDate").datepicker().datepicker("setDate", new Date(current));
    $("#targetDate").datepicker().datepicker('disable');
    /*$("#targetDate").datepicker({
        onSelect: function (dateStr) {
            var diff = Math.abs(moment(dateStr).startOf('day').diff(current.startOf('day'), 'days'));
            if (moment(dateStr).format('YYYY-MM-DD') > current.format('YYYY-MM-DD')) {
                $('#targeticon').html("D - " + (diff + 1) + "<br><span class='small-note'>tactical</span>");
            }
            if (moment(dateStr).format('YYYY-MM-DD') < current.format('YYYY-MM-DD')) {
                document.getElementById('targeticon').innerHTML = "D + " + diff;
            }
        }
    });*/
    //$('#targetDate').datePicker().datepicker("setDate", current);
    var sidelist = "";
    $('.hp-widget').each(function () {
        var name = $(this).attr('name');
        var id = $(this).attr('id');
        if ($(this).css('display') == 'none') {
            sidelist += " <div class='mb-1'><input type='checkbox' value=" + id + " class='mr-1 hp-sidelist'><span>" + name + "</span></div>";
        }
        else {
            sidelist += " <div class='mb-1'><input type='checkbox' value=" + id + " class='mr-1 hp-sidelist' checked><span>" + name + "</span></div>";
        }

    });
    $('.sidelist-wrapper').append(sidelist);

    $('.hp-sidelist').on('click', function () {
        if ($(this).is(':checked')) {
            $('#' + $(this).val()).show();
        }
        else {
            $('#' + $(this).val()).hide();
        }
    });

    $('.btn-tool').on('click', function () {
        if ($(this).attr("data-card-widget") == "remove") {
            var hpName = $(this).closest("div.hp-widget").attr('id');
            $("input[value='" + hpName + "']").prop('checked', false);
        }
    });
    $('hp-widget').attr("data-card-widget")

    $(document).on("click", ".announce-btn", function () {
        var id = $(this).val();
        getAnnouncementById($(this).attr('value'));
    });
    $(document).on("click", ".atfmu-btn", function () {

        var id = $(this).attr('id');
        var ob = _.find(mapAtfmu, ["location", id]);
        var color = 'primary';
        if (ob.isLocal)
            color = 'primary';
        $('#modalAtfmuHeader').removeClass();
        $('#modalAtfmuHeader').addClass('modal-header');
        $('#modalAtfmuHeader').addClass('bg-' + color);
        var content = "<div class='bg-" + color + " atfmu-body-header'>";

        content += "<h5 class='text-white'>" + ob.name + "</h5>";
        content += "<div class='text-white-50 text-sm'>Contact Information</div></div>";

        content += "<div class='atfmu-contact-wrapper'>";
        content += "<div><ul class='ml-4 mb-0 fa-ul contact-ul'>";
        content += "<li><span class='fa-li'><i class='far fa-envelope'></i></span>" + ob.emailAddress + "</li>";
        content += "<li><span class='fa-li'><i class='fas fa-phone-alt'></i></span>" + ob.phoneNumber + "</li>";
        content += "</ul></div>";

        $('.atfmu-detail').html(content);
    });

    /* Demand/Capacity */
    $(document).on("click", ".modal-demand", function () {
        var trafficAreaId = ($(this).attr("id") == "AirportDemandWidget") ? 1 : 3;
        getAllTrafficDemand(trafficAreaId);
    });

    /* Filter Sector/ Airport on Map */
    $(document).on('click', '#resetBtn', function () {

        var datasetAirport = JSON.parse(localStorage.getItem("allAirports"));
        var datasetSector = JSON.parse(localStorage.getItem("allSectors"));

        localStorage.setItem('selectedAirports', JSON.stringify(datasetAirport));
        localStorage.setItem('selectedSectors', JSON.stringify(datasetSector));

        renderAirportLayer();
        renderSectorLayer();

        // Re-check previously selected items from storage
        $('#selectAllCheckboxAirport').prop('checked', true);
        $('#selectAllCheckboxSector').prop('checked', true);
      
        const savedAirportIds = datasetAirport.map(i => i.designator);
        const savedSectorIds = datasetSector.map(i => i.designator);
        $('.item-checkbox-airport').each(function () {
            if (savedAirportIds.includes($(this).val())) {
                $(this).prop('checked', true);
            }
        });
        $('.item-checkbox-sector').each(function () {
            if (savedSectorIds.includes($(this).val())) {
                $(this).prop('checked', true);
            }
        });
    });
    $(document).on('click', '#applyBtn', function () {
        console.log('apply click' + JSON.parse(localStorage.getItem('selectedAirports')));
        const selectedSectorIds = $('.item-checkbox-sector:checked').map(function () {
            return $(this).val();
        }).get();
        const selectedAirportIds = $('.item-checkbox-airport:checked').map(function () {
            return $(this).val();
        }).get();

        // Save to localStorage (or use localStorage)
        var datasetAirport = JSON.parse(localStorage.getItem("allAirports"));
        const selectedAirportObjects = datasetAirport.filter(item => selectedAirportIds.includes(item.designator));
        var datasetSector = JSON.parse(localStorage.getItem("allSectors"));
        const selectedSectorObjects = datasetSector.filter(item => selectedSectorIds.includes(item.designator));
 
        localStorage.setItem('selectedAirports', JSON.stringify(selectedAirportObjects));
        localStorage.setItem('selectedSectors', JSON.stringify(selectedSectorObjects));
        renderAirportLayer();
        renderSectorLayer();

    });
    $(document).on('input','#searchBoxAirport', function () {
        const term = $(this).val().toLowerCase();
        const filtered = dataset.filter(item => item.designator.toLowerCase().includes(term));
        renderCheckboxes(filtered, '#dropdownAirports');
    });
    $(document).on('input', '#searchBoxSector', function () {
        const term = $(this).val().toLowerCase();
        const filtered = dataset.filter(item => item.designator.toLowerCase().includes(term));
        renderCheckboxes(filtered, '#dropdownSectors');
    });

    $(document).on('change', '#selectAllCheckboxAirport', function () {
        const checked = $(this).is(':checked');
        $('.item-checkbox-airport').prop('checked', checked);
    });
    $(document).on('change', '.item-checkbox-airport', function () {
        const allChecked = $('.item-checkbox-airport').length === $('.item-checkbox-airport:checked').length;
        $('#selectAllCheckboxAirport').prop('checked', allChecked);
    });
    $(document).on('change', '#selectAllCheckboxSector', function () {
        const checked = $(this).is(':checked');
        $('.item-checkbox-sector').prop('checked', checked);
    });
    $(document).on('change', '.item-checkbox-sector', function () {
        const allChecked = $('.item-checkbox-sector').length === $('.item-checkbox-sector:checked').length;
        $('#selectAllCheckboxSector').prop('checked', allChecked);
    });

    $("#modalCapacity").on('hidden.bs.modal', function (e) {
        clearTimeout(capacityModalTimeout);
    })


    $("input[name='options']").change(function () {
        if ($(this).is(':checked')) {
            if (($(this).val()) == 1)
                $('#announceBox').html(ongoing);

            if (($(this).val()) == 0)
                $('#announceBox').html(latest);
        }
    });

});
function poll(fname, refreshRate) {
    setTimeout(fname, refreshRate);
}

function getTotalStats() {
    $.ajax({
        url: "/Home/getTotalStats",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        complete: function () {
            poll(getTotalStats, refreshRate);
            $('.last-update').html('Last Update: ' + moment.utc().format('DD/MM/YYYY HH:mm') + ' UTC');
        },
        success: function (result) {
            var planded = 0;
            var pairborne = 0;
            var pdelay = 0;

            if (result.flightTotal > 0) {
                planded = Math.round((result.flightLanded / result.flightTotal) * 100) + '%';
                pairborne = Math.round((result.flightAirborned / result.flightTotal) * 100) + '%';
                pdelay = Math.round((result.flightDelayCount / result.flightTotal) * 100) + '%';
            }

            $('#flightTTL').html(result.flightTotal);
            $('#flightLanded').html(result.flightLanded);
            $('#flightAirborne').html(result.flightAirborned);
            $('#flightLandedProgressTxt').html(planded + "  LANDED");
            $('#flightAirborneProgressTxt').html(pairborne + "  AIRBORNE");
            $('#flightAvgDLT').html(Math.round(result.flightDelayCM));
            $('#flightTtlDLT').html(result.flightDelayTTL);

            $('#flightTtlDL').html(result.flightDelayCount);
            $('#fdelaytxt').html(pdelay + "  Flights Delay");

            $('#flightLandedProgress').css("width", planded);
            $('#flightAirborneProgress').css("width", pairborne);
            $('#fdelay').css("width", pdelay);



        },
        error: function (result) {
            alert("Error: Cannot Load Summary");
        }
    });

}
function live() {
    var refresh = 1000; // Refresh rate in milli seconds
    mytime = setTimeout('displayTime()', refresh);
}
function displayWidget() {
    $.ajax({
        url: "api/FrontEnd/",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",

        success: function (result) {
            console.log('permission =' + JSON.stringify(result));
            var sidemenu = "<div class='navbar-custom-menu'><ul class='nav navbar-nav'>";
            //sidemenu += "<li><a class='nav-link' style='color:#fff;' data-slide='true' href='/Management' target='_blank' role='button'><i class='fas fa-cogs'></i></a></li>";
            sidemenu += "<li><a class='nav-link' style='color:#fff;' data-widget='control-sidebar' data-slide='true' href='#' role='button'><i class='fas fa-th-large'></i></a></li>";
            sidemenu += "</ul></div>";
            $('.sidebarnav').append(sidemenu);

        },
        error: function (result) {
            $('#Newsedit').hide();
            var sidemenu = "<div class='navbar-custom-menu'><ul class='nav navbar-nav'>";
            // sidemenu += "<li><a class='nav-link' style='color:#fff;' data-slide='true' href='/Management' target='_blank' role='button'><i class='fas fa-cogs'></i></a></li>";
            sidemenu += "<li><a class='nav-link' style='color:#fff;' data-widget='control-sidebar' data-slide='true' href='#' role='button'><i class='fas fa-th-large'></i></a></li>";
            sidemenu += "</ul></div>";
            $('.sidebarnav').append(sidemenu);
            //alert("Error: Cannot Load Summary");
        }
    });
    //getTotalStats();
    getFlightTrack();
    getAdp();
    getActiveGdp();
    $('.last-update').html('Last Update: ' + moment.utc().format('DD/MM/YYYY HH:mm') + ' UTC');

}

function displayTime() {
    var d = moment.utc();
    var dateformat = "ddd, D MMM YYYY ";
    var timeformat = "HH:mm:ss";
    document.getElementById('livedate').innerHTML = d.format(dateformat);
    document.getElementById('livetime').innerHTML = d.format(timeformat) + " UTC";
    live(); //reomve live() for test 
}
function getActiveGdp() {
    $.ajax({
        url: "/Home/GetActiveGdp",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        complete: function () {
           poll(getActiveGdp, refreshRate);
        },
        success: function (result) {
            $('.measure-ul').html('');
            console.log('active gdp');
            console.log(result);
            if (result.length < 1 || result == null) {
                $('.measure-ul').append('<li class="list-group-item border-bottom" > No Active ATFM Measure </li>');
                return;
            }

            for (var i = 0; i < result.length; i++) {
                var actives = '<li class="list-group-item border-bottom" ><div class="row">';
                actives += '<div class="measure-name col-md-4" style="">' + result[i].designator + '</div>';
                actives += '<div class="col-md-4"><i class="far fa-clock" style="margin-right: 0.5em;"></i>' + formatGdpTimeDisplay(result[i]) + '</div> ';
                actives += '<div class="col-md-4"><i class="fas fa-plane" style="margin-right: 0.5em;"></i>' + formatGdpPoint(result[i]) + '</div>';
                actives += ' </div></li ></ul >';
                $('.measure-ul').append(actives);
            }
                                                
        },
        error: function (result) {
            console.log("Error: Cannot Load Active GDP");
        }
    })
}
function formatGdpPoint(gdp) {
    if (gdp.trafficAreaId == 1)
        return "Airport " + gdp.point;
    if (gdp.trafficAreaId == 2)
        return "Waypoint " + gdp.point;
    if (gdp.trafficAreaId == 3)
        return "Sector " + gdp.point;
    if (gdp.trafficAreaId == 4)
        return "Airspace " + gdp.point;
}
function formatGdpTimeDisplay(gdp) {
    var start = new Date(gdp.startTime);
    var end = new Date(gdp.endTime);
    var startHH = start.getHours();
    var startMM = start.getMinutes();
    var endHH = end.getHours();
    var endMM = end.getMinutes();
    return (startHH < 10 ? '0' + startHH : startHH) + (startMM < 10 ? '0' + startMM : startMM) + ' - ' + (endHH < 10 ? '0' + endHH : endHH) + (endMM < 10 ? '0' + endMM : endMM) +' UTC';

}
       
function getFlightTrack() {
    /* trackLayer.eachLayer(function (layer) {
         trackLayer.removeLayer(layer);
     });*/

    $.ajax({
        url: "/api/radarservice",
        type: "GET",
        dataType: "xml",
        complete: function () {
            poll(getFlightTrack, refreshRate);
        },
        success: function (res) {
            trackLayer.eachLayer(function (layer) {
                trackLayer.removeLayer(layer);
            });

            var track = new L.KML(res);
            trackLayer.addLayer(track);
            if (map.getZoom() < tooltipThreshold) {
                $(".kml-popup").css("display", "none")
            } else {
                $(".kml-popup").css("display", "block")
            }
            //const bounds = trackLayer.getBounds();
            //map.fitBounds(bounds);
        },
        error: function (result) {
            console.log('error loading flight list : ' + JSON.stringify(result));
        }
    });
}
function getTrafficDemandSector() {
    /* sectorLayer.eachLayer(function (layer) {
         sectorLayer.removeLayer(layer);
     });*/

    $.ajax({
        url: "/Home/getTrafficDemandSectorMap",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        complete: function () {
            poll(getTrafficDemandSector, refreshRate);
        },
        success: function (result) {

            dataset = result;
            localStorage.setItem('allSectors', JSON.stringify(dataset));
            if (sectorPreselected.length == 0) {
                // Preselect all
                const allIds = dataset.map(item => item.designator);
                const allItems = dataset;
                localStorage.setItem('selectedSectors', JSON.stringify(allItems));

                sectorPreselected = JSON.parse(localStorage.getItem("selectedSectors"));;
            }
            renderCheckboxes(dataset, '#dropdownSectors');
            //maps
            sectorLayer.eachLayer(function (layer) {
                sectorLayer.removeLayer(layer);
            });
            const storedSectors = JSON.parse(localStorage.getItem("selectedSectors"));
            console.log(storedSectors);
            if (storedSectors.length > 0)
                tdSectors = storedSectors; //result -> selectedAirports


            for (var i = 0; i < tdSectors.length; i++) {
                // if (tdSectors[i].airspaceType == 16) {
                var feature = {
                    type: "Feature", properties: { id: tdSectors[i].id, name: tdSectors[i].name },
                    geometry: Terraformer.WKT.parse(tdSectors[i].geographyString)
                };
                var style = getStyle(16);//tdSectors[i].airspaceType 
                if (parseInt(tdSectors[i].capacity) < 1 || parseInt(tdSectors[i].capacity) == null) // ถ้าไม่มี capacity ให้ใส่สีเทา
                    style.color = '#2a2b2b';
                else if (parseInt(tdSectors[i].arrival) >= parseInt(tdSectors[i].capacity) && parseInt(tdSectors[i].capacity) > 0)
                    style.color = '#DC143C';
                else if (parseInt(tdSectors[i].arrival) >= (parseInt(tdSectors[i].capacity) * 0.8) && parseInt(tdSectors[i].capacity) > 0)
                    style.color = '#E0E01D';

                //displayAirspaceMap(feature, style);
                var mapStyle = {
                    weight: 1,
                    color: "#999",
                    opacity: 1,
                    fillColor: style.color,
                    fillOpacity: 0.1
                };
                var geoJson = L.geoJSON(feature, {
                    style: mapStyle,
                    onEachFeature: onEachFeature
                });

                function onEachFeature(feature, layer) {
                    if (feature.properties && feature.properties.name) {
                        mapLayerGroups[feature.properties.id] = layer;
                        layer.bindTooltip(feature.properties.name,
                            {
                                permanent: true,
                                direction: "center",
                                maxWidth: "auto",
                                className: 'labelstyle-sector'
                            }
                        );
                    }
                }

                geoJson.addTo(sectorLayer);
                // }
            }


        },
        error: function (result) {
            console.log("Error: Cannot Load Static Airspaces");
        }
    });
}
function getTrafficDemandAirport() {
    /*capaLayer.eachLayer(function (layer) {
        capaLayer.removeLayer(layer);
    });*/

    $.ajax({
        url: "/Home/GetTrafficDemandAirportMap",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        complete: function () {
            poll(getTrafficDemandAirport, refreshRate);
        },
        success: function (result) {

            dataset = result;
            localStorage.setItem('allAirports', JSON.stringify(dataset));
            if (airportPreselected.length == 0) {
                // Preselect all
                const allIds = dataset.map(item => item.designator);
                const allItems = dataset;
                localStorage.setItem('selectedAirports', JSON.stringify(allItems));
               
                airportPreselected = JSON.parse(localStorage.getItem("selectedAirports"));;
            }
            renderCheckboxes(dataset, '#dropdownAirports');
           
            //maps
            capaLayer.eachLayer(function (layer) {
                capaLayer.removeLayer(layer);
            });
            const storedAirports = JSON.parse(localStorage.getItem("selectedAirports"));
            console.log(storedAirports);
            if (storedAirports.length > 0)
                tdAirports = storedAirports; //result -> selectedAirports

            for (var i = 0; i < tdAirports.length; i++) {
                var icon = grayIcon;
                if (tdAirports[i].arrival != null && tdAirports[i].departure != null) {
                    if (parseInt(tdAirports[i].capacity) > 0) {
                        icon = greenIcon;
                        if ((parseInt(tdAirports[i].arrival) + parseInt(tdAirports[i].departure)) > parseInt(tdAirports[i].capacity)) //change from >= to > Capacity as Sectors
                            icon = redIcon;
                        else if ((parseInt(tdAirports[i].arrival) + parseInt(tdAirports[i].departure)) >= (parseInt(tdAirports[i].capacity) * 0.8))
                            icon = yellowIcon;
                    }
                }
                console.log("loooop" + JSON.stringify(tdAirports[i]));
                L.marker([toDecimalLat(tdAirports[i].latDegree), toDecimalLng(tdAirports[i].lonDegree)], { icon: icon }).addTo(capaLayer).bindPopup("<div class='text-bold'>" + tdAirports[i].designator + "</div>");
            }
            //renderAirportLayer();
            //L.control.layers(baseLayer,capaLayer).addTo(map);
        },
        error: function (result) {
            console.log("Error: Cannot Load Traffic Demand");
        }
    });
}
function getAllTrafficDemand(trafficId) {
    $('#loading-overlay').show();
    var tr = "";
    var thr = "<tr class='time-interval'><td style='width:16%'></td>";
    var $title = $('#modalCapacity').find('.modal-title');
    $title.text('');
    $('.capacity-tbody').html('');
    var d = new Date();
    var hour = d.getUTCHours();
    var timeBlock = [];
    for (var i = 0; i < 12; i++) timeBlock[i] = hour + i;

    console.log('getAllTrafficDemand' + trafficId);
    $.ajax({
        url: "/Home/GetAllTrafficDemandList",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        complete: function () {
            //capacityModalTimeout = setTimeout(getAllTrafficDemand, refreshRate, trafficId);
        },
        success: function (result) {         
            var isDisplay = false;
            var displayIndex;
            if (result.length > 0)
                tdCapacities = result;

            $('.capacity-tbody').html('');
            $('#loading-overlay').hide();
            $('.update-td-table').html("Data Updated On : " + moment().utc().format('DD/MM/YYYY HH:mm') + " UTC");
            var size = 0;
            var times = [];
            var title = (trafficId == 1) ? 'Airport' : 'Sector';
            $title.text(title + ' Demand/Capacity 12 hrs onwards');
            //if (tdCapacities[0] != null) {

            // times = tdCapacities[0].timeInterval.split(',');
            //size = times.length;             
            // }
            // else
            //     $('.capacity-tbody').append('<tr><td>No ' + title + ' Demand/Capacity to Display</td></tr>');



            for (var i = 0; i < tdCapacities.length; i++) {
                if (tdCapacities[i] != null && tdCapacities[i].trafficAreaId == trafficId) {
                    isDisplay = true;
                    displayIndex = i;
                    times = tdCapacities[i].timeInterval.split(',');
                    size = times.length;

                    var dep = (tdCapacities[i].departure == null) ? getZeros(size) : tdCapacities[i].departure.split(',');
                    var arr = tdCapacities[i].arrival.split(',');
                    var capa = (tdCapacities[i].capacity == null) ? getZeros(size) : tdCapacities[i].capacity.split(',');

                    tr += "<tr>";
                    tr += "<td style='width:16%'>" + tdCapacities[i].designator + "</td>";

                    for (var j = 0; j < arr.length - 1; j++) {
                        var classCurrent = "";
                        var depint = parseInt(dep[j]);
                        var arrint = parseInt(arr[j]);
                        var capaint = parseInt(capa[j]);

                         var from = times[j] + ':00';
                         var to = '- ' + times[j] + ':59';

                       /* var from = timeBlock[j] + ':00';
                        var to = '- ' + timeBlock[j] + ':59';*/

                        if (hour == times[j])
                        //if (hour == timeBlock[j])
                            classCurrent = "table-current";

                      
                       // if (timeBlock[j] != times[j]) {
                       //     console.log("NODATA !!!! = timeBlock[j] = " + timeBlock[j] + "    times[j] = " + times[j]);
                       //     tr += "<td class='box " + classCurrent + "'><div class='box-div bg-gray' data-toggle='tooltip' data-html='true' title='<p>no data</p>'></div></td>";
                       // }
                       // else {
                            if (capaint == 0)
                                tr += "<td class='box " + classCurrent + "'><div class='box-div bg-gray' data-toggle='tooltip' data-html='true' title='<p><u>" + from + to + "</u><p><p>" + (depint + arrint) + "/" + capaint + "</p>'></div></td>";
                            else {
                                if ((depint + arrint) < (capaint * 0.8))
                                    tr += "<td class='box " + classCurrent + "'><div class='box-div bg-green' data-toggle='tooltip' data-html='true' title='<p><u>" + from + to + "</u><p><p>" + (depint + arrint) + "/" + capaint + "</p>'></div></td>";
                                else if ((depint + arrint) >= (capaint * 0.8) && (depint + arrint) < capaint)
                                    tr += "<td class='box " + classCurrent + "'><div class='box-div bg-yellow' data-toggle='tooltip' data-html='true' title='<p><u>" + from + to + "</u><p><p>" + (depint + arrint) + "/" + capaint + "</p>'></div></td>";
                                else
                                    tr += "<td class='box " + classCurrent + "'><div class='box-div bg-red'  data-toggle='tooltip' data-placement='top' data-html='true' title='<p><u>" + from + to + "</u><p><p>" + (depint + arrint) + "/" + capaint + "</p>'></div></td>";
                            }
                        //}
                    }
                    //count = arr.length;
                    tr += "</tr>";
                }//if
            }//for

            if (moment(tdCapacities[displayIndex].timeStamp) - d.getUTCDate > 12) isDisplay = false;

            if (isDisplay) $('.update-td-table').html("Data Updated On : " + moment(tdCapacities[displayIndex].timeStamp).format('DD/MM/YYYY HH:mm') + " UTC");
            else $('.capacity-tbody').append('<tr><td>No ' + title + ' Demand/Capacity to Display</td></tr>');

            for (var k = 0; k < times.length; k++) {
                 if (hour == times[k])
                     thr += "<td class='table-current'><div>" + times[k] + ":" + "00</div></td>";
                 else
                     thr += "<td><div>" + times[k] + ":" + "00</div></td>";
             }
            /* for (var k = 0; k < timeBlock.length; k++) {
                console.log('Hour =' + hour + '   timeBlock = ' + timeBlock[k]);
                if (hour == timeBlock[k])
                    thr += "<td class='table-current'><div>" + timeBlock[k] + ":" + "00</div></td>";
                else
                    thr += "<td><div>" + timeBlock[k] + ":" + "00</div></td>";
            }*/
            thr += "</tr>";
            $('.capacity-tbody').append(thr).append(tr);
            //$('.capacity-tbody').html(tr);
            // }


        },
        error: function (result) {
            alert("Error: Cannot Load Traffic Demand");
        }
    });
}
function getZeros(len) {
    var result = new Array(len);;
    for (var i = 0; i < len; i++) { result[i] = '0'; }
    return result;
}
function getAnnouncement() {
    //$('#loading-overlay').show();
    $.ajax({
        url: "/Home/getAnnouncement",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        complete: function () {
            poll(getAnnouncement, refreshRate);
        },
        success: function (result) {
            announce = result;
            var countl = 0;
            var counto = 0;
            latest = "";
            ongoing = "";

            for (var i = 0; i < result.length; i++) {
                var date = moment(result[i].timeStamp);
                var day = date.format('D');
                var mon = date.format('MMM');
                var time = date.format('DD/MM/YY HH:mm');
                if (date.isSame(moment(), 'day'))
                    time = date.format('HH:mm');
                //result[i].startDate == null && 
                if (countl < 4) {

                    latest += "<div class='info-box btn info-btn announce-btn' value=" + result[i].id + " data-toggle='modal' data-target='#modalAnnounceById'>";
                    latest += "<div class='info-box-icon bg-gradient-" + result[i].catagory + "'><span class='announce-date'>" + day + " <br /><span class='announce-month'>" + mon + "</span> </span></div>"
                    latest += "<div class='info-box-content'>";
                    latest += "<span class='info-box-number announce-header'>" + result[i].header + "</span>";
                    //latest += "<span class='info-box-text'>" + result[i].content + "</span>";
                    latest += "<span class='info-box-text text-small'>By " + result[i].username + ' | ' + time + " UTC</span>";
                    latest += "</div></div>";
                    countl = countl + 1;
                }
                /*if (moment(result[i].endDate) >= moment.utc() && counto < 4) {
                    ongoing += "<div class='info-box btn info-btn announce-btn' value=" + result[i].id + " data-toggle='modal' data-target='#modalAnnounceById'>";
                    ongoing += "<div class='info-box-icon bg-gradient-" + result[i].catagory + "'><span class='announce-date'>" + day + " <br /><span class='announce-month'>" + mon + "</span> </span></div>"
                    ongoing += "<div class='info-box-content'>";
                    ongoing += "<span class='info-box-number announce-header'>" + result[i].header + "</span>";
                    //ongoing += "<span class='info-box-text'>" + result[i].content + "</span>";
                    ongoing += "<span class='info-box-text text-small'>By " + result[i].username + ' | ' + time + " UTC</span>";
                    ongoing += "</div></div>";
                    counto = counto + 1;
                }*/

            }
            $('#announceBox').html(latest);
            $('#atable').bootstrapTable({ data: announce });
            $('#atable').bootstrapTable('load', announce);
            $('.fixed-table-body').overlayScrollbars({});
            $('#loading-overlay').hide();
        },
        error: function (result) {
            alert("Error: Cannot Load Static Airspaces");
        }
    });
}
function getAllAdp() {
    $('#loading-overlay').show();
    $.ajax({
        url: "/Home/GetAllAdp",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function (result) {
            $('#adptable').bootstrapTable({ data: result });
            $('#adptable').bootstrapTable('load', result);
            $('.fixed-table-body').overlayScrollbars({});
            $('#loading-overlay').hide();
        },
        error: function (result) {
            alert("Error: Cannot Load ADP");
        }
    });
}
function dateFormat(value) {
    return moment(value).format('DD/MM/YYYY HH:mm');
}
function dateFormatAdp(value) {
    return moment(value).format('DD/MM/YYYY');
}
function downloadcolFormat(value) {
    return "<a class='btn btn-outline-info btn-sm announce-btn'href='Home/DownloadAdp/" + value + "' target='_blank'>Download</a>";
}
function filecolFormat(value) {
    if (value != null) return "<i class='fas fa-paperclip'></i>";
    else return "";
}
function viewcolFormat(value) {
    return "<button class='btn btn-outline-info btn-sm announce-btn' value=" + value + " data-toggle='modal' data-target='#modalAnnounceById' >View</button>";
}
function getAnnouncementById(id) {
    $('#loading-overlay').show();
    var latest = "";
    //var id = 2; 
    $('#modalAnnounceHeader').removeClass(function (index, className) {
        return (className.match(/(^|\s)bg-\S+/g) || []).join(' ');
    });
    $('#modalAnnounceFooter').removeClass(function (index, className) {
        return (className.match(/(^|\s)bg-\S+/g) || []).join(' ');
    });
    $('.announce-modal-body').each(function (i, item) {
        $(item).html('');
    });
    $.ajax({
        url: "/Announcement/getAnnouncementById/" + id,
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        success: function (result) {
            $('#loading-overlay').hide();

            $('#modalAnnounceHeader').addClass('bg-' + result.catagory);
            $('#modalAnnounceFooter').addClass('bg-' + result.catagory);
            $('#modalAnnouncedate').html("Publised on " + moment(result.timeStamp).format("YYYY-MM-DD HH:mm") + " UTC");
            $('#modalAnnounceUser').html("By " + result.username);
            $('#modalAnnouncetitle').html(result.header);
            $('#modalAnnouncemsg').html(result.content); //href = "./Announcement/Edit/' + value + '"
            if (result.startDate != null && result.endDate != null) {
                $('#modalAnnouncevalid').html("<span class='effective-announce'>Effective Date  </span>" + moment(result.startDate).format('D MMM YYYY HH:mm') + " - " + moment(result.endDate).format(' D MMM YYYY HH:mm') + " UTC");
            }
            else $('#modalAnnouncevalid').html('');

            if (result.filelist != null) {
                var file = "<hr/><div class='row' style='font-weight: 500;margin: 15px 2px;'>Attachment</div>";
                for (var i = 0; i < result.filelist.length; i++) {
                    file += "<div class='card'><div class='card-body' style='padding: 0.5em;'><i class='fas fa-paperclip' style='margin-right: 1em;'></i>" + result.filelist[i].fileName;
                    file += "<a id='btnDownload' style='float:right;' class='btn btn-outline-secondary btn-xs' value='" + result.filelist[i].fileId + "' href='Home/Download/" + result.filelist[i].fileId + "'>Download</a></div></div>";
                }

                $('#modalAnnouncefile').html(file);
            }
            else $('#modalAnnouncefile').html('');


        },
        error: function (result) {
            alert("Error: Cannot Load Announcement");
        }
    });
}
/* ADP Widget*/
function getAdp() {
    var localAdp = [];
    var interAdp = [];
    var mapAdp = [];
    var utcDate = moment.utc().format('DD/MM/YYYY');


    /*   adpLayer.eachLayer(function (layer) {
           adpLayer.removeLayer(layer);
       });*/

    $.ajax({
        url: "/Home/GetAdp",
        type: "GET",
        dataType: "json",
        contentType: "application/json; charset=utf-8",
        complete: function () {
            poll(getAdp, refreshRate);
            $('.last-update').html('Last Update: ' + moment.utc().format('DD/MM/YYYY HH:mm') + ' UTC');
        },
        success: function (result) {
            adpLayer.eachLayer(function (layer) {
                adpLayer.removeLayer(layer);
            });

            $('.adp-list').html('');  // Clear Content
            if (result.length < 1)
                $('.adp-list').html("<li class='list-group-item ' > - None - </li>");

            for (var i = 0; i < result.length; i++) {
                var adpDate = moment(result[i].adpDate).format('DD/MM/YYYY');
                if (utcDate == adpDate)
                    mapAdp.push(result[i]);
                if (result[i].isLocal)
                    localAdp.push(result[i]);
                else
                    interAdp.push(result[i]);
            }

            generateLocalAdp(localAdp);
            generateInterAdp(interAdp);
            generateMapAdp(mapAdp);
            initAdpSearch(); 
        },
        error: function (result) {
            alert("Error: Cannot Load Adp");
        }
    });
}
function generateMapAdp(mapAdp) {
    for (var i = 0; i < mapAdp.length; i++) {
        var icon = adpIcon;
        var color = 'primary';

        if (mapAdp[i].isLocal) {
            icon = adplocalIcon;
            color = 'primary';
        }
        mapAdp[i].atfmu.isLocal = mapAdp[i].isLocal;
        mapAtfmu.push(mapAdp[i].atfmu);
        if (mapAdp[i].atfmu.latitude != null && mapAdp[i].atfmu.longitude != null) {
            var popload = "<a target='_blank' href='Home/DownloadAdp/" + mapAdp[i].id + "'  class='btn btn-sm btn-" + color + "'>Download ADP</a>";
            var popview = "<a href='javascript:void(0)' class='" + color + " atfmu-btn' id='" + mapAdp[i].atfmu.location + "' data-toggle='modal' data-target='#modalAtfmu'> " + mapAdp[i].atfmu.name + "</a>";
            var pop = "<div class='pop-atfmu-title'>" + popview + "</div><div class='pop-atfmu-content'>" + popload + "</div>";
            L.marker([toDecimalLat(mapAdp[i].atfmu.latitude), toDecimalLng(mapAdp[i].atfmu.longitude)], { icon: icon }).addTo(adpLayer).bindPopup(pop);
        }

    }
    //map.fitBounds(adpLayer.getBounds());
}
function generateLocalAdp(local) {
    if (local.length > 0) {
        var utcDate = moment.utc();
        var prev = "<a href='#' class='btn btn-xs  btn-outline-warning adp-btn disabled'><span class='adp-dl-content'>Unavailable</span></a>";
        var current = "<a href='#' class='btn btn-xs  btn-outline-primary adp-btn disabled'><span class='adp-dl-content'>Unavailable</span></a>";
        var next = "<a href='#' class='btn btn-xs  btn-outline-success adp-btn  disabled'><span class='adp-dl-content'>Unavailable</span></a>";

        var llist = "<li class='list-group-item border-bottom adp-local'><span class='local-pin'>";
        llist += "<i class='fas fa-map-marker-alt' ></i></span >" + local[0].atfmu.name + "<span class='adp-btn-group'>";

        for (var i = 0; i < local.length; i++) {
            var adpDate = moment(local[i].adpDate);
            var adpName = moment(local[i].adpDate).format('DD/MM') + "  V. " + local[i].adpRevision;
            if (adpDate.isAfter(utcDate, 'day'))// next
                next = "<a target='_blank' href='Home/DownloadAdp/" + local[i].id + "' class='btn btn-xs  btn-success adp-btn '><span class='adp-dl-content'>" + adpName + "</span></a>";
            if (adpDate.isBefore(utcDate, 'day'))// prev
                prev = "<a target='_blank' href='Home/DownloadAdp/" + local[i].id + "' class='btn btn-xs  btn-warning adp-btn'><span class='adp-dl-content'>" + adpName + "</span></a>";
            if (adpDate.isSame(utcDate, 'day'))
                current = "<a target='_blank' href='Home/DownloadAdp/" + local[i].id + "' class='btn btn-xs  btn-primary adp-btn'><span class='adp-dl-content'>" + adpName + "</span></a>";
        }

        llist += prev;
        llist += current;
        llist += next;
        llist += "</span></li>";

        $('.adp-list').append(llist);
    }

}

function generateInterAdp(inter) {
    if (inter.length > 0) {

        var utcDate = moment.utc();
        var grouped = _.groupBy(inter, function (intr) {
            return intr.adpFirsString;
        });
        var count = 0;

        for (var key in grouped) {
            if (grouped.hasOwnProperty(key)) {
                count = count + 1;

                var firs = grouped[key];
                var prev = "<a href='#' class='btn btn-xs  btn-outline-warning adp-btn disabled'><span class='adp-dl-content'>Unavailable</span></a>";
                var current = "<a href='#' class='btn btn-xs  btn-outline-primary adp-btn disabled'><span class='adp-dl-content'>Unavailable</span></a>";
                var next = "<a href='#' class='btn btn-xs  btn-outline-success adp-btn  disabled'><span class='adp-dl-content'>Unavailable</span></a>";
                var ilist = "<li class='list-group-item border-bottom'>" + firs[0].atfmu.name + "<span class='adp-btn-group'>";

                for (var i = 0; i < firs.length; i++) {
                    var adpDate = moment(firs[i].adpDate);
                    var adpName = moment(firs[i].adpDate).format('DD/MM') + "  V. " + firs[i].adpRevision;
                    if (adpDate.isAfter(utcDate, 'day'))// next
                    {
                        next = "<a target='_blank' href='Home/DownloadAdp/" + firs[i].id + "' class='btn btn-xs  btn-success adp-btn'><span class='adp-dl-content'>" + adpName + "</span></a>";
                        //$('#firsNext').html(next);
                    }
                    if (adpDate.isBefore(utcDate, 'day'))// prev
                    {
                        prev = "<a target='_blank' href='Home/DownloadAdp/" + firs[i].id + "' class='btn btn-xs  btn-warning adp-btn'><span class='adp-dl-content'>" + adpName + "</span></a>";
                        //$('#firsPrevious').html(prev);
                    }
                    if (adpDate.isSame(utcDate, 'day')) {
                        current = "<a target='_blank' href='Home/DownloadAdp/" + firs[i].id + "' class='btn btn-xs  btn-primary adp-btn'><span class='adp-dl-content'>" + adpName + "</span></a>";
                        //$('#firsCurrent').html(current);
                    }
                }
                ilist += prev;
                ilist += current;
                ilist += next;
                ilist += " </span > </li >";

            }
            $('.adp-list').append(ilist);
        }
    }
}

function toDecimalLat(strLat) {
    var degree = parseFloat(strLat.substring(0, 2));
    var minute = parseFloat(strLat.substring(2, 4));
    var second = parseFloat(strLat.substring(4, 8));
    var decimalLat = degree + (minute / 60) + (second / 3600);
    if (strLat.toUpperCase().includes("S")) {
        decimalLat = decimalLat * -1;
    }
    return decimalLat;
}
function toDecimalLng(strLon) {
    var degree = parseFloat(strLon.substring(0, 3));
    var minute = parseFloat(strLon.substring(3, 5));
    var second = parseFloat(strLon.substring(5, 9));
    var decimalLon = degree + (minute / 60) + (second / 3600);
    if (strLon.toUpperCase().includes("W")) {
        decimalLon = decimalLon * -1;
    }
    return decimalLon;
}
function getStyle(type) {
    var result = {};
    var fill = [{
        color: '#007bff',
        types: [0, 44]
    },
    {
        color: '#17a2b8',
        types: [1, 2, 3, 4]
    },
    {
        color: '#20c997',
        types: [5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 22]
    },
    {
        color: '#17a2b8',
        types: [15, 37, 38, 39, 40]
    },
    {
        color: '#01a3a4',
        types: [16, 17]
    },
    {
        color: '#2980b9',
        types: [20, 33]
    },
    {
        color: '#34495e',
        types: [21]
    },
    {
        color: '#28a745',
        types: [23]
    },
    {
        color: '#dc3545',
        types: [24, 25, 26, 27, 32, 36]
    },
    {
        color: '#f8f9fa',
        types: [28]
    },
    {
        color: '#2980b9',
        types: [29, 30]
    },
    {
        color: '#c0392b',
        types: [31]
    },
    {
        color: '#ffc107',
        types: [34, 35]
    },
    {
        color: '#3498db',
        types: [42]
    },
    {
        color: '#ff9ff3',
        types: [43]
    },
    {
        color: '#c8d6e5',
        types: [45]
    }
    ];

    for (var i = 0; i < fill.length; i++) {
        if (fill[i].types.includes(type)) result = fill[i];
    }
    return result;
}

function renderCheckboxes(data, dropDownId) {
    var prefix= "sector";
    if (dropDownId.includes('Airports'))
        prefix = "airport";
    const $menu = $(dropDownId);
    $menu.find('.checkbox-item').remove();
    console.log('render!');
    const insertBefore = $menu.find('hr').eq(1); // second divider
    
    const checkboxClass = `item-checkbox-${prefix}`; // e.g., "item-checkbox-dropdown1"

    data.forEach(item => {
        const checkboxId = `${prefix}_${item.designator}`;

        const html = `
        <li class="checkbox-item">
          <div class="form-check">
            <input class="form-check-input ${checkboxClass}" type="checkbox" value="${item.designator}" id="${checkboxId}">
            <label class="form-check-label" for="${checkboxId}">${item.designator}</label>
          </div>
        </li>`;
        $(html).insertBefore(insertBefore);
    });


    // Re-check previously selected items from storage
    var item = "selectedSectors";
    if (dropDownId.includes('Airports')) 
        item = "selectedAirports";
    
    const savedList = JSON.parse(localStorage.getItem(item) || '[]');
    const savedIds = savedList.map(i => i.designator);
    $(`.${checkboxClass}`).each(function () {
        if (savedIds.includes($(this).val())) {
            $(this).prop('checked', true);
        }
    });

    const allAirportChecked = $('.item-checkbox-airport').length === $('.item-checkbox-airport:checked').length;
    $('#selectAllCheckboxAirport').prop('checked', allAirportChecked);

    const allSectorChecked = $('.item-checkbox-sector').length === $('.item-checkbox-sector:checked').length;
    $('#selectAllCheckboxSector').prop('checked', allSectorChecked);
}
function renderAirportLayer()
{
    capaLayer.eachLayer(function (layer) {
        capaLayer.removeLayer(layer);
    });
    const storedAirports = JSON.parse(localStorage.getItem("selectedAirports"));
    console.log(storedAirports);
    if (storedAirports.length > 0)
        tdAirports = storedAirports; //result -> selectedAirports

    for (var i = 0; i < tdAirports.length; i++) {
        var icon = grayIcon;
        if (tdAirports[i].arrival != null && tdAirports[i].departure != null) {
            if (parseInt(tdAirports[i].capacity) > 0) {
                icon = greenIcon;
                if ((parseInt(tdAirports[i].arrival) + parseInt(tdAirports[i].departure)) > parseInt(tdAirports[i].capacity)) //change from >= to > Capacity as Sectors
                    icon = redIcon;
                else if ((parseInt(tdAirports[i].arrival) + parseInt(tdAirports[i].departure)) >= (parseInt(tdAirports[i].capacity) * 0.8))
                    icon = yellowIcon;
            }
        }
       
        L.marker([toDecimalLat(tdAirports[i].latDegree), toDecimalLng(tdAirports[i].lonDegree)], { icon: icon }).addTo(capaLayer).bindPopup("<div class='text-bold'>" + tdAirports[i].designator + "</div>");
    }
}

function renderSectorLayer() {
    sectorLayer.eachLayer(function (layer) {
        sectorLayer.removeLayer(layer);
    });
    const storedSectors = JSON.parse(localStorage.getItem("selectedSectors"));
    console.log(storedSectors);
    if (storedSectors.length > 0)
        tdSectors = storedSectors; //result -> selectedAirports

    for (var i = 0; i < tdSectors.length; i++) {
        // if (tdSectors[i].airspaceType == 16) {
        var feature = {
            type: "Feature", properties: { id: tdSectors[i].id, name: tdSectors[i].name },
            geometry: Terraformer.WKT.parse(tdSectors[i].geographyString)
        };
        var style = getStyle(16);//tdSectors[i].airspaceType 
        if (parseInt(tdSectors[i].capacity) < 1 || parseInt(tdSectors[i].capacity) == null) // ถ้าไม่มี capacity ให้ใส่สีเทา
            style.color = '#2a2b2b';
        else if (parseInt(tdSectors[i].arrival) >= parseInt(tdSectors[i].capacity) && parseInt(tdSectors[i].capacity) > 0)
            style.color = '#DC143C';
        else if (parseInt(tdSectors[i].arrival) >= (parseInt(tdSectors[i].capacity) * 0.8) && parseInt(tdSectors[i].capacity) > 0)
            style.color = '#E0E01D';

        //displayAirspaceMap(feature, style);
        var mapStyle = {
            weight: 1,
            color: "#999",
            opacity: 1,
            fillColor: style.color,
            fillOpacity: 0.1
        };
        var geoJson = L.geoJSON(feature, {
            style: mapStyle,
            onEachFeature: onEachFeature
        });

        function onEachFeature(feature, layer) {
            if (feature.properties && feature.properties.name) {
                mapLayerGroups[feature.properties.id] = layer;
                layer.bindTooltip(feature.properties.name,
                    {
                        permanent: true,
                        direction: "center",
                        maxWidth: "auto",
                        className: 'labelstyle-sector'
                    }
                );
            }
        }

        geoJson.addTo(sectorLayer);
        // }
    }
}

//  ADP SEARCH  (simple filter – no remote suggestions)
function initAdpSearch() {

    const LOG = '[ADP-SEARCH]';
    const $box = $('#adpSearch');        // <input>
    const $btn = $('#adpSearchBtn');     // 🔍 button

    if (!$box.length || !$btn.length) {
        console.warn(`${LOG} input or button not found — skip init`);
        return;
    }

    //  Remove handlers or autocomplete left from a previous run
    $box.off('.adpSearch');
    $btn.off('.adpSearch');

    if ($box.data('ui-autocomplete')) {
        // in case an earlier build added it – wipe it
        $box.autocomplete('destroy');
    }

    //  Helper to show / hide <li>
    function filterList(term) {
        term = term.trim().toLowerCase();
        $('.adp-list > li').each(function () {
            $(this).toggle($(this).text().toLowerCase().includes(term));
        });
    }

    //  ENTER key   +   🔍 button
    $box.on('keypress.adpSearch', function (e) {
        if (e.which === 13) {            // ENTER
            e.preventDefault();
            filterList($box.val());
        }
    });

    $btn.on('click.adpSearch', function () {
        filterList($box.val());
    });

    /* optional: live filtering while typing */
    $box.on('keyup.adpSearch', function () {
        filterList($box.val());
    });
}


//  ADP SEARCH  (simple filter – no remote suggestions)
function initAdpSearch() {

    const LOG = '[ADP-SEARCH]';
    const $box = $('#adpSearch');        // <input>
    const $btn = $('#adpSearchBtn');     // 🔍 button

    if (!$box.length || !$btn.length) {
        console.warn(`${LOG} input or button not found — skip init`);
        return;
    }

    //  Remove handlers or autocomplete left from a previous run
    $box.off('.adpSearch');
    $btn.off('.adpSearch');

    if ($box.data('ui-autocomplete')) {
        // in case an earlier build added it – wipe it
        $box.autocomplete('destroy');
    }

     //  Helper to show / hide <li>
    function filterList(term) {
        term = term.trim().toLowerCase();
        $('.adp-list > li').each(function () {
            $(this).toggle($(this).text().toLowerCase().includes(term));
        });
    }

    //  ENTER key   +   🔍 button
    $box.on('keypress.adpSearch', function (e) {
        if (e.which === 13) {            // ENTER
            e.preventDefault();
            filterList($box.val());
        }
    });

    $btn.on('click.adpSearch', function () {
        filterList($box.val());
    });

    /* optional: live filtering while typing */
    $box.on('keyup.adpSearch', function () {
        filterList($box.val());
    });
}

/* ──────────  RESOURCES widget  ────────── */
function loadResourcesWidget() {

    $.getJSON('/Home/GetResourcesList')
        .done(function (rows) {

            const $ul = $('.resource-list').empty();        // wipe old list
            const host = window.location.origin;             // get host

            if (!rows.length) {
                $ul.append('<li class="list-group-item">No resources.</li>');
                return;
            }

            rows.forEach(r => {

                /* ────────── build HREF safely ────────── */
                const host = window.location.origin;
                let href = '#';

                if (r.url) {                               // LINK mode
                    href = r.url.trim();

                    // If the URL is *relative* (no http/https), prefix the site-origin
                    if (!/^(https?:)?\/\/.+/i.test(href)) {
                        href = `${host}/${href.replace(/^\/+/, '')}`;
                    }
                }
                else if (r.fileRelPath) {                  // FILE mode
                    href = `${host}/${r.fileRelPath.replace(/^\/+/, '')}`;
                }

                /* ────────── build <li> row ────────── */
                const title = _.escape(r.title || r.displayName || '(no title)');

                const li = href === '#'
                    ? `<li class="list-group-item">${title}</li>`
                    : `<li class="list-group-item"><a href="${href}" target="_blank">${title}</a></li>`;

                $ul.append(li);
            });
        })
        .fail(xhr => console.error('[RESOURCES]', xhr.responseText || xhr.statusText));
}


