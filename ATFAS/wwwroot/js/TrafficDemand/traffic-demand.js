﻿$(document).ready(function () {
    TrafficDemand.init();
});

TrafficDemand = {
    tdObj: new Object(),
    airports: [],
    fixes: [],
    sectors: [],
    airspaces: [],
    tdCharts: [],
    bgSCH: 'rgba(207, 226, 243, 0.5)',
    bgFPL: 'rgba(164, 194, 244, 0.5)',
    bgATFM: 'rgba(109, 158, 235, 0.5)',
    bgATSMSG: 'rgba(60, 120, 216, 0.5)',
    bgSUR: 'rgba(28, 69, 135, 0.5)',
    bgPassed: 'rgba(102, 102, 102, 0.5)',
    bgSCHDep: 'rgba(255, 229, 153, 0.5)',
    bgFPLDep: 'rgba(241, 194, 50, 0.5)',
    bgATFMDep: 'rgba(191, 144, 0, 0.5)',
    bgATSMSGDep: 'rgba(127, 96, 0, 0.5)',
    bgKickoffDelay: 'rgba(255, 99, 132, 0.3)',
    formFn: null,

    init: function () {
        TrafficDemand.requestTrafficDemandInfo();
        TrafficDemand.addEventDesignatorSelect();
        TrafficDemand.addEventAreaSelect();
        TrafficDemand.addEventAirportCheckbox();
        TrafficDemand.addEventTimeOptionRadio();
        TrafficDemand.addEventClearBtn();
        TrafficDemand.formFn = TrafficDemand.tdFormFn;
        TrafficDemand.addEventForm();
        TrafficDemand.addEventSaveModalForm();
        TrafficDemand.initSortableChartContainer();
        setInterval(TrafficDemand.updateCharts, 60000);
    },

    requestTrafficDemandInfo: function () {
        $.ajax({
            type: 'POST',
            url: '/TrafficDemand/RequestTrafficDemandInfo',
            dataType: 'json',
            success: function (trafficDemandInfo) {
                var $designator = $('#designator');
                $.each(trafficDemandInfo.trafficDemands, function (i, item) {
                    $designator.append($('<option>', {
                        value: item.id,
                        text: item.designator
                    }));
                    TrafficDemand.tdObj[item.id] = item;
                });
                TrafficDemand.airports = trafficDemandInfo.airports;
                TrafficDemand.fixes = trafficDemandInfo.fixes;
                TrafficDemand.sectors = trafficDemandInfo.staticAirspaces;
                TrafficDemand.airspaces = trafficDemandInfo.userDefinedAirspaces;
                $("#Point").autocomplete({
                    source: TrafficDemand.airports
                });
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to request traffic demand information');
            }
        });
    },

    addEventDesignatorSelect: function () {
        $('#designator').on('change', function (e) {
            var objSelected = TrafficDemand.tdObj[this.value];
            if (objSelected) {
                $('#Point').val(objSelected.point).focus();
                $('#IsDep').prop('checked', objSelected.isDep);
                $('#IsArr').prop('checked', objSelected.isArr);
                $('#IsCombined').prop('checked', objSelected.isCombined);
                $('#LowerFlightLevel').val(objSelected.lowerFlightLevel);
                $('#UpperFlightLevel').val(objSelected.upperFlightLevel);
                $('#RadiusNm').val(objSelected.radiusNm);
                if (objSelected.startTime) {
                    var startTimes = objSelected.startTime.split('T');
                    $('#StartTime').val(startTimes[0] + ' ' + startTimes[1].substr(0, 5)).focus();
                    var endTimes = objSelected.endTime.split('T');
                    $('#EndTime').val(endTimes[0] + ' ' + endTimes[1].substr(0, 5)).focus();
                }
                if (objSelected.aheadHour) $('#AheadHour').val(objSelected.aheadHour).focus();
                $('#IntervalMin').val(objSelected.intervalMin).focus();
                $('#IsIFR').prop('checked', objSelected.isIFR);
                $('#IsVFR').prop('checked', objSelected.isVFR);
                if (!objSelected.isIFR && !objSelected.isVFR) {
                    $('#IsIFR').prop('checked', true);
                    $('#IsVFR').prop('checked', true);
                }
                $('#IsSCH').prop('checked', objSelected.isSCH);
                $('#IsFPL').prop('checked', objSelected.isFPL);
                $('#IsATFM').prop('checked', objSelected.isATFM);
                $('#IsATSMSG').prop('checked', objSelected.isATSMSG);
                $('#IsSUR').prop('checked', objSelected.isSUR);
                $('#TrafficAreaId').val(objSelected.trafficAreaId).trigger('change');
                $('#td-specify').prop('checked', (objSelected.startTime != null)).trigger('change');
                $('#td-current').prop('checked', (objSelected.aheadHour != null)).trigger('change');
                $('#td-fieldset').prop('disabled', true).find('.field-validation-error').children().remove();
                $(this).focus();
            } else {
                $('#td-fieldset').prop('disabled', false);
            }
        });
    },

    addEventAreaSelect: function () {
        $('#TrafficAreaId').on('change', function (e) {
            if (this.value == 1) {
                var $isDep = $('#IsDep');
                var $isArr = $('#IsArr');
                if (!$isDep.is(':checked') && !$isArr.is(':checked')) {
                    $isDep.add($isArr).prop("checked", true);
                }
                $('#td-fieldset .td-airport').show();
                $('#td-fieldset .td-waypoint, #td-fieldset .td-sector').hide();
                $isArr.trigger('change');
                $("#Point").autocomplete("option", "source", TrafficDemand.airports);
            } else {
                $('#td-fieldset .td-airport').hide();
                if (this.value == 2) {
                    $('#td-fieldset .td-waypoint').show();
                    $("#Point").autocomplete("option", "source", TrafficDemand.fixes);
                } else {
                    $('#td-fieldset .td-waypoint').hide();
                    if (this.value == 3) {
                        $('#td-fieldset .td-sector').show();
                        $("#Point").autocomplete("option", "source", TrafficDemand.sectors);
                    } else if (this.value == 4) {
                        $('#td-fieldset .td-sector').show();
                        $("#Point").autocomplete("option", "source", TrafficDemand.airspaces);
                    } else {
                        $('#td-fieldset .td-sector').hide();
                    }
                }
            }
        });
    },

    addEventAirportCheckbox: function () {
        $('#IsCombined').on('change', function (e) {
            if ($(this).is(':checked')) {
                $('#IsDep').prop("checked", true);
                $('#IsArr').prop("checked", true).trigger('change');
            }
        });
        $('#IsDep').on('change', function (e) {
            if (!$(this).is(':checked')) {
                $('#IsCombined').prop("checked", false);
            }
        });
        $('#IsArr').on('change', function (e) {
            if ($(this).is(':checked')) {
                $('#td-fieldset .td-arr').show();
            } else {
                $('#IsCombined').prop("checked", false);
                $('#td-fieldset .td-arr').hide();
            }
        });
    },

    addEventTimeOptionRadio: function () {
        $('#td-specify').on('change', function (e) {
            if ($(this).is(':checked')) {
                $('#td-fieldset .td-specify').show();
                $('#td-fieldset .td-current').hide();
            }
        });
        $('#td-current').on('change', function (e) {
            if ($(this).is(':checked')) {
                $('#td-fieldset .td-current').show();
                $('#td-fieldset .td-specify').hide();
            }
        });
    },

    addEventClearBtn: function () {
        $('#td-clear').on('click', function (e) {
            $('#td-form').trigger('reset');
            $('#td-fieldset').prop('disabled', false).find('.field-validation-valid').children().remove();
            $('#TrafficAreaId').trigger('change');
            $('#td-specify').trigger('change');
        });
    },

    addEventForm: function () {
        $("#td-form").on('submit', function (e) {
            if ($(this).valid()) {
                e.preventDefault();
                $('#loading-overlay').show();
                TrafficDemand.formFn();
            }
        });
    },

    tdFormFn: function () {
        var data = {
            Id: $('#designator').val(),
            Designator: $('#designator option:selected').text(),
            TrafficAreaId: $('#TrafficAreaId').val(),
            Point: $('#Point').val(),
            IsDep: $('#IsDep').is(':checked'),
            IsArr: $('#IsArr').is(':checked'),
            IsCombined: $('#IsCombined').is(':checked'),
            LowerFlightLevel: $('#LowerFlightLevel').val(),
            UpperFlightLevel: $('#UpperFlightLevel').val(),
            RadiusNm: $('#RadiusNm').val(),
            IntervalMin: $('#IntervalMin').val(),
            IsIFR: $('#IsIFR').is(':checked'),
            IsVFR: $('#IsVFR').is(':checked'),
            IsSCH: $('#IsSCH').is(':checked'),
            IsFPL: $('#IsFPL').is(':checked'),
            IsATFM: $('#IsATFM').is(':checked'),
            IsATSMSG: $('#IsATSMSG').is(':checked'),
            IsSUR: $('#IsSUR').is(':checked'),
            IsPassed: $('#IsPassed').is(':checked')
        };
        if ($('#td-specify').is(':checked')) {
            data.StartTime = $('#StartTime').val();
            data.EndTime = $('#EndTime').val();
        } else if ($('#td-current').is(':checked')) {
            data.AheadHour = $('#AheadHour').val();
        }
        $.ajax({
            type: 'POST',
            url: '/TrafficDemand/RequestTDChart',
            dataType: 'json',
            data: data,
            success: function (tdChart) {
                if (!tdChart.trafficDemand) {
                    TrafficDemand.showError(tdChart.title);
                    return;
                }
                if (tdChart.warning) {
                    TrafficDemand.showWarning(tdChart.warning);
                }
                var $tdChartContainer = $('#td-chart-container');
                $tdChartContainer.append($tdChartContainer.children().eq(0).clone().show());
                var $tdChart = $tdChartContainer.find('.td-chart').last();
                var chart = TrafficDemand.createChart(tdChart, $tdChart);
                var tdChartObj = {
                    tdChart: tdChart,
                    container: $tdChart,
                    chart: chart
                };
                TrafficDemand.tdCharts.push(tdChartObj);
                TrafficDemand.addEventChartBtn(tdChartObj);
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to request traffic demand');
            },
            complete: function () {
                $('#loading-overlay').hide();
            }
        });
    },

    addEventSaveModalForm: function () {
        var $saveModal = $('#save-modal');
        $saveModal.find('form').on('submit', function (e) {
            if ($(this).valid()) {
                e.preventDefault();
                $saveModal.modal('hide');
                var tdChartObj = $(this).data('tdChartObj');
                var data = tdChartObj.tdChart.trafficDemand;
                data.designator = $saveModal.find('.save-modal-designator').val();
                $.ajax({
                    type: 'POST',
                    url: '/TrafficDemandManage/CreateAjax',
                    dataType: 'json',
                    data: data,
                    success: function (tdChart) {
                        if (!tdChart.trafficDemand) {
                            alert('Unable to save traffic demand parameter');
                            return;
                        }
                        var tdToast = $('#success-toast');
                        tdToast.find('.toast-body').text('Traffic demand parameter (' + data.designator + ') is successfully saved.');
                        tdToast.find('.success-toast-time').text(tdChart.time);
                        tdToast.toast('show');
                        $('#designator').append($('<option>', {
                            value: tdChart.trafficDemand.id,
                            text: tdChart.trafficDemand.designator
                        }));
                        TrafficDemand.tdObj[tdChart.trafficDemand.id] = tdChart.trafficDemand;
                        tdChartObj.tdChart.trafficDemand = tdChart.trafficDemand;
                        tdChartObj.container.parent().find('.td-chart-save').off('click').hide();
                        var chartTitle = tdChartObj.chart.options.title.text;
                        tdChartObj.chart.options.title.text = [tdChart.trafficDemand.designator, chartTitle];
                        tdChartObj.chart.update();
                    },
                    error: function (req, status, error) {
                        console.log(error);
                        alert('Unable to save traffic demand parameter');
                    }
                });
            }
        });
    },

    initSortableChartContainer: function () {
        var $tdChartContainer = $("#td-chart-container");
        $tdChartContainer.sortable({
            containment: "parent",
            cursor: "move",
            forcePlaceholderSize: true,
            placeholder: "bg-primary float-left mr-lg-3 mb-3"
        });
        $tdChartContainer.disableSelection();
    },

    updateCharts: function () {
        if (TrafficDemand.tdCharts.length == 0) {
            if (!('GDP' in window)) $('#td-update').hide();
            return;
        }
        var $tdUpdate = $('#td-update');
        var $tdUpdateTime = $tdUpdate.find('.td-update-time');
        for (var i = 0; i < TrafficDemand.tdCharts.length; i++) {
            let tdChartObj = TrafficDemand.tdCharts[i];
            $.ajax({
                type: 'POST',
                url: '/TrafficDemand/RequestTDChart',
                dataType: 'json',
                data: tdChartObj.tdChart.trafficDemand,
                success: function (tdChart) {
                    tdChartObj.tdChart = tdChart;
                    tdChartObj.chart.data.datasets.forEach((dataset) => {
                        if (dataset.order == 0) dataset.data = tdChart.dataATSMSGDep;
                        else if (dataset.order == 1) dataset.data = tdChart.dataATFMDep;
                        else if (dataset.order == 2) dataset.data = tdChart.dataFPLDep;
                        else if (dataset.order == 3) dataset.data = tdChart.dataSCHDep;
                        else if (dataset.order == 4) dataset.data = tdChart.dataPassed;
                        else if (dataset.order == 5) dataset.data = tdChart.dataSUR;
                        else if (dataset.order == 6) dataset.data = tdChart.dataATSMSG;
                        else if (dataset.order == 7) dataset.data = tdChart.dataATFM;
                        else if (dataset.order == 8) dataset.data = tdChart.dataFPL;
                        else if (dataset.order == 9) dataset.data = tdChart.dataSCH;
                        else if (dataset.order == 11) dataset.data = tdChart.capacitiesDep;
                        else if (dataset.order == 12) dataset.data = tdChart.capacities;
                        else if (dataset.order == 13) dataset.data = tdChart.capacityDepWarnings;
                        else if (dataset.order == 14) dataset.data = tdChart.capacityWarnings;
                    });
                    if (tdChart.trafficDemand.aheadHour) {
                        if (tdChart.trafficDemand.id < 0) tdChartObj.chart.options.title.text = tdChart.title;
                        else tdChartObj.chart.options.title.text[1] = tdChart.title;
                        tdChartObj.chart.data.labels = tdChart.labels;
                    }
                    tdChartObj.chart.update();
                    $tdUpdate.show();
                    $tdUpdateTime.text(tdChart.time);
                    tdChartObj.container.siblings('.td-chart-warning').hide();
                    TrafficDemand.addEventTdChart(tdChart, tdChartObj.container, tdChartObj.chart);
                },
                error: function (req, status, error) {
                    console.log(error);
                    tdChartObj.container.siblings('.td-chart-warning').show();
                }
            });
        }
    },

    addEventChartBtn: function (tdChartObj) {
        var $tdChartBtn = tdChartObj.container.siblings('.td-chart-btn');
        var $tdChartRemove = $tdChartBtn.find('.td-chart-remove');
        var $tdChartSave = $tdChartBtn.find('.td-chart-save');
        if (tdChartObj.tdChart.trafficDemand.id < 0) {
            $tdChartSave.show();
            $tdChartSave.on('click', function () {
                var $saveModal = $('#save-modal');
                var $saveModalForm = $saveModal.find('form');
                $saveModalForm.trigger('reset');
                $saveModal.find('.td-airport, .td-waypoint, .td-sector, .td-arr, .td-current, .td-specify').show();
                $saveModal.find('.save-modal-area').text(tdChartObj.tdChart.trafficDemand.trafficArea.type);
                $saveModal.find('.save-modal-point').text(tdChartObj.tdChart.trafficDemand.point);
                var trafficAreaId = tdChartObj.tdChart.trafficDemand.trafficAreaId;
                if (trafficAreaId == 1) {
                    $saveModal.find('.td-waypoint, .td-sector').hide();
                    if (tdChartObj.tdChart.trafficDemand.isDep) $saveModal.find('.save-modal-dep').prop("checked", true);
                    if (tdChartObj.tdChart.trafficDemand.isArr) {
                        $saveModal.find('.td-arr').show();
                        $saveModal.find('.save-modal-arr').prop("checked", true);
                    }
                    if (tdChartObj.tdChart.trafficDemand.isCombined) $saveModal.find('.save-modal-combined').prop("checked", true);
                } else {
                    if (trafficAreaId == 2) {
                        $saveModal.find('.td-airport, .td-sector').hide();
                        $saveModal.find('.td-waypoint').show();
                        if (tdChartObj.tdChart.trafficDemand.radiusNm) $saveModal.find('.save-modal-radius').text(tdChartObj.tdChart.trafficDemand.radiusNm);
                        else $saveModal.find('.td-radius').hide();
                    } else {
                        $saveModal.find('.td-airport, .td-waypoint').hide();
                        $saveModal.find('.td-sector').show();
                    }
                    if (tdChartObj.tdChart.trafficDemand.lowerFlightLevel != null) $saveModal.find('.save-modal-lower').text(tdChartObj.tdChart.trafficDemand.lowerFlightLevel);
                    else $saveModal.find('.td-lower').hide();
                    if (tdChartObj.tdChart.trafficDemand.upperFlightLevel != null) $saveModal.find('.save-modal-upper').text(tdChartObj.tdChart.trafficDemand.upperFlightLevel);
                    else $saveModal.find('.td-upper').hide();
                }
                if (tdChartObj.tdChart.trafficDemand.startTime) {
                    $saveModal.find('.save-modal-specify').prop('checked', true);
                    $saveModal.find('.td-current').hide();
                    var startArray = tdChartObj.tdChart.trafficDemand.startTime.split('T');
                    var endArray = tdChartObj.tdChart.trafficDemand.endTime.split('T');
                    $saveModal.find('.save-modal-start').text(startArray[0] + ' ' + startArray[1].substr(0,5));
                    $saveModal.find('.save-modal-end').text(endArray[0] + ' ' + endArray[1].substr(0, 5));
                }
                else {
                    $saveModal.find('.save-modal-current').prop('checked', true);
                    $saveModal.find('.td-specify').hide();
                    $saveModal.find('.save-modal-ahead').text(tdChartObj.tdChart.trafficDemand.aheadHour);
                }
                $saveModal.find('.save-modal-interval').text(tdChartObj.tdChart.trafficDemand.intervalMin);
                $saveModal.find('.ifr').prop('checked', tdChartObj.tdChart.trafficDemand.isIFR);
                $saveModal.find('.vfr').prop('checked', tdChartObj.tdChart.trafficDemand.isVFR);
                $saveModal.find('.save-modal-sch').prop('checked', tdChartObj.tdChart.trafficDemand.isSCH);
                $saveModal.find('.save-modal-fpl').prop('checked', tdChartObj.tdChart.trafficDemand.isFPL);
                $saveModal.find('.save-modal-atfm').prop('checked', tdChartObj.tdChart.trafficDemand.isATFM);
                $saveModal.find('.save-modal-atsmsg').prop('checked', tdChartObj.tdChart.trafficDemand.isATSMSG);
                $saveModal.find('.save-modal-sur').prop('checked', tdChartObj.tdChart.trafficDemand.isSUR);
                $saveModal.find('.save-modal-passed').prop('checked', tdChartObj.tdChart.trafficDemand.isPassed);
                $saveModalForm.data('tdChartObj', tdChartObj);
                $saveModal.modal('show');
            });
        }
        var $tdChartExport = $tdChartBtn.find('.td-chart-export');
        $tdChartBtn.find('button').tooltip();
        var $confirmModal = $('#confirm-modal');
        $tdChartRemove.on('click', function () {
            $confirmModal.find('.modal-title').text('Remove Chart');
            var $modalText = $confirmModal.find('.modal-body p');
            $modalText.html('Do you want to remove the following chart?<br><br>');
            if (tdChartObj.tdChart.trafficDemand.id < 0) {
                var $chartTitle = $('<i></i>').text(tdChartObj.chart.options.title.text);
                $modalText.append($chartTitle);
            }
            else {
                var $chartTitle = $('<i></i>').text(tdChartObj.chart.options.title.text[0]);
                var $chartTitle2 = $('<i></i>').text(tdChartObj.chart.options.title.text[1]);
                $modalText.append($chartTitle).append('<br>').append($chartTitle2);
            }
            $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                $confirmModal.modal('hide');
                tdChartObj.container.parent().remove();
                for (var i = 0; i < TrafficDemand.tdCharts.length; i++) {
                    if (TrafficDemand.tdCharts[i].container == tdChartObj.container) TrafficDemand.tdCharts.splice(i, 1);
                }
                if ('GDP' in window) {
                    for (var i = 0; i < GDP.gdpCharts.length; i++) {
                        if (GDP.gdpCharts[i].container == tdChartObj.container) GDP.gdpCharts.splice(i, 1);
                    }
                }
            });
            $confirmModal.modal('show');
        });
        $tdChartExport.on('click', function () {
            $confirmModal.find('.modal-title').text('Export Traffic Demand Data');
            var $modalText = $confirmModal.find('.modal-body p');
            $modalText.html('Do you want to export traffic demand data of the following chart?<br><br>');
            if (tdChartObj.tdChart.trafficDemand.id < 0) {
                var $chartTitle = $('<i></i>').text(tdChartObj.chart.options.title.text);
                $modalText.append($chartTitle);
            }
            else {
                var $chartTitle = $('<i></i>').text(tdChartObj.chart.options.title.text[0]);
                var $chartTitle2 = $('<i></i>').text(tdChartObj.chart.options.title.text[1]);
                $modalText.append($chartTitle).append('<br>').append($chartTitle2);
            }
            $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                $confirmModal.modal('hide');
                var filename = $chartTitle.text() + '.csv';
                var data = [];
                if (tdChartObj.tdChart.trafficDemand.isDep) {
                    for (var i = 0; i < tdChartObj.tdChart.flightDepLists.length; i++) {
                        for (var j = 0; j < tdChartObj.tdChart.flightDepLists[i].length; j++) {
                            data.push(tdChartObj.tdChart.flightDepLists[i][j].flight);
                        }
                    }
                }
                if (tdChartObj.tdChart.trafficDemand.isArr || tdChartObj.tdChart.trafficDemand.trafficAreaId != 1) {
                    for (var i = 0; i < tdChartObj.tdChart.flightLists.length; i++) {
                        for (var j = 0; j < tdChartObj.tdChart.flightLists[i].length; j++) {
                            data.push(tdChartObj.tdChart.flightLists[i][j].flight);
                        }
                    }
                }
                TrafficDemand.downloadCSV({
                    filename: filename,
                    data: data
                });
            });
            $confirmModal.modal('show');
        });
    },

    downloadCSV: function (args) {
        var data, filename, link;
        var csv = TrafficDemand.convertArrayOfObjectsToCSV({
            data: args.data
        });
        if (csv == null) return;
        filename = args.filename || 'export.csv';
        if (!csv.match(/^data:text\/csv/i)) {
            csv = 'data:text/csv;charset=utf-8,' + csv;
        }
        data = encodeURI(csv);
        link = document.createElement('a');
        link.setAttribute('href', data);
        link.setAttribute('download', filename);
        link.click();
    },

    convertArrayOfObjectsToCSV: function (args) {
        var result, ctr, keys, columnDelimiter, lineDelimiter, data;
        data = args.data || null;
        if (data == null || !data.length) {
            return null;
        }
        columnDelimiter = args.columnDelimiter || ',';
        lineDelimiter = args.lineDelimiter || '\n';
        keys = Object.keys(data[0]);
        result = '';
        result += keys.join(columnDelimiter);
        result += lineDelimiter;
        data.forEach(function (item) {
            ctr = 0;
            keys.forEach(function (key) {
                if (ctr > 0) result += columnDelimiter;
                result += item[key];
                ctr++;
            });
            result += lineDelimiter;
        });
        return result;
    },

    createChart: function (tdChart, $tdChart) {
        var datasets = [];
        var bgColor = 'rgba(255, 99, 132, 0.3)';
        var borderColor = 'rgb(255, 99, 132)';
        var capacityStr = 'Capacity';
        if (tdChart.capacitiesDep) {
            bgColor = 'rgba(255, 159, 64, 0.3)';
            borderColor = 'rgb(255, 159, 64)';
            datasets.push({
                type: 'line',
                label: 'Capacity-Dep',
                order: 11,
                borderColor: borderColor,
                borderWidth: 2,
                fill: false,
                pointRadius: 0,
                pointHoverRadius: 0,
                steppedLine: 'before',
                xAxisID: 'x-axis-line',
                data: tdChart.capacitiesDep
            });
            datasets.push({
                type: 'line',
                label: 'Capacity-Dep Warning',
                order: 13,
                backgroundColor: bgColor,
                borderColor: borderColor,
                borderWidth: 0,
                fill: 'origin',
                pointRadius: 0,
                pointHoverRadius: 0,
                steppedLine: 'before',
                xAxisID: 'x-axis-line',
                data: tdChart.capacityDepWarnings
            });
            bgColor = 'rgba(153, 102, 255, 0.3)';
            borderColor = 'rgb(153, 102, 255)';
            capacityStr = 'Capacity-Arr'
        }
        datasets.push({
            type: 'line',
            label: capacityStr,
            order: 12,
            borderColor: borderColor,
            borderWidth: 2,
            fill: false,
            pointRadius: 0,
            pointHoverRadius: 0,
            steppedLine: 'before',
            xAxisID: 'x-axis-line',
            data: tdChart.capacities
        });
        if (!tdChart.gdp) {
            datasets.push({
                type: 'line',
                label: capacityStr + ' Warning',
                order: 14,
                backgroundColor: bgColor,
                borderColor: borderColor,
                borderWidth: 0,
                fill: 'origin',
                pointRadius: 0,
                pointHoverRadius: 0,
                steppedLine: 'before',
                xAxisID: 'x-axis-line',
                data: tdChart.capacityWarnings
            });
        }
        if (tdChart.trafficDemand.trafficAreaId == 1 && tdChart.trafficDemand.isDep) {
            if (tdChart.trafficDemand.isATSMSG) {
                datasets.push({
                    label: 'DEP',
                    stack: '0',
                    order: 0,
                    data: tdChart.dataATSMSGDep,
                    backgroundColor: TrafficDemand.bgATSMSGDep,
                    borderColor: 'rgba(127, 96, 0, 1)',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isATFM) {
                datasets.push({
                    label: 'GDP',
                    stack: '0',
                    order: 1,
                    data: tdChart.dataATFMDep,
                    backgroundColor: TrafficDemand.bgATFMDep,
                    borderColor: 'rgba(191, 144, 0, 1))',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isFPL) {
                datasets.push({
                    label: 'FPL',
                    stack: '0',
                    order: 2,
                    data: tdChart.dataFPLDep,
                    backgroundColor: TrafficDemand.bgFPLDep,
                    borderColor: 'rgba(241, 194, 50, 1)',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isSCH) {
                datasets.push({
                    label: 'SCH',
                    stack: '0',
                    order: 3,
                    data: tdChart.dataSCHDep,
                    backgroundColor: TrafficDemand.bgSCHDep,
                    borderColor: 'rgba(241, 194, 50, 0.7)',
                    borderWidth: 1
                });
            }
        }
        if ((tdChart.trafficDemand.trafficAreaId == 1 && tdChart.trafficDemand.isArr) || tdChart.trafficDemand.trafficAreaId != 1) {
            var stack = '1';
            var label = 'ENT';
            if (tdChart.trafficDemand.trafficAreaId == 1) {
                label = 'ARR';
                if (tdChart.trafficDemand.isCombined) stack = '0';
            }
            if (tdChart.trafficDemand.isPassed) {
                datasets.push({
                    label: label,
                    stack: stack,
                    order: 4,
                    data: tdChart.dataPassed,
                    backgroundColor: TrafficDemand.bgPassed,
                    borderColor: 'rgba(102, 102, 102, 1)',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isSUR) {
                datasets.push({
                    label: 'SUR',
                    stack: stack,
                    order: 5,
                    data: tdChart.dataSUR,
                    backgroundColor: TrafficDemand.bgSUR,
                    borderColor: 'rgba(28, 69, 135, 1)',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isATSMSG) {
                datasets.push({
                    label: 'DEP',
                    stack: stack,
                    order: 6,
                    data: tdChart.dataATSMSG,
                    backgroundColor: TrafficDemand.bgATSMSG,
                    borderColor: 'rgba(60, 120, 216, 1)',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isATFM) {
                datasets.push({
                    label: 'GDP',
                    stack: stack,
                    order: 7,
                    data: tdChart.dataATFM,
                    backgroundColor: TrafficDemand.bgATFM,
                    borderColor: 'rgba(109, 158, 235, 1)',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isFPL) {
                datasets.push({
                    label: 'FPL',
                    stack: stack,
                    order: 8,
                    data: tdChart.dataFPL,
                    backgroundColor: TrafficDemand.bgFPL,
                    borderColor: 'rgba(164, 194, 244, 1)',
                    borderWidth: 1
                });
            }
            if (tdChart.trafficDemand.isSCH) {
                datasets.push({
                    label: 'SCH',
                    stack: stack,
                    order: 9,
                    data: tdChart.dataSCH,
                    backgroundColor: TrafficDemand.bgSCH,
                    borderColor: 'rgba(164, 194, 244, 0.7)',
                    borderWidth: 1
                });
            }
            // Kick-off Delayed Flights (beyond recovery period)
            if (tdChart.gdp) {
                datasets.push({
                    label: 'DLA',
                    stack: stack,
                    order: 10,
                    data: tdChart.dataKickoffDelay,
                    backgroundColor: TrafficDemand.bgKickoffDelay,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                });
            }
        }
        var title = tdChart.title;
        if (tdChart.trafficDemand.id >= 0) title = [tdChart.trafficDemand.designator, tdChart.title];
        var chart = new Chart($tdChart, {
            type: 'bar',
            data: {
                labels: tdChart.labels,
                datasets: datasets
            },
            options: {
                title: {
                    display: true,
                    text: title
                },
                legend: {
                    labels: {
                        boxWidth: 15
                    }
                },
                tooltips: {
                    enabled: false, // disable the built-in tooltips
                    mode: 'index',
                    custom: function (tooltipModel) {
                        // Create tooltip element if it doesn't exist
                        var tooltipEl = document.getElementById("chartjs-tooltip");
                        if (!tooltipEl) {
                            tooltipEl = document.createElement("div");
                            tooltipEl.id = "chartjs-tooltip";
                            document.body.appendChild(tooltipEl);
                        }
                        // Hide tooltip if not visible
                        if (tooltipModel.opacity === 0) {
                            tooltipEl.style.opacity = 0;
                            return;
                        }
                        // Set tooltip text (mimic built-in content)
                        if (tooltipModel.body) {
                            var title = tooltipModel.title || [];
                            var bodyLines = tooltipModel.body.map(b => b.lines);
                            var innerHtml = "";
                            if (title.length) {
                                innerHtml += "<div style='font-weight:bold;margin-bottom:4px'>" + title.join(" ") + "</div>";
                            }
                            innerHtml += "<div>" + bodyLines.join("<br>") + "</div>";
                            tooltipEl.innerHTML = innerHtml;
                        }
                        // Get chart canvas position
                        var position = this._chart.canvas.getBoundingClientRect();
                        // Center on hovered bar (x) and place at bottom outside canvas (y)
                        tooltipEl.style.opacity = 1;
                        tooltipEl.style.left = position.left + window.pageXOffset + tooltipModel.caretX + "px";
                        tooltipEl.style.top = position.bottom + window.pageYOffset + 5 + "px"; // 5px outside canvas
                    },
                    filter: function (tooltipItem, data) {
                        var dataset = data.datasets[tooltipItem.datasetIndex];
                        var order = dataset.order || 0;
                        var value = dataset.data[tooltipItem.index];
                        // Only include datasets with non-zero values and order not 13-14 (capacity)
                        return value !== 0 && ![13, 14].includes(order);
                    }
                },
                scales: {
                    xAxes: [{
                        type: 'category',
                        stacked: true
                    }, {
                        type: 'category',
                        labels: tdChart.labels.concat(' '),
                        display: false,
                        id: 'x-axis-line'
                    }],
                    yAxes: [{
                        ticks: {
                            beginAtZero: true,
                            precision: 0
                        }
                    }]
                }
            },
            plugins: [
                {
                    // runs after all bars are drawn and every time chart.update() is called
                    afterDatasetsDraw: function (chart) {
                        var ctx = chart.ctx;
                        ctx.save();
                        ctx.font = "bold 11px sans-serif";
                        ctx.textAlign = "center";
                        ctx.textBaseline = "bottom";
                        ctx.fillStyle = "gray";
                        chart.data.labels.forEach(function (label, index) {
                            // Compute the sum of dataset values at that index
                            var sum = 0;
                            chart.data.datasets.forEach(function (dataset) {
                                var value = dataset.data[index];
                                var order = dataset.order || 0;
                                if ([11, 12, 13, 14].includes(order)) {
                                    return; // skip excluded dataset orders (capacity datasets)
                                }
                                if (value) {
                                    sum += value;
                                }
                            });
                            // Find the top-most bar segment of the stacked bar at this index
                            var topElement = null;
                            for (var d = chart.data.datasets.length - 1; d >= 0; d--) {
                                var bar = chart.getDatasetMeta(d).data[index];
                                if (bar && !isNaN(chart.data.datasets[d].data[index])) {
                                    topElement = bar;
                                    break;
                                }
                            }
                            // Draw the sum slightly above that bar
                            if (topElement) {
                                var x = topElement._model.x;
                                var y = topElement._model.y;
                                ctx.fillText(sum, x, y - 1); // 1px above top bar
                            }
                        });
                        ctx.restore();
                    }
                }
            ]
        });
        TrafficDemand.addEventTdChart(tdChart, $tdChart, chart);
        return chart;
    },

    addEventTdChart: function (tdChart, $tdChart, chart) {
        $tdChart.off('click').on('click', function (e) {
            var activeElement = chart.getElementAtEvent(e);
            if (activeElement[0]) {
                var index = activeElement[0]._index;
                var $tdModal = $('#td-modal');
                $tdModal.find('.modal-title').text(tdChart.titles[index]);
                var $modalBody = $tdModal.find('.modal-body');
                $modalBody.find('.td-waypoint, .td-sector, .td-gdp').hide();
                if (tdChart.trafficDemand.trafficAreaId == 2) $modalBody.find('.td-waypoint').show();
                else if (tdChart.trafficDemand.trafficAreaId == 3) $modalBody.find('.td-sector').show();
                var $tbody = $modalBody.find('tbody').html('');
                var isGDP = !!tdChart.gdp;
                if (isGDP) $modalBody.find('.td-gdp').show();
                if (tdChart.flightDepLists && tdChart.flightDepLists[index]) {
                    var flightDepList = tdChart.flightDepLists[index];
                    for (var i = 0; i < flightDepList.length; i++) {
                        var $tr = TrafficDemand.createTrFromFlightData(flightDepList[i], i + 1, tdChart.trafficDemand, true, isGDP);
                        if (flightDepList[i].flightSourceId == 1) $tr.css('background-color', TrafficDemand.bgSCHDep);
                        else if (flightDepList[i].flightSourceId == 2) $tr.css('background-color', TrafficDemand.bgFPLDep);
                        else if (flightDepList[i].flightSourceId == 3) $tr.css('background-color', TrafficDemand.bgATFMDep);
                        else if (flightDepList[i].flightSourceId >= 4) $tr.css('background-color', TrafficDemand.bgATSMSGDep);
                        $tbody.append($tr);
                    }
                }
                if (tdChart.flightLists && tdChart.flightLists[index]) {
                    var flightList = tdChart.flightLists[index];
                    for (var i = 0; i < flightList.length; i++) {
                        var $tr = TrafficDemand.createTrFromFlightData(flightList[i], i + 1, tdChart.trafficDemand, false, isGDP);
                        if (flightList[i].isKickoffDelay) $tr.css('background-color', TrafficDemand.bgKickoffDelay);
                        else if (flightList[i].flightSourceId == 1) $tr.css('background-color', TrafficDemand.bgSCH);
                        else if (flightList[i].flightSourceId == 2) $tr.css('background-color', TrafficDemand.bgFPL);
                        else if (flightList[i].flightSourceId == 3) $tr.css('background-color', TrafficDemand.bgATFM);
                        else if (flightList[i].flightSourceId == 4) $tr.css('background-color', TrafficDemand.bgATSMSG);
                        else if (flightList[i].flightSourceId == 5 || flightList[i].flightSourceId == 6) $tr.css('background-color', TrafficDemand.bgSUR);
                        else if (flightList[i].flightSourceId == 7) $tr.css('background-color', TrafficDemand.bgPassed);
                        $tbody.append($tr);
                    }
                }
                $tdModal.modal('show');
            }
        });
    },

    createTrFromFlightData: function (flightData, index, trafficDemand, isDep, isGDP) {
        var $tr = $('<tr></tr>');
        $('<td></td>').text(index).appendTo($tr);
        $('<td></td>').text(flightData.flight.callsign).appendTo($tr);
        $('<td></td>').text(flightData.flight.airportDeparture).appendTo($tr);
        $('<td></td>').text(flightData.flight.airportArrival).appendTo($tr);
        $('<td></td>').text(flightData.obtStr).appendTo($tr);
        var $tot = $('<td></td>');
        var $ldt = $('<td></td>');
        if (trafficDemand.trafficAreaId == 1) {
            if (isDep) $tot = $('<th></th>');
            else $ldt = $('<th></th>');
        }
        $tot.text(flightData.totStr).appendTo($tr);
        if (trafficDemand.trafficAreaId == 2) $('<th></th>').text(flightData.toStr).appendTo($tr);
        else if (trafficDemand.trafficAreaId == 3) {
            $('<th></th>').text(flightData.inbStr).appendTo($tr);
            $('<td></td>').text(flightData.outbStr).appendTo($tr);
        }
        $ldt.text(flightData.ldtStr).appendTo($tr);
        if (isGDP) $('<td></td>').text(flightData.atfmDelay).appendTo($tr);
        var title = flightData.flight.callsign;
        if (flightData.sobtStr) title += '<br>SOBT ' + flightData.sobtStr;
        if (flightData.eobtStr) title += '<br>EOBT ' + flightData.eobtStr;
        if (flightData.cobtStr) title += '<br>COBT ' + flightData.cobtStr;
        if (flightData.aobtStr) title += '<br>AOBT ' + flightData.aobtStr;
        title += '<br>------';
        if (flightData.stotStr) title += '<br>STOT ' + flightData.stotStr;
        if (flightData.etotStr) title += '<br>ETOT ' + flightData.etotStr;
        if (flightData.ctotStr) title += '<br>CTOT ' + flightData.ctotStr;
        if (flightData.atotStr) title += '<br>ATOT ' + flightData.atotStr;
        if (flightData.toStr || flightData.inbStr) title += '<br>------';
        if (flightData.stoStr) title += '<br>STO ' + flightData.stoStr;
        if (flightData.etoStr) title += '<br>ETO ' + flightData.etoStr;
        if (flightData.ctoStr) title += '<br>CTO ' + flightData.ctoStr;
        if (flightData.etoByDepStr) title += '<br>TO(DEP) ' + flightData.etoByDepStr;
        if (flightData.etoBySurStr) title += '<br>TO(SUR) ' + flightData.etoBySurStr;
        if (flightData.etoByTMCSStr) title += '<br>TO(TMCS) ' + flightData.etoByTMCSStr;
        if (flightData.atoStr) title += '<br>ATO ' + flightData.atoStr;
        if (flightData.sinbStr) title += '<br>SINB ' + flightData.sinbStr;
        if (flightData.einbStr) title += '<br>EINB ' + flightData.einbStr;
        if (flightData.cinbStr) title += '<br>CINB ' + flightData.cinbStr;
        if (flightData.einbByDepStr) title += '<br>INB(DEP) ' + flightData.einbByDepStr;
        if (flightData.einbBySurStr) title += '<br>INB(SUR) ' + flightData.einbBySurStr;
        if (flightData.einbByTMCSStr) title += '<br>INB(TMCS) ' + flightData.einbByTMCSStr;
        if (flightData.ainbStr) title += '<br>AINB ' + flightData.ainbStr;
        if (flightData.soutbStr) title += '<br>SOUTB ' + flightData.soutbStr;
        if (flightData.eoutbStr) title += '<br>EOUTB ' + flightData.eoutbStr;
        if (flightData.coutbStr) title += '<br>COUTB ' + flightData.coutbStr;
        if (flightData.eoutbByDepStr) title += '<br>OUTB(DEP) ' + flightData.eoutbByDepStr;
        if (flightData.eoutbBySurStr) title += '<br>OUTB(SUR) ' + flightData.eoutbBySurStr;
        if (flightData.eoutbByTMCSStr) title += '<br>OUTB(TMCS) ' + flightData.eoutbByTMCSStr;
        if (flightData.aoutbStr) title += '<br>AOUTB ' + flightData.aoutbStr;
        title += '<br>------';
        if (flightData.sldtStr) title += '<br>SLDT ' + flightData.sldtStr;
        if (flightData.eldtStr) title += '<br>ELDT ' + flightData.eldtStr;
        if (flightData.cldtStr) title += '<br>CLDT ' + flightData.cldtStr;
        if (flightData.eldtByDepStr) title += '<br>LDT(DEP) ' + flightData.eldtByDepStr;
        if (flightData.eldtBySurStr) title += '<br>LDT(SUR) ' + flightData.eldtBySurStr;
        if (flightData.eldtByTMCSStr) title += '<br>LDT(TMCS) ' + flightData.eldtByTMCSStr;
        if (flightData.aldtStr) title += '<br>ALDT ' + flightData.aldtStr;
        $tr.tooltip({
            title: title,
            placement: 'right',
            html: true
        });
        return $tr;
    },

    showError: function (error) {
        var $errorToast = $('#error-toast');
        $errorToast.find('.toast-body').text(error);
        $errorToast.toast('show');
    },

    showWarning: function (error) {
        var $errorToast = $('#warning-toast');
        $errorToast.find('.toast-body').text(error);
        $errorToast.toast('show');
    }
};