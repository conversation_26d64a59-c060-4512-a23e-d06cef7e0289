﻿
/* GDP Information Management */
$(document).ready(function () {
    GDPManage.init();
    $('input[type=datetime]').datetimepicker({
        format: 'YYYY-MM-DD HH:mm',
        formatTime: 'HH:mm',
        formatDate: 'YYYY-MM-DD'
    });
});

GDPManage = {
    gdpObj: new Object(), // id => gdp
    eventGdpObj: new Object(),// forfilter
    capacityObj: new Object(), // point => capacityPerHr
    eventObj: new Object(), // id => capacityEvent
    gdpArray: new Array(), //array to collect gdpObj
    tdCharts: [],
    tdTables: [],
    isGraph: false,
    isTable: false,
    isPreload: false,
    airports: [],
    fixes: [],
    sectors: [],
    ctotResult: new Object(),
    isEdit: false,
    isIFR: false,
    isVFR: false,

    init: function () {
        GDPManage.userPermissions();
        GDPManage.eventParamOptionCheck();
        GDPManage.addEventAreaSelect();
        GDPManage.initSortableTimelineContainer();
        GDPManage.initPreloadChart();
        GDPManage.eventFilterSelect();
        GDPManage.eventCapacitySelect();
        GDPManage.eventPointInput();
        GDPManage.eventAddGdp();
        GDPManage.requestTrafficDemandInfo();
        GDPManage.eventEditGdpModalForm();
        /* GDPManage.eventChangeModalBtn(); remove change state */
        GDPManage.eventCtotMannualRadio();
        setInterval(GDPManage.updateChartsTables, 60000);
        setInterval(GDPManage.updateGdpInfo, 60000)
    },
    eventDatetimeFormat: function () {
        $("input[type=datetime]").on("change", function () {
            this.setAttribute(
                "data-date",
                moment(this.value, "YYYY-MM-DD")
                    .format(this.getAttribute("data-date-format"))
            )
        }).trigger("change")

    },
    userPermissions: function () {
        $.ajax({
            type: 'GET',
            url: '/api/FrontEnd',
            dataType: 'json',

            success: function (res) {
                for (var i = 0; i < res.length; i++) {
                    if (res[i] == "CtotChange" || res[i] == "AccessAll")
                        GDPManage.isEdit = true;
                }
            },
            error: function (req, status, error) {
                console.log(error);
            }
        });
    },

    initSortableTimelineContainer: function () {
        var $tdTableContainer = $("#td-table-container");
        $tdTableContainer.sortable({
            containment: "parent",
            cursor: "move",
            forcePlaceholderSize: true,
            placeholder: "bg-primary float-left mr-lg-3 mb-3"
        });
        $tdTableContainer.disableSelection();
    },
    initPreloadChart: function () {
        var data = {
            StartTime: null, //$('#StartTimeFilter').val().replace(' / ', ' '),
            EndTime: $('#EndTimeFilter').val().replace(' / ', ' '),
        };

        $('.gdp-info-icon').popover({
            placement: 'bottom',
            template: '<div class="popover" role="tooltip"><div class="arrow"></div><h3 class="popover-header"></h3><div class="popover-body"></div></div>',
            title: 'Character Explaination',//'<div class="info-tooltip-header">Character Explaination </br></div><div class="info-tooltip">(A) Landed</br>(P) Provisional</br>(EX) Expected</br>(E) Planned</br>(S) Scheduled</br>(C) Regulated</div>',
            content: '<span>(ARR)</span>Arrival</br><span>(SUR)</span>Surveillance</br><span>(DEP)</span>Departure</br><span>(FPL)</span>Flightplan</br><span>(SCH)</span>Scheduled</br><span>(GDP)</span>Regulated',
            html: true
        });

        var gdp = { trafficAreaId: $('#TrafficAreaId').val() };
        if ($('#Point').val()) gdp.point = $('#Point').val();
        $('#loading-overlay').show();
        $.ajax({
            type: 'POST',
            url: '/GDPManage/GetGdpInfo',
            dataType: 'json',
            data: data,
            success: function (info) {
                //clear option 
                GDPManage.gdpObj = {};
                $.each(info.gdpExecuted, function (i, item) {
                    GDPManage.gdpObj[item.id] = item;
                });
                GDPManage.capacityObj = {};
                $("#event-capacity-select option[value !='-1']").each(function () {
                    $(this).remove();
                });

                GDPManage.filterGdpObject(gdp);

                var $eventSelect = $('#event-capacity-select');
                $.each(info.capacities, function (i, item) {
                    GDPManage.capacityObj[item.point] = item.capacityPerHr;
                });
                $.each(info.capacityEvents, function (i, item) {
                    $eventSelect.append($('<option>', {
                        value: item.id,
                        text: item.reason ? item.reason : GDPManage.formatDateTime(item.startTime) + ' (' + item.capacityPerHr + ')'
                    }));
                    GDPManage.eventObj[item.id] = item;
                });

                $.ajax({
                    type: 'GET',
                    url: '/GDPManage/GetFlightTrial',
                    dataType: 'json',
                    success: function (ctotResult) {
                        GDPManage.ctotResult = ctotResult;
                        GDPManage.isGraph = true;
                        GDPManage.isTable = true;
                        if (info.gdpExecuted.length > 0) { //info.gdpSaved    
                            $('.info-gdp').show();
                            GDPManage.requestChart(info.gdpExecuted, true, true);
                        }
                    },
                    error: function (req, status, error) {
                        console.log(error);
                        alert('Unable to get FlightTrial');
                    },
                    complete: function () {
                        $('#loading-overlay').hide();
                    }
                });
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to request GDP information');
            },
            complete: function () {
                // $('#loading-overlay').hide();
            }
        });
    },
    updateGdpInfo: function () {
        var data = {
            StartTime: null, //$('#StartTimeFilter').val().replace(' / ', ' '),
            EndTime: null, // get all active gdp
        };
        var gdp = {
            trafficAreaId: $('#TrafficAreaId').val(),
            point: $('#Point').val(),
            startTime: $('#StartTimeFilter').val(),
            endTime: $('#EndTimeFilter').val(),
        };
        /* if ($('#Point').val())
             gdp.point = $('#Point').val();*/
        $.ajax({
            type: 'POST',
            url: '/GDPManage/GetGdpInfo',
            dataType: 'json',
            data: data,
            success: function (info) {
                var gdpUpdateArray = info.gdpExecuted;
                $.each(gdpUpdateArray, function (i, item) {
                    if (GDPManage.gdpObj[item.id]) {
                        if (GDPManage.gdpObj[item.id].isDisplayChart)
                            item.isDisplayChart = GDPManage.gdpObj[item.id].isDisplayChart;
                        if (GDPManage.gdpObj[item.id].isDisplayTable)
                            item.isDisplayTable = GDPManage.gdpObj[item.id].isDisplayTable;
                    }
                    else
                        item.isNew = true;
                });
                $.each(gdpUpdateArray, function (i, item) {
                    GDPManage.gdpObj[item.id] = item;
                });

                GDPManage.capacityObj = {};
                $("#event-capacity-select option[value !='-1']").each(function () {
                    $(this).remove();
                });

                GDPManage.filterGdpObject(gdp);

                var $eventSelect = $('#event-capacity-select');
                $.each(info.capacities, function (i, item) {
                    GDPManage.capacityObj[item.point] = item.capacityPerHr;
                });
                $.each(info.capacityEvents, function (i, item) {
                    $eventSelect.append($('<option>', {
                        value: item.id,
                        text: item.reason ? item.reason : GDPManage.formatDateTime(item.startTime) + ' (' + item.capacityPerHr + ')'
                    }));
                    GDPManage.eventObj[item.id] = item;
                });

            },
            error: function (req, status, error) {
                console.log(error);
                console.log('Unable to request GDP information');
            }

        });
    },
    eventCtotMannualRadio: function () {
        $('input[name="ctotradio"]').on('change', function (e) {
            if ($('#mannualRadio').is(':checked'))
                $('.mannual-ctot-group').show();
            else
                $('.mannual-ctot-group').hide();
        });
    },
    eventFocus: function (tdObj) {
        var $chartBlock = tdObj.container.parent();
        console.log('eventFocus');
        $chartBlock.on('click', function () {
            $(this).toggleClass('border-thick')
                .siblings().removeClass('border-thick');

            /*   var id = tdObj.tdChart.gdp.id;
               if (tdObj.table) {
                   $.each(GDPManage.tdCharts, function (i, item) {
                       if (item.tdChart.gdp.id == id) {
                           var $chartBlocklink = item.container.parent();
                           $chartBlocklink.toggleClass('border-thick')
                               .siblings().removeClass('border-thick');
                       }
                   });
               }
               if (tdObj.chart) {
                   $.each(GDPManage.tdTables, function (i, item) {
                       if (item.tdChart.gdp.id == id) {
                           var $chartBlocklink = item.container.parent();
                           $chartBlocklink.toggleClass('border-thick')
                               .siblings().removeClass('border-thick');
                       }
                   });
               }*/ //remove parallel toggle
        });
    },
    eventFlightClick: function (tdObj) {
        var $tr = tdObj.table.find('tr');
        $tr.on('click', function () {
            $(this).toggleClass('tr-activate')
                .siblings().removeClass('tr-activate');

            //var id = tdObj.tdChart.gdp.id;
            //if (tdObj.table) {
            //    $.each(GDPManage.tdCharts, function (i, item) {
            //        if (item.tdChart.gdp.id == id) {
            //            var $chartBlocklink = item.container.parent();
            //            $chartBlocklink.toggleClass('border-thick')
            //                .siblings().removeClass('border-thick');
            //        }
            //    });
            //}
            //if (tdObj.chart) {
            //    $.each(GDPManage.tdTables, function (i, item) {
            //        if (item.tdChart.gdp.id == id) {
            //            var $chartBlocklink = item.container.parent();
            //            $chartBlocklink.toggleClass('border-thick')
            //                .siblings().removeClass('border-thick');
            //        }
            //    });
            //}
        });
    },


    /*eventChangeModalBtn: function () {
        $changeModal = $('#change-state-modal');
        $changeModal.find('.execute-btn, .undo-btn').on('click', function () {
            $changeModal.modal('hide');

            var obj = new Object();
            var tdChartObj = $changeModal.data('tdChartObj');
            var tdTableObj = $changeModal.data('tdTableObj');

            if (tdChartObj) obj = tdChartObj;
            if (tdTableObj) obj = tdTableObj;

            if (tdChartObj) {
                var id = tdChartObj.tdChart.gdp.id;
                $.each(GDPManage.tdTables, function (i, item) {
                    if (item.tdChart.gdp.id == id) {
                        tdTableObj = item;
                    }
                });
            }
            if (tdTableObj) {
                var id = tdTableObj.tdChart.gdp.id;
                $.each(GDPManage.tdCharts, function (i, item) {
                    if (item.tdChart.gdp.id == id) {
                        tdChartObj = item;
                    }
                });
            }

            var $designator = $('#measure-designator-filter');
            var stateId = obj.tdChart.gdp.isExecuted ? 2 : 1;
            stateId += $(this).data('state');
            var regul = $changeModal.find('.regul').val();
            var regcause = $changeModal.find('.regcause').val();
            var comment = $changeModal.find('.comment').val();
            $.ajax({
                type: 'POST',
                url: '/GDP/ChangeState',
                dataType: 'json',
                data: {
                    gdpId: obj.tdChart.gdp.id,
                    stateId: stateId,
                    regul: regul,
                    regcause: regcause,
                    comment: comment
                },
                success: function (gdpChart) {
                    if (!gdpChart.gdp) {
                        var $toast = $('#error-toast');
                        $toast.find('.toast-body').text(gdpChart.title);
                        $toast.toast('show');
                        return;
                    }
                    var $toast = $('#success-toast');
                    $toast.find('.toast-body').text('Regulated demand state (' + gdpChart.gdp.designator + ') is successfully changed.');
                    $toast.find('.success-toast-time').text(gdpChart.time);
                    $toast.toast('show');
                    // var $designator = $('#measure-designator');
                    $designator.children().each(function () {
                        var $this = $(this);
                        if ($this.val() == gdpChart.gdp.id) {
                            $this.remove();
                            return false;
                        }
                    });
                    GDPManage.gdpObj[gdpChart.gdp.id] = gdpChart.gdp;
                    if (gdpChart.gdp.isExecuted) {
                        $designator.append($('<option>', {
                            value: gdpChart.gdp.id,
                            text: gdpChart.gdp.designator
                        }));
                        if (tdChartObj) {
                            tdChartObj.container.parent().addClass('border-danger').removeClass('border-info');
                            tdChartObj.container.siblings('.state').text('EX').addClass('text-danger').removeClass('text-info');
                            tdChartObj.container.siblings('.td-chart-btn').find('.td-chart-edit').show();
                            tdChartObj.tdChart.gdp = gdpChart.gdp;
                        }
                        if (tdTableObj) {
                            tdTableObj.container.parent().addClass('border-danger').removeClass('border-info');
                            tdTableObj.container.siblings('.state').text('EX').addClass('text-danger').removeClass('text-info');
                            tdTableObj.container.siblings('.td-table-btn').find('.td-table-edit').show();
                            tdTableObj.tdChart.gdp = gdpChart.gdp;
                        }
                    }
                    //isSave
                    else if (!gdpChart.gdp.isCancelled) {
                        if (tdChartObj) {
                            tdChartObj.container.parent().addClass('border-info').removeClass('border-danger');
                            tdChartObj.container.siblings('.state').text('S').addClass('text-info').removeClass('text-danger');
                            tdChartObj.container.siblings('.td-chart-btn').find('.td-chart-edit').hide();
                            tdChartObj.tdChart.gdp = gdpChart.gdp;


                        }
                        if (tdTableObj) {
                            tdTableObj.container.parent().addClass('border-info').removeClass('border-danger');
                            tdTableObj.container.siblings('.state').text('S').addClass('text-info').removeClass('text-danger');
                            tdTableObj.tdChart.gdp = gdpChart.gdp;
                            tdTableObj.container.siblings('.td-table-btn').find('.td-table-edit').hide();
                            tdTableObj.table.find('.td-edit').hide();


                        }

                    }
                    else {
                        if (tdChartObj) {
                            var $chartBtn = tdChartObj.container.siblings('.td-chart-btn');
                            $chartBtn.find('.td-chart-change').hide();
                            $chartBtn.find('.td-chart-edit').hide();
                            var chartTitle = tdChartObj.chart.options.title.text;
                            tdChartObj.chart.options.title.text = chartTitle[1];
                            tdChartObj.chart.update();
                            tdChartObj.container.parent().removeClass('border border-info rounded-lg');
                            tdChartObj.container.siblings('.state').remove();
                            tdChartObj.container.siblings('.td-chart-btn').removeClass('mt-1 mr-2');

                        }
                        if (tdTableObj) {
                            var $chartBtn = tdTableObj.container.siblings('.td-table-btn');
                            $chartBtn.find('.td-table-change').hide();
                            $chartBtn.find('.td-table-edit').hide();
                            tdTableObj.container.siblings('.td-table-header').find('.td-table-title').text('');
                            tdTableObj.container.parent().removeClass('border border-info rounded-lg');
                            tdTableObj.container.siblings('.state').remove();
                            tdTableObj.container.siblings('.td-table-btn').removeClass('mt-1 mr-2');
                            tdTableObj.table.find('.td-edit').hide();

                        }

                    }
                    //tdChartObj.tdChart.trafficDemand = tdChart.trafficDemand;
                },
                error: function (req, status, error) {
                    console.log(error);
                    alert('Unable to change regulated demand state');
                }
            });

        });
    }, remove change state*/
    eventParamOptionCheck: function () {
        $('#CheckGraph').on('click', function () {
            if ($("#CheckGraph").is(":checked")) GDPManage.isGraph = true;
            else GDPManage.isGraph = false;
        });
        $('#CheckTable').on('click', function () {
            if ($("#CheckTable").is(":checked")) GDPManage.isTable = true;
            else GDPManage.isTable = false;
        });
    },
    filterGdpObject: function (gdp) {
        //GDPManage.eventGdpObj = {}; clear obj
        GDPManage.gdpArray = [];
        $("#measure-designator-filter option[value !='-1']").each(function () {
            $(this).remove();
        });

        $('input[type="checkbox"]').prop('checked', false);
        GDPManage.isGraph = false;
        GDPManage.isTable = false;

        var $designator = $('#measure-designator-filter');
        var $last = $designator.children().last();


        $.each(GDPManage.gdpObj, function (i, item) {//gdpSaved
            if (item.isExecuted) {
                var filter = false;
                if (gdp.trafficAreaId && gdp.trafficAreaId == item.trafficAreaId) {
                    if (gdp.point != null) {
                        filter = false;
                        if (gdp.point == item.point)
                            filter = true;
                    }
                    else if (gdp.startTime != null) {
                        filter = false;
                        if (new Date(gdp.startTime) >= new Date(item.startTime) || new Date(gdp.startTime) <= new Date(item.endRecoveryTime))
                            filter = true;
                    }
                    else if (gdp.endTime != null) {
                        filter = false;
                        if (new Date(gdp.endTime) >= new Date(item.startTime) || new Date(gdp.endTime) >= new Date(item.endRecoveryTime))
                            filter = true;
                    }
                    else
                        filter = true;
                }
                if (filter) {
                    //$designator.before($('<option>', {
                    $last.after($('<option>', {
                        value: item.id,
                        text: item.designator
                    }));
                    //GDPManage.eventGdpObj[item.id] = item;
                    GDPManage.gdpArray.push(item);
                }
            }
        });
    },
    eventFilterSelect: function () {
        $('#TrafficAreaId').on('change', function (e) {
            $('#Point').val('');
            var gdp = {
                trafficAreaId: $(this).val(),
            }
            GDPManage.filterGdpObject(gdp);
        });
        $('#Point').on('change', function (e) {
            var gdp = {
                trafficAreaId: $('#TrafficAreaId').val(),
                point: $(this).val()
            }
            GDPManage.filterGdpObject(gdp);
        });
        $('#EndTimeFilter').on('change', function (e) {
            $('.end-time-err').hide();
            var isErr = false;
            if ($('#EndTimeFilter').val() && $('#StartTimeFilter').val()) {
                if ($(this).val() < $('#StartTimeFilter').val()) {
                    $('.end-time-err').show();
                    isErr = true;
                }
            }
            if (!isErr) {
                var gdp = { trafficAreaId: $('#TrafficAreaId').val() };
                if ($(this).val())
                    gdp.endTime = $(this).val();
                GDPManage.filterGdpObject(gdp);
            }
        });
        $('#StartTimeFilter').on('change', function (e) {
            $('.end-time-err').hide();
            var isErr = false;
            if ($('#EndTimeFilter').val() && $('#StartTimeFilter').val()) {
                if ($('#EndTimeFilter').val() < $('#StartTimeFilter').val()) {
                    $('.end-time-err').show();
                    isErr = true;
                }
            }
            if (!isErr) {
                var gdp = { trafficAreaId: $('#TrafficAreaId').val() };
                if ($(this).val())
                    gdp.startTime = $(this).val();
                GDPManage.filterGdpObject(gdp);
            }
        });
        //add intervalFilter
        $('#IntervalMinFilter').on('change', function (e) {
            GDPManage.filterGdpObject(gdp);
        });

    },


    requestTrafficDemandInfo: function () {
        $.ajax({
            type: 'POST',
            url: '/TrafficDemand/RequestTrafficDemandInfo',
            dataType: 'json',
            success: function (trafficDemandInfo) {

                GDPManage.airports = trafficDemandInfo.airports;
                GDPManage.fixes = trafficDemandInfo.fixes;
                GDPManage.sectors = trafficDemandInfo.staticAirspaces;
                $("#PointEdt").autocomplete({
                    source: GDPManage.airports
                });
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to request traffic demand information');
            }
        });
    },
    getGdpbyId: function ($editModal, tdChart) {

        if (!tdChart.gdp) {
            TrafficDemand.showError(gdpChart.title);
            return;
        }

        var gdp = tdChart.gdp;
        var $form = $('#EditGdpForm');
        $form.find('.field-validation-error span').html(''); // clear error

        $('#GdpId').val(gdp.id);
        $('#DesignatorEdt').val(gdp.designator);
        $('#TrafficAreaIdEdt').val(gdp.trafficAreaId);
        $('#PointEdt').val(gdp.point);
        $('#LowerFlightLevelEdt').val(gdp.lowerFlightLevel);
        $('#UpperFlightLevelEdt').val(gdp.upperFlightLevel);
        $('#RadiusNmEdt').val(gdp.radiusNm);
        $('#CapacityRecoveryPerHrEdt').val(gdp.capacityRecoveryPerHr);
        $('#event-capacity-select').val();
        $('#StartTimeEdt').val(GDPManage.formatDateTime(gdp.startTime));
        $('#EndTimeEdt').val(GDPManage.formatDateTime(gdp.endTime));
        $('#EndRecoveryTimeEdt').val(GDPManage.formatDateTime(gdp.endRecoveryTime));
        $('#CapacityPerHrEdt').val(gdp.capacityPerHr);
        $('#ExemptADEPEdt').val(gdp.exemptADEP);
        $('#ExemptADESEdt').val(gdp.exemptADES);
        $('#ExemptAirlinesEdt').val(gdp.exemptAirlines);

        GDPManage.isVFR = gdp.isVFR;
        GDPManage.isIFR = gdp.isIFR;

        var trafficAreaId = gdp.trafficAreaId

        if (trafficAreaId == 1) $editModal.find('.td-waypoint, .td-sector').hide();
        else {
            if (trafficAreaId == 2) {
                $editModal.find('.td-airport, .td-sector').hide();
                $editModal.find('.td-waypoint').show();
            } else {
                $editModal.find('.td-airport, .td-waypoint').hide();
                $editModal.find('.td-sector').show();
            }
        }

    },
    addEventAreaSelect: function () {
        $('#TrafficAreaIdEdt').on('change', function (e) {
            if (this.value == 1) {
                $('.form-row.td-airport').show();
                $('.form-row.td-waypoint,.form-row.td-sector').hide();
                //$isArr.trigger('change');
                $("#PointEdt").autocomplete("option", "source", GDPManage.airports);
            } else {
                $('.form-row.td-airport').hide();
                if (this.value == 2) {
                    $('.form-row.td-waypoint').show();
                    $("#PointEdt").autocomplete("option", "source", GDPManage.fixes);
                } else {
                    $('.form-row.td-waypoint').hide();
                    if (this.value == 3 || this.value == 4) {
                        $('.form-row.td-sector').show();
                    } else {
                        $('.form-row.td-sector').hide();
                    }
                    $("#PointEdt").autocomplete("option", "source", GDPManage.sectors);
                }
            }
        });
    },
    /*requestGdpInfo: function () {
        var data = {
            StartTime: $('#StartTimeFilter').val(),
            EndTime: $('#EndTimeFilter').val(),
        };

        var gdp = { trafficAreaId: $('#TrafficAreaId').val() };
        if ($('#Point').val()) gdp.point = $('#Point').val();
        $.ajax({
            type: 'POST',
            url: '/GDPManage/GetGdpInfo',
            dataType: 'json',
            data: data,
            success: function (info) {
                //clear option
                GDPManage.gdpObj = info;
                GDPManage.capacityObj = {};
                $("#event-capacity-select option[value !='-1']").each(function () {
                    $(this).remove();
                });
                GDPManage.filterGdpObject(gdp);

                var $eventSelect = $('#event-capacity-select');
                $.each(info.capacities, function (i, item) {
                    GDPManage.capacityObj[item.point] = item.capacityPerHr;
                });
                $.each(info.capacityEvents, function (i, item) {
                    $eventSelect.append($('<option>', {
                        value: item.id,
                        text: item.reason ? item.reason : GDPManage.formatDateTime(item.startTime) + ' (' + item.capacityPerHr + ')'
                    }));
                    GDPManage.eventObj[item.id] = item;
                });
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to request GDP information');
            }
        });
        //});
    }, */
    eventPointInput: function () {
        /* $('#PointFilter').on('blur', function () {
             $('#CapacityRecoveryPerHr').val(GDPManage.capacityObj[$(this).val()]).trigger('blur');
         });*/
        $('#PointEdt').on('blur', function () {
            $('#CapacityRecoveryPerHrEdt').val(GDPManage.capacityObj[$(this).val()]).trigger('blur');
        });
    },
    eventCapacitySelect: function () {
        $('#event-capacity-select').on('change', function () {
            var capacityEvent = GDPManage.eventObj[$(this).val()];
            if (capacityEvent) {
                $('#StartTimeEdt').val(GDPManage.formatDateTime(capacityEvent.startTime)).trigger('blur');
                $('#EndTimeEdt').val(GDPManage.formatDateTime(capacityEvent.endTime)).trigger('blur');
                $('#CapacityPerHrEdt').val(capacityEvent.capacityPerHr).trigger('blur');
            }
        });
    },
    requestChart: function (selectArray, isGraph, isTable) {
        //console.log("requestChart CTOT TRIAL " + JSON.stringify(GDPManage.ctotResult));
        for (var i = 0; i < selectArray.length; i++) {
            $('#loading-overlay').show();

            var data = {
                Id: selectArray[i].id,
                Designator: selectArray[i].designator,
                TrafficAreaId: $('#TrafficAreaId').val(),
                IntervalMin: $('#IntervalMinFilter').val()
            };
            // console.log('requestChart =' + JSON.stringify(data));
            $.ajax({
                type: 'POST',
                url: '/GDPManage/RequestChart',
                dataType: 'json',
                data: data,
                success: function (gdpChart) {
                    if (!gdpChart.gdp) {
                        TrafficDemand.showError(gdpChart.title);
                        return;
                    }
                    var id = gdpChart.gdp.id;
                    var intervalMin = gdpChart.gdp.intervalMin;
                    var isChartDuplicate = false;
                    var isTableDuplicate = false;

                    $.each(GDPManage.tdCharts, function (i, item) {
                        if (item.tdChart.gdp.id == id && item.tdChart.gdp.intervalMin == intervalMin) {
                            isChartDuplicate = true;
                            return;
                        }
                    });
                    $.each(GDPManage.tdTables, function (i, item) {
                        if (item.tdChart.gdp.id == id) {
                            isTableDuplicate = true;
                            return;
                        }
                    });
                    //console.log('isGraph = ' + isGraph + 'isChartDuplicate =' + isChartDuplicate);
                    if (isGraph && !isChartDuplicate) {
                        $('#HideChartBtn').show();
                        $('.mgm-chart').show();
                        var $tdChartContainer = $('#td-chart-container');
                        $tdChartContainer.append($tdChartContainer.children().eq(0).clone().show());
                        var $tdChart = $tdChartContainer.find('.td-chart').last();
                        var chart = TrafficDemand.createChart(gdpChart, $tdChart);
                        // GDPManage.addEventGdpChart(gdpChart, $tdChart, chart);
                        var tdChartObj = {
                            tdChart: gdpChart,
                            container: $tdChart,
                            chart: chart
                            //gdp: gdpChart.gdp
                        };
                        GDPManage.gdpObj[id] = gdpChart.gdp;
                        GDPManage.gdpObj[id].isDisplayChart = true;



                        GDPManage.tdCharts.push(tdChartObj);
                        TrafficDemand.addEventChartBtn(tdChartObj);
                        GDPManage.eventChartBtn(tdChartObj);
                        //GDP.eventChartBtn(tdChartObj);
                        GDPManage.eventFocus(tdChartObj);
                        //GDPManage.displayObj.gdpChart.gdp.id.chart = tdTableObj;
                    }

                    if (isTable && !isTableDuplicate) {
                        $('#HideTimelineBtn').show();
                        var $tdTableContainer = $('#td-table-container');
                        $tdTableContainer.append($tdTableContainer.children().eq(0).clone().show());

                        var $tdTable = $tdTableContainer.find('.td-table').last();
                        $tdTableContainer.find('.td-table-title').last().text(gdpChart.trafficDemand.designator);
                        $tdTableContainer.find('.td-table-subtitle').last().text(gdpChart.title);

                        var flightObj = {};
                        var flightArray = [];
                        for (var i = 0; i < gdpChart.flightLists.length; i++) {
                            for (var j = 0; j < gdpChart.flightLists[i].length; j++) {
                                flightArray.push(gdpChart.flightLists[i][j]);
                                flightObj[gdpChart.flightLists[i][j].flight.id] = gdpChart.flightLists[i][j];
                            }
                        }
                        var table = GDPManage.createGdpTable(gdpChart, $tdTable, flightArray);
                        var tdTableObj = {
                            //tdTable: gdpChart,
                            tdChart: gdpChart,
                            container: $tdTable,
                            table: table,
                            flights: flightArray,
                            flightObj: flightObj
                            //gdp: gdpChart.gdp
                        };
                        GDPManage.gdpObj[id].isDisplayTable = true;
                        GDPManage.tdTables.push(tdTableObj);
                        GDPManage.eventTableBtn(tdTableObj);
                        GDPManage.eventFlightTableBtn(tdTableObj);
                        GDPManage.eventFocus(tdTableObj);
                        //GDPManage.eventFlightClick(tdTableObj);                    
                    }
                    //
                    if (!GDPManage.isEdit)
                        $('.permission-btn').hide();
                },
                error: function (req, status, error) {
                    console.log(error);
                    alert('Unable to request GDP Chart');
                },
                complete: function () {
                    if (i == selectArray.length)
                        $('#loading-overlay').hide();
                }
            });
        }//for
    },
    addEventGdpChart: function (tdChart, $tdChart, chart) {
        $tdChart.off('click').on('click', function (e) {
            //console.log(' addEventGdpChart $tdChart.off(click)');
            var activeElement = chart.getElementAtEvent(e);
            if (activeElement[0]) {
                var index = activeElement[0]._index;
                var $tdModal = $('#td-modal');
                $tdModal.find('.modal-title').text(tdChart.titles[index]);
                var $modalBody = $tdModal.find('.modal-body');
                var $tbody = $modalBody.find('tbody').html('');

                $modalBody.find('.td-waypoint, .td-sector').hide();
                if (tdChart.trafficDemand.trafficAreaId == 2) $modalBody.find('.td-waypoint').show();
                else if (tdChart.trafficDemand.trafficAreaId == 3) $modalBody.find('.td-sector').show();

                if (tdChart.flightLists && tdChart.flightLists[index]) {
                    var flightList = tdChart.flightLists[index];
                    for (var i = 0; i < flightList.length; i++) {
                        if (flightList[i].isExempt)
                            $("td:eq(3)").css("color", "red");

                        var $tr = GDPManage.createTrFromFlightData(flightList[i], i + 1, tdChart.trafficDemand);
                        if (flightList[i].isKickoffDelay) $tr.css('background-color', TrafficDemand.bgKickoffDelay);
                        else if (flightList[i].flightSourceId == 1) $tr.css('background-color', TrafficDemand.bgSCH);
                        else if (flightList[i].flightSourceId == 2) $tr.css('background-color', TrafficDemand.bgFPL);
                        else if (flightList[i].flightSourceId == 3) $tr.css('background-color', TrafficDemand.bgATFM);
                        else if (flightList[i].flightSourceId == 4) $tr.css('background-color', TrafficDemand.bgATSMSG);
                        else if (flightList[i].flightSourceId == 5 || flightList[i].flightSourceId == 6) $tr.css('background-color', TrafficDemand.bgSUR);
                        else if (flightList[i].flightSourceId == 7) $tr.css('background-color', TrafficDemand.bgPassed);
                        $tbody.append($tr);
                    }
                }
                $tdModal.modal('show');
            }
        });
    },
    eventAddGdp: function () {
        // var isTableExist = false;
        // var isGraphExist = false;
        $('#SubmitAdd').on('click', function (e) {
            //clear border-toggle
            $.each(GDPManage.tdCharts, function (i, item) {
                var $chartBlocklink = item.container.parent();
                $chartBlocklink.removeClass('border-thick');
            });

            $.each(GDPManage.tdTables, function (i, item) {
                var $chartBlocklink = item.container.parent();
                $chartBlocklink.removeClass('border-thick');
            });

            if (($('#IntervalMinFilter').val() > 0 && $('#IntervalMinFilter').val() < 1441 && $('#IntervalMinFilter').val() != null) && $('.end-time-err').is(":hidden")) {

                if (!GDPManage.isTable && !GDPManage.isGraph) {
                    alert('please select display method');
                    return;
                }
                if (GDPManage.gdpArray < 1) {
                    alert('no gdp designator to display');
                    return;
                }

                var selectArray = new Array();
                if ($('#measure-designator-filter option:selected').val() < 0)
                    selectArray = GDPManage.gdpArray;
                else {
                    selectArray.push({
                        id: $('#measure-designator-filter option:selected').val(),
                        designator: $('#measure-designator-filter option:selected').text(),
                        intervalMin: $('#IntervalMinFilter').val()
                    });
                }

                /*  if (GDPManage.isTable) {
                      $.each(GDPManage.tdTables, function (i, item) {
                          if (item.tdChart.gdp.id == $('#measure-designator-filter option:selected').val()) {
                              alert('this gdp table already exist!');
                              isTableExist = true;
                          }
                      });
                  }
                  if (GDPManage.isGraph) {
                      $.each(GDPManage.tdCharts, function (i, item) {
                          if (item.tdChart.gdp.id == $('#measure-designator-filter option:selected').val()) {
                              alert('this gdp chart already exist!');
                              isGrahpExist = true;
                          }
                      });
                  }
                  if (!isGraphExist || !isTableExist)*/
                GDPManage.requestChart(selectArray, GDPManage.isGraph, GDPManage.isTable);

            }// valid

        });// submit evt

    },

    updateChartsTables: function () {
        if (GDPManage.tdCharts.length == 0 && GDPManage.tdTables.length == 0) {
            $('#td-update').hide();
            return;
        }
        /* Update flight trial lists */
        $.ajax({
            type: 'GET',
            url: '/GDPManage/GetFlightTrial',
            dataType: 'json',
            success: function (ctotResult) {
                console.log("G917: get flight trials" + JSON.stringify(ctotResult));
                GDPManage.ctotResult = ctotResult;
            },
            error: function (req, status, error) {
                //console.log(error);
                console.log('Unable to get FlightTrial');
            }
        });
        /* ADD NEW GDP */
        var selectedArray = [];
        var tdChartObj = null;
        var tdTableObj = null;
        $.each(GDPManage.gdpObj, function (key, value) {

            if (value.isNew) {
                selectedArray.push(value);
                value.isNew = false;
            }
            if (value.isDisplayChart || value.isDisplayTable) {
                var data = {
                    Id: value.id,
                    Designator: value.designator,
                    IntervalMin: value.intervalMin
                };

                $.each(GDPManage.tdCharts, function (i, item) {
                    if (item.tdChart.gdp.id == value.id) {
                        tdChartObj = item;
                    }
                });
                $.each(GDPManage.tdTables, function (i, item) {
                    if (item.tdChart.gdp.id == value.id) {
                        tdTableObj = item;
                    }
                });
                //console.log('b_updated data = ' + JSON.stringify(data));
                GDPManage.updateRequestChart(data, value, tdChartObj, tdTableObj);
            }
        });
        GDPManage.requestChart(selectedArray, true, true);
    },
    updateRequestChart: function (data, value, tdChartObj, tdTableObj) {
        //console.log(' updateRequestChart: = ' + JSON.stringify(data));
        var $tdUpdate = $('#td-update');
        var $tdUpdateTime = $tdUpdate.find('.td-update-time');
        $.ajax({
            type: 'POST',
            url: '/GDPManage/RequestChart',
            dataType: 'json',
            data: data,
            success: function (gdpChart) {
                $tdUpdate.show();
                $tdUpdateTime.text(gdpChart.time);
                if (gdpChart.gdp) {
                    if (value.isDisplayChart)
                        GDPManage.updateChartState(tdChartObj, gdpChart);
                    if (value.isDisplayTable)
                        GDPManage.updateTableState(tdTableObj, gdpChart);
                }
                else {
                    if (value.isDisplayChart)
                        tdChartObj.container.find('.td-chart-warning').show();
                    if (value.isDisplayTable)
                        tdTableObj.container.find('.td-table-warning').show();
                }
            },
            error: function (req, status, error) {
                console.log(error);
                if (value.isDisplayChart)
                    tdChartObj.container.find('.td-chart-warning').show();
                if (value.isDisplayTable)
                    tdTableObj.container.find('.td-table-warning').show();
            }
        });
    },
    updateChartState: function (tdChartObj, gdpChart) {
        var $chartBtn = tdChartObj.container.siblings('.td-chart-btn');
        var $chartBtnEdit = $chartBtn.find('.td-chart-edit');
        var $chartBtnChange = $chartBtn.find('.td-chart-change');
        var $chartBtnExport = $chartBtn.find('.td-chart-export');
        tdChartObj.container.parent().removeClass('not-active');
        GDPManage.gdpObj[gdpChart.gdp.id] = gdpChart.gdp;
        GDPManage.gdpObj[gdpChart.gdp.id].isDisplayChart = true;
        if (gdpChart.gdp.isExecuted) {
            tdChartObj.tdChart = gdpChart;
            /* if (!tdChartObj.container.parent().hasClass('border-danger')) {
                 tdChartObj.container.parent().addClass('border-danger').removeClass('border-info');
                 tdChartObj.container.siblings('.state').text('EX').addClass('text-danger').removeClass('text-info');
                 $chartBtnEdit.show();
             } remove change state*/
            if (gdpChart.gdp.isActive)
                GDPManage.updateChart(tdChartObj);
            else {
                tdChartObj.container.parent().addClass('not-active');
                $chartBtnEdit.hide();
                $chartBtnChange.hide();
                $chartBtnExport.hide();
            }
        }
        /* else if (!gdpChart.gdp.isCancelled) {
             if (!tdChartObj.container.parent().hasClass('border-info')) {
                 GDPManage.updateChart(tdChartObj);
                 tdChartObj.container.parent().addClass('border-info').removeClass('border-danger');
                 tdChartObj.container.siblings('.state').text('S').addClass('text-info').removeClass('text-danger');
                 $chartBtnEdit.hide();
             }
         }
         else {
             $chartBtnChange.hide();
             $chartBtnEdit.hide();
             var chartTitle = tdChartObj.chart.options.title.text;
             tdChartObj.chart.options.title.text = chartTitle[1];
             tdChartObj.chart.update();
             tdChartObj.container.parent().removeClass('border border-info rounded-lg');
             tdChartObj.container.siblings('.state').remove();
             tdChartObj.container.siblings('.td-chart-btn').removeClass('mt-1 mr-2');
         } remove change state*/
        tdChartObj.container.find('.td-chart-warning').hide();
    },
    updateTableState: function (tdTableObj, gdpChart) {
        var $tableBtn = tdTableObj.container.siblings('.td-table-btn');
        var $tableBtnEdit = $tableBtn.find('.td-table-edit');
        var $tableBtnChange = $tableBtn.find('.td-table-change');
        var $tableBtnExport = $tableBtn.find('.td-table-export');
        tdTableObj.container.parent().removeClass('not-active');
        GDPManage.gdpObj[gdpChart.gdp.id] = gdpChart.gdp;
        GDPManage.gdpObj[gdpChart.gdp.id].isDisplayTable = true;
        if (gdpChart.gdp.isExecuted) {
            tdTableObj.tdChart = gdpChart;

            /*  if (!tdTableObj.container.parent().hasClass('border-danger')) {
                  tdTableObj.container.parent().addClass('border-danger').removeClass('border-info');
                  tdTableObj.container.siblings('.state').text('EX').addClass('text-danger').removeClass('text-info');
                  $tableBtnEdit.show();
              } remove change state*/

            if (gdpChart.gdp.isActive)
                GDPManage.updateTable(tdTableObj, null);
            else {
                tdTableObj.container.parent().addClass('not-active');
                $tableBtnEdit.hide();
                $tableBtnChange.hide();
                $tableBtnExport.hide();
            }
        }
        /* else if (!gdpChart.gdp.isCancelled) {
            if (!tdTableObj.container.parent().hasClass('border-info')) {
                GDPManage.updateTable(tdTableObj, null);
                tdTableObj.container.parent().addClass('border-info').removeClass('border-danger');
                tdTableObj.container.siblings('.state').text('S').addClass('text-info').removeClass('text-danger');
                tdTableObj.container.siblings('.td-table-btn').find('.td-table-edit').hide();
                $tableBtnEdit.hide();
            }
        }
        else {
            $tableBtnChange.hide();
            $tableBtnEdit.hide();
            tdTableObj.container.siblings('.td-table-header').find('.td-table-title').text('');
            tdTableObj.container.parent().removeClass('border border-info rounded-lg');
            tdTableObj.container.siblings('.state').remove();
            tdTableObj.container.siblings('.td-table-btn').removeClass('mt-1 mr-2');
            tdTableObj.table.find('.td-edit').hide();

        } remove change state*/
        tdTableObj.container.find('.td-chart-warning').hide();
    },

    updateTable: function (tdTableObj, fArray) {
        //console.log('updateTable CTOT TRIAL : ' + JSON.stringify(GDPManage.ctotResult));
        var gdpChart = tdTableObj.tdChart;
        // create table
        var flightArray = [];

        if (fArray != null)
            flightArray = fArray;
        else {

            for (var i = 0; i < gdpChart.flightLists.length; i++) {
                for (var j = 0; j < gdpChart.flightLists[i].length; j++) {
                    flightArray.push(gdpChart.flightLists[i][j]);
                }
            }
        }
        for (var i = 0; i < flightArray.length; i++) {
            if (tdTableObj.flightObj[flightArray[i].flight.id]) {
                flightArray[i].isRemove = false;
                if (tdTableObj.flightObj[flightArray[i].flight.id].isActive)
                    flightArray[i].isActive = true;
                delete tdTableObj.flightObj[flightArray[i].flight.id];

            }
        }

        var ajaxRequests = [];
        $.each(tdTableObj.flightObj, function (key, value) {
            // console.log('trafficAreaId => ' + JSON.stringify(gdpChart.gdp.trafficAreaId));

            /*  GDPManage.getFlightDataUpdate(value, gdpChart.gdp.id);//trafficAreaId
             //value.isRemove = true;
           
             flightArray.push(value);*/


            var ajaxCall = GDPManage.getFlightDataUpdate(value, gdpChart.gdp.id).done(function (response) {
                value = response;
                value.isRemove = true;
                flightArray.push(value);

            }).fail(function () {
                console.error("Failed to get flight data for", value);
            });
            ajaxRequests.push(ajaxCall); // Add each AJAX request to the array
        });
        // Wait for all AJAX requests to complete before calling another function
        $.when.apply($, ajaxRequests).done(function () {
            flightArray.sort(function (a, b) {
                if (tdTableObj.tdChart.gdp.trafficAreaId == 1)
                    return moment(a.ldt) - moment(b.ldt);
                if (tdTableObj.tdChart.gdp.trafficAreaId == 2)
                    return moment(a.to) - moment(b.to);
                if (tdTableObj.tdChart.gdp.trafficAreaId > 2)
                    return moment(a.inb) - moment(b.inb);

            });

            tdTableObj.container.siblings('.td-table-header').find('.td-table-title').text(gdpChart.trafficDemand.designator); // error not update
            tdTableObj.container.siblings('.td-table-header').find('.td-table-subtitle').text(gdpChart.title); // error not update
            tdTableObj.table.find('.tr').each(function (index) {
                $(this).removeData('tooltip');
            });
            $('.tooltip').remove();
            var table = GDPManage.createGdpTable(tdTableObj.tdChart, tdTableObj.container, flightArray);

            var flightObj = {};
            for (var i = 0; i < flightArray.length; i++) {
                flightObj[flightArray[i].flight.id] = flightArray[i];
            }
            var tdTableObjUpdate = {
                tdChart: gdpChart,
                container: tdTableObj.container,
                table: table,
                flights: flightArray,
                flightObj: flightObj
            };
            $.each(GDPManage.tdTables, function (i, item) {
                if (item.tdChart.gdp.id == tdTableObj.tdChart.gdp.id) {
                    item = tdTableObjUpdate;
                }
            });

            GDPManage.eventFlightTableBtn(tdTableObjUpdate);
            GDPManage.eventTableBtn(tdTableObjUpdate);
            GDPManage.eventFocus(tdTableObjUpdate);
            var $tdTableFilter = tdTableObjUpdate.container.siblings('.td-table-filter');
            var $tdTableTr = tdTableObjUpdate.container.find('tbody tr');

            if ($tdTableFilter.val() !== null) {
                var value = $tdTableFilter.val().toLowerCase();
                // tdTableObj.filterCallsign = value;
                $tdTableTr.filter(function () {
                    //$(this).toggle($(this).find('.td-callsign').text().toLowerCase().indexOf(value) > -1)
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            }
            $tdTableFilter.on("keyup", function () {
                var value = $(this).val().toLowerCase();            
                $tdTableTr.filter(function () {
                    //$(this).toggle($(this).find('.td-callsign').text().toLowerCase().indexOf(value) > -1)
                    $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
                });
            });

            for (var i = 0; i < GDPManage.tdTables.length; i++) {
                if (GDPManage.tdTables[i].container == tdTableObj.container) GDPManage.tdTables[i] = tdTableObjUpdate;
            }
            //toggle comment
            tdTableObjUpdate.container.find('td.td-comment > span').off('click').on('click', function () {
                $(this).toggleClass("show");
            });
        });
    },

    getFlightDataUpdate: function (value, gid) // add function to get latest flight data in case flight is out of GDP (อาจเกิดจาก Actual อยู่นอก GDP ไปแล้ว )
    {
        var flightid = value.flight.id;
        //console.log("call GDP Data update " + flightid);
        return $.ajax({
            url: '/GDPManage/GetFlightDataUpdate/' + flightid + '/' + gid,   // Replace with actual API
            type: 'GET',
            dataType: 'json'
        });

        /* $.ajax({
             type: 'GET',
             url: '/GDPManage/GetFlightDataUpdate/' + flightid +'/'+ gid ,
             dataType: 'json',
 
             success: function (res) {
                 //console.log("getRemoved Flight");
                 //console.log(res);
                 //value.isRemove = true;
                 
                // flightArray.push(value);
             },
             error: function (req, status, error) {
                 console.log(error);
             }
         });*/
    },

    updateChart: function (tdChartObj) {

        // tdChartObj.tdChart = gdpChart;
        var gdpChart = tdChartObj.tdChart;
        var datasets = [];
        var bgColor = 'rgba(255, 99, 132, 0.1)';
        var borderColor = 'rgb(255, 99, 132)';
        if (gdpChart.capacitiesDep) {
            bgColor = 'rgba(255, 159, 64, 0.1)';
            borderColor = 'rgb(255, 159, 64)';
            datasets.push({
                type: 'line',
                label: 'Capacity-Dep',
                order: 0,
                backgroundColor: bgColor,
                borderColor: borderColor,
                borderWidth: 1,
                fill: 'origin',
                pointRadius: 0,
                pointHoverRadius: 0,
                steppedLine: 'before',
                xAxisID: 'x-axis-line',
                data: gdpChart.capacitiesDep
            });
            bgColor = 'rgba(153, 102, 255, 0.1)';
            borderColor = 'rgb(153, 102, 255)';
        }
        datasets.push({
            type: 'line',
            label: 'Capacity',
            order: 1,
            backgroundColor: bgColor,
            borderColor: borderColor,
            borderWidth: 1,
            fill: 'origin',
            pointRadius: 0,
            pointHoverRadius: 0,
            steppedLine: 'before',
            xAxisID: 'x-axis-line',
            data: gdpChart.capacities
        });

        if (gdpChart.trafficDemand.trafficAreaId == 1 && gdpChart.trafficDemand.isDep) {
            if (gdpChart.trafficDemand.isATSMSG) {
                datasets.push({
                    label: 'Departed',
                    stack: '0',
                    order: 2,
                    data: gdpChart.dataATSMSGDep,
                    backgroundColor: TrafficDemand.bgATSMSGDep,
                    borderColor: 'rgba(127, 96, 0, 1)',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isATFM) {
                datasets.push({
                    label: 'Regulated-Dep',
                    stack: '0',
                    order: 3,
                    data: gdpChart.dataATFMDep,
                    backgroundColor: TrafficDemand.bgATFMDep,
                    borderColor: 'rgba(191, 144, 0, 1))',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isFPL) {
                datasets.push({
                    label: 'Planned-Dep',
                    stack: '0',
                    order: 4,
                    data: gdpChart.dataFPLDep,
                    backgroundColor: TrafficDemand.bgFPLDep,
                    borderColor: 'rgba(241, 194, 50, 1)',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isSCH) {
                datasets.push({
                    label: 'Scheduled-Dep',
                    stack: '0',
                    order: 5,
                    data: gdpChart.dataSCHDep,
                    backgroundColor: TrafficDemand.bgSCHDep,
                    borderColor: 'rgba(241, 194, 50, 0.7)',
                    borderWidth: 1
                });
            }
            if (gdpChart.gdp) {
                datasets.push({
                    label: 'DLA',
                    stack: stack,
                    order: 6,
                    data: gdpChart.dataKickoffDelay,
                    backgroundColor: TrafficDemand.bgKickoffDelay,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                });
            }
        }
        if ((gdpChart.trafficDemand.trafficAreaId == 1 && gdpChart.trafficDemand.isArr) || gdpChart.trafficDemand.trafficAreaId != 1) {
            var stack = '1';
            var label = 'Passed';
            if (gdpChart.trafficDemand.trafficAreaId == 1) {
                label = 'ARR';
                if (gdpChart.trafficDemand.isCombined) stack = '0';
            }
            if (gdpChart.trafficDemand.isPassed) {
                datasets.push({
                    label: label,
                    stack: stack,
                    order: 6,
                    data: gdpChart.dataPassed,
                    backgroundColor: TrafficDemand.bgPassed,
                    borderColor: 'rgba(102, 102, 102, 1)',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isSUR) {
                datasets.push({
                    label: 'SUR',
                    stack: stack,
                    order: 7,
                    data: gdpChart.dataSUR,
                    backgroundColor: TrafficDemand.bgSUR,
                    borderColor: 'rgba(28, 69, 135, 1)',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isATSMSG) {
                datasets.push({
                    label: 'DEP',
                    stack: stack,
                    order: 8,
                    data: gdpChart.dataATSMSG,
                    backgroundColor: TrafficDemand.bgATSMSG,
                    borderColor: 'rgba(60, 120, 216, 1)',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isATFM) {
                datasets.push({
                    label: 'GDP',
                    stack: stack,
                    order: 9,
                    data: gdpChart.dataATFM,
                    backgroundColor: TrafficDemand.bgATFM,
                    borderColor: 'rgba(109, 158, 235, 1)',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isFPL) {
                datasets.push({
                    label: 'FPL',
                    stack: stack,
                    order: 10,
                    data: gdpChart.dataFPL,
                    backgroundColor: TrafficDemand.bgFPL,
                    borderColor: 'rgba(164, 194, 244, 1)',
                    borderWidth: 1
                });
            }
            if (gdpChart.trafficDemand.isSCH) {
                datasets.push({
                    label: 'SCH',
                    stack: stack,
                    order: 11,
                    data: gdpChart.dataSCH,
                    backgroundColor: TrafficDemand.bgSCH,
                    borderColor: 'rgba(164, 194, 244, 0.7)',
                    borderWidth: 1
                });
            }
            if (gdpChart.gdp) {
                datasets.push({
                    label: 'DLA',
                    stack: stack,
                    order: 12,
                    data: gdpChart.dataKickoffDelay,
                    backgroundColor: TrafficDemand.bgKickoffDelay,
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                });
            }
        }

        tdChartObj.chart.data.datasets = datasets;

        tdChartObj.chart.data.datasets.forEach((dataset) => {
            if (dataset.order == 0) dataset.data = gdpChart.capacitiesDep;
            else if (dataset.order == 1) dataset.data = gdpChart.capacities;
            else if (dataset.order == 2) dataset.data = gdpChart.dataATSMSGDep;
            else if (dataset.order == 3) dataset.data = gdpChart.dataATFMDep;
            else if (dataset.order == 4) dataset.data = gdpChart.dataFPLDep;
            else if (dataset.order == 5) dataset.data = gdpChart.dataSCHDep;
            else if (dataset.order == 6) dataset.data = gdpChart.dataPassed;
            else if (dataset.order == 7) dataset.data = gdpChart.dataSUR;
            else if (dataset.order == 8) dataset.data = gdpChart.dataATSMSG;
            else if (dataset.order == 9) dataset.data = gdpChart.dataATFM;
            else if (dataset.order == 10) dataset.data = gdpChart.dataFPL;
            else if (dataset.order == 11) dataset.data = gdpChart.dataSCH;
            else if (dataset.order == 12) dataset.data = gdpChart.dataKickoffDelay;
        });

        tdChartObj.chart.options.scales.xAxes[1].labels = gdpChart.labels.concat(' ')
        tdChartObj.chart.options.title.text = [gdpChart.gdp.designator, gdpChart.title];
        tdChartObj.chart.data.labels = gdpChart.labels;
        tdChartObj.chart.update();
        TrafficDemand.addEventTdChart(gdpChart, tdChartObj.container, tdChartObj.chart);
        TrafficDemand.addEventChartBtn(tdChartObj);
        GDPManage.eventChartBtn(tdChartObj);
        GDPManage.eventFocus(tdChartObj);

        for (var i = 0; i < GDPManage.tdCharts.length; i++) {
            if (GDPManage.tdCharts[i].container == tdChartObj.container) GDPManage.tdCharts[i] = tdChartObj;
        }
    },
    createTrFromFlightData: function (flightData, index, trafficDemand) {

        // issent = ismanage 
        flightData.isSent = flightData.flight.isManaged;
        //console.log("flightData = " + JSON.stringify(flightData));

        var $tr = $('<tr fid="' + flightData.flight.id + '"></tr>');
        $('<td></td>').text(index).appendTo($tr);

        if (flightData.isGdpExempt || flightData.isFlightExempt)
            $('<td class="td-callsign text-danger"></td>').text(flightData.flight.callsign).appendTo($tr);
        else
            $('<td class="td-callsign"></td>').text(flightData.flight.callsign).appendTo($tr);

        var $tot = $('<td></td>');
        var $ldt = $('<td></td>');
        if (trafficDemand.trafficAreaId == 1)
            $ldt = $('<th></th>');
        $tot.text(flightData.totStr).appendTo($tr);
        if (trafficDemand.trafficAreaId == 2) $('<th></th>').text(flightData.toStr).appendTo($tr);
        else if (trafficDemand.trafficAreaId == 3) {
            $('<th></th>').text(flightData.inbStr).appendTo($tr);
            $('<td></td>').text(flightData.outbStr).appendTo($tr);
        }
        $ldt.text(flightData.ldtStr).appendTo($tr);
        var tdEdit = '<button type="button" fid="' + flightData.flight.id + '" class="btn btn-link no-padding edit-flight-btn">Edit</button>';
        var tdExempt = '<button type="button" fid="' + flightData.flight.id + '" class="btn btn-link no-padding exempt-flight-btn remove-flight-btn" removestr="exempt">Exempt</button>'; //add exempt button
        var tdExclude = '<button type="button" fid="' + flightData.flight.id + '" class="btn btn-link no-padding exclude-flight-btn remove-flight-btn" removestr="exclude">Exclude</button>'; //add exclude button
        var tdEditExemptExclude = tdEdit + ' | ' + tdExempt + ' | ' + tdExclude;
        var tdExemptExclude = tdExempt + ' | ' + tdExclude;
        var tdCtot = '<u>New CTOT Available</u>';
        var tdStatusSuccess = '<span class="badge badge-success">Sent</span>';
        var tdStatus = '';//<span class="badge badge-info">Waiting</span>

        if (flightData.newEobt != null && flightData.newEobt != "") {
            //$('.td-new-eobt').show();
            $('<th class="td-new-eobt"></th>').text(moment(flightData.newEobt).format("HH:mm") + ' (New)').appendTo($tr);
        }
        else
            $('<td class="td-new-eobt"></td>').text('').appendTo($tr);

        var isCtot = false;
        var nowUtcString = moment.utc().toISOString().replace('Z', '');
        var current = moment(nowUtcString);
        var count = 0;
        var remain = moment.duration(GDPManage.ctotResult.ctotTimeout).minutes();
        var expired = GDPManage.ctotResult.ctotTimeout;// 1800000  600000
        var tdListCtot = '';

        if (GDPManage.ctotResult.ctotFlights != null) {
            for (var i = 0; i < GDPManage.ctotResult.ctotFlights.length; i++) {
                if (flightData.flight.id == GDPManage.ctotResult.ctotFlights[i].flightId) {
                    //console.log("CTOT TRIAL" + JSON.stringify(GDPManage.ctotResult.ctotFlights[i])); 
                    var start = moment(GDPManage.ctotResult.ctotFlights[i].timeSaved);
                    var diff = current.diff(start);
                    if (diff < expired && remain > 0) {
                        count = count + 1;
                        tdListCtot += '<h6>' + moment(GDPManage.ctotResult.ctotFlights[i].ctot).format("HH:mm") + ' (C' + count + ')' + '</h6>';
                        isCtot = true;
                        remain = moment.duration(expired - diff).minutes();
                        flightData.comment = GDPManage.ctotResult.ctotFlights[i].comment;

                    }
                    // console.log('remain from gen = ' + remain);                   
                }
            }
            tdCtot += '<div class="expired-time">*CTOT expires in ' + (remain + 1) + ' min</div>';
            tdCtot += tdListCtot;
            tdCtot += '<button type="button" fid="' + flightData.flight.id + '" class="btn btn-link no-padding cancel-ctot-btn" >Cancel Request</button>';
            tdCtot += ' | ';
            tdCtot += '<button type="button" fid="' + flightData.flight.id + '" class="btn btn-link no-padding confirm-ctot-btn">Select CTOT</button>';
        }
        if (flightData.isSent)
            $('<td></td>').html(tdStatusSuccess).appendTo($tr);
        else
            $('<td></td>').appendTo($tr);

        if (GDPManage.isEdit && flightData.isSent) {
            if (flightData.atotStr == null && flightData.aldtStr == null && !isCtot && !(flightData.isGdpExempt || flightData.isFlightExempt) && !flightData.isRemove)//flightData.atotStr == null 
                $('<td class="td-edit"></td>').html(tdEditExemptExclude).appendTo($tr);
            else if (isCtot && !flightData.isRemove)
                $('<td class="td-edit"></td>').html(tdCtot).appendTo($tr);
            else
                $('<td class="td-edit"></td>').html('').appendTo($tr);
        }
        else
            $('<td class="td-edit"></td>').html(tdExemptExclude).appendTo($tr);


        if (flightData.comment != null || flightData.username != null) {
            var tdComment = '<span>';
            tdComment += (flightData.comment != null) ? flightData.comment : "";
            if (flightData.username != null)
                tdComment += '<span  style ="font-size: smaller"><i> By  ' + flightData.username + '</i></span>';
            tdComment += '</span>'
            $('<td class="td-comment"></td>').html(tdComment).appendTo($tr);
        }
        else
            $('<td class="td-comment"></td>').appendTo($tr);


        var title = '<b>' + flightData.flight.callsign + '</b>';
        title += '<br>ADEP ' + flightData.flight.airportDeparture;
        title += '<br>ADES ' + flightData.flight.airportArrival;
        title += '<br>OBT ' + flightData.obtStr;

        if (flightData.sobtStr) title += '<br>SOBT ' + flightData.sobtStr;
        if (flightData.eobtStr) title += '<br>EOBT ' + flightData.eobtStr;
        if (flightData.cobtStr) title += '<br>COBT ' + flightData.cobtStr;
        if (flightData.aobtStr) title += '<br>AOBT ' + flightData.aobtStr;
        title += '<br>------';
        if (flightData.stotStr) title += '<br>STOT ' + flightData.stotStr;
        if (flightData.etotStr) title += '<br>ETOT ' + flightData.etotStr;
        if (flightData.ctotStr) title += '<br>CTOT ' + flightData.ctotStr;
        if (flightData.atotStr) title += '<br>ATOT ' + flightData.atotStr;
        if (flightData.toStr || flightData.inbStr) title += '<br>------';
        if (flightData.stoStr) title += '<br>STO ' + flightData.stoStr;
        if (flightData.etoStr) title += '<br>ETO ' + flightData.etoStr;
        if (flightData.ctoStr) title += '<br>CTO ' + flightData.ctoStr;
        if (flightData.etoByDepStr) title += '<br>TO(DEP) ' + flightData.etoByDepStr;
        if (flightData.etoBySurStr) title += '<br>TO(SUR) ' + flightData.etoBySurStr;
        if (flightData.etoByTMCSStr) title += '<br>TO(TMCS) ' + flightData.etoByTMCSStr;
        if (flightData.atoStr) title += '<br>ATO ' + flightData.atoStr;
        if (flightData.sinbStr) title += '<br>SINB ' + flightData.sinbStr;
        if (flightData.einbStr) title += '<br>EINB ' + flightData.einbStr;
        if (flightData.cinbStr) title += '<br>CINB ' + flightData.cinbStr;
        if (flightData.einbByDepStr) title += '<br>INB(DEP) ' + flightData.einbByDepStr;
        if (flightData.einbBySurStr) title += '<br>INB(SUR) ' + flightData.einbBySurStr;
        if (flightData.einbByTMCSStr) title += '<br>INB(TMCS) ' + flightData.einbByTMCSStr;
        if (flightData.ainbStr) title += '<br>AINB ' + flightData.ainbStr;
        if (flightData.soutbStr) title += '<br>SOUTB ' + flightData.soutbStr;
        if (flightData.eoutbStr) title += '<br>EOUTB ' + flightData.eoutbStr;
        if (flightData.coutbStr) title += '<br>COUTB ' + flightData.coutbStr;
        if (flightData.eoutbByDepStr) title += '<br>OUTB(DEP) ' + flightData.eoutbByDepStr;
        if (flightData.eoutbBySurStr) title += '<br>OUTB(SUR) ' + flightData.eoutbBySurStr;
        if (flightData.eoutbByTMCSStr) title += '<br>OUTB(TMCS) ' + flightData.eoutbByTMCSStr;
        if (flightData.aoutbStr) title += '<br>AOUTB ' + flightData.aoutbStr;
        title += '<br>------';
        if (flightData.sldtStr) title += '<br>SLDT ' + flightData.sldtStr;
        if (flightData.eldtStr) title += '<br>ELDT ' + flightData.eldtStr;
        if (flightData.cldtStr) title += '<br>CLDT ' + flightData.cldtStr;
        if (flightData.eldtByDepStr) title += '<br>LDT(DEP) ' + flightData.eldtByDepStr;
        if (flightData.eldtBySurStr) title += '<br>LDT(SUR) ' + flightData.eldtBySurStr;
        if (flightData.eldtByTMCSStr) title += '<br>LDT(TMCS) ' + flightData.eldtByTMCSStr;
        if (flightData.aldtStr) title += '<br>ALDT ' + flightData.aldtStr;
        $tr.tooltip({
            title: title,
            placement: 'right',
            html: true
        });
        return $tr;
    },

    createGdpTable: function (gdpChart, $tdTable, fplArray) {
        //console.log("CreateGDPTable call CTOT TRIAL  = " + JSON.stringify(GDPManage.ctotResult));
        console.log("")
        $tdTable.find('.td-waypoint, .td-sector').hide();
        if (gdpChart.trafficDemand.trafficAreaId == 2) $tdTable.find('.td-waypoint').show();
        else if (gdpChart.trafficDemand.trafficAreaId == 3) $tdTable.find('.td-sector').show();

        var $tbody = $tdTable.find('tbody').html('');
        var haveComment = false;
        var haveNewEobt = false;
        if (gdpChart.flightLists) {
            var flightList = fplArray;
            for (var i = 0; i < flightList.length; i++) {
                var $tr = GDPManage.createTrFromFlightData(flightList[i], i + 1, gdpChart.trafficDemand);
                if (flightList[i].isKickoffDelay) $tr.css('background-color', TrafficDemand.bgKickoffDelay);
                else if (flightList[i].flightSourceId == 1) $tr.css('background-color', TrafficDemand.bgSCH);
                else if (flightList[i].flightSourceId == 2) $tr.css('background-color', TrafficDemand.bgFPL);
                else if (flightList[i].flightSourceId == 3) $tr.css('background-color', TrafficDemand.bgATFM);
                else if (flightList[i].flightSourceId == 4) $tr.css('background-color', TrafficDemand.bgATSMSG);
                else if (flightList[i].flightSourceId == 5 || flightList[i].flightSourceId == 6) $tr.css('background-color', TrafficDemand.bgSUR);
                else if (flightList[i].flightSourceId == 7) $tr.css('background-color', TrafficDemand.bgPassed);
                if (flightList[i].isRemove)
                    $tr.addClass('tr-remove');
                if (flightList[i].isActive)
                    $tr.addClass('tr-activate');
                $tbody.append($tr);
                if (flightList[i].newEobt != null && flightList[i].newEobt != "")
                    haveNewEobt = true;
                if ((flightList[i].comment != null && flightList[i].comment != "") || (flightList[i].username != null && flightList[i].username != ""))
                    haveComment = true;
            }
        }

        if (haveNewEobt) $tdTable.find('.td-new-eobt').show();
        else $tdTable.find('.td-new-eobt').hide();
        if (haveComment) $tdTable.find('.td-comment').show();
        else $tdTable.find('.td-comment').hide();

        return $tbody;
        // });
    },
    eventEditGdpModalForm: function () {
        var $editModal = $('#edit-gdp-modal');
        var $editForm = $editModal.find('#EditGdpForm');
        var $editRemove = $editForm.find('.remove-gdp');
        var $editPreview = $editForm.find('.preview-gdp');
        var $editBtn = $editForm.find('.edit-form');
        var $previewContainer = $('.preview-container');

        $editRemove.on('click', function (e) {
            var $confirmModal = $('#confirm-modal');
            var $designator = $('#measure-designator-filter');
            var tdChartObj = $editForm.data('tdChartObj');
            var tdTableObj = $editForm.data('tdTableObj');
            var obj = new Object();
            var data = new Object();
            if (tdChartObj) obj = tdChartObj.tdChart.gdp;
            if (tdTableObj) obj = tdTableObj.tdChart.gdp;

            $editModal.modal('hide');

            /* confirm modal  */
            $confirmModal.find('.modal-title').text('Cancel GDP');
            var $TableTitle = $('<i></i>').append(obj.designator);

            $confirmModal.find('.modal-body p').html('Do you want to cancel the following GDP?<br><br>').append($TableTitle);
            $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                $editModal.modal('hide');
                $confirmModal.modal('hide');
                $('#loading-overlay').show();
                var id = obj.id;
                if (tdChartObj) {
                    $.each(GDPManage.tdTables, function (i, item) {
                        if (item.tdChart.gdp.id == id) {
                            tdTableObj = item;
                        }
                    });
                }
                if (tdTableObj) {
                    $.each(GDPManage.tdCharts, function (i, item) {
                        if (item.tdChart.gdp.id == id) {
                            tdChartObj = item;
                        }
                    });
                }

                data.id = obj.id;
                data.designator = $('#DesignatorEdt').val();
                data.trafficAreaId = $('#TrafficAreaIdEdt').val();
                data.point = $('#PointEdt').val();
                data.lowerFlightLevel = $('#LowerFlightLevelEdt').val();
                data.upperFlightLevel = $('#UpperFlightLevelEdt').val();
                data.radiusNm = $('#RadiusNmEdt').val();
                data.capacityPerHr = $('#CapacityPerHrEdt').val();
                data.startTime = $('#StartTimeEdt').val().replace('/', '');
                data.endTime = $('#EndTimeEdt').val().replace('/', '');
                data.endRecoveryTime = $('#EndRecoveryTimeEdt').val().replace('/', '');
                data.capacityRecoveryPerHr = $('#CapacityRecoveryPerHrEdt').val();
                data.exemptADEP = $('#ExemptADEPEdt').val();
                data.exemptADES = $('#ExemptADESEdt').val();
                data.exemptAirlines = $('#ExemptAirlinesEdt').val();
                data.intervalMin = obj.intervalMin;
                data.isCancelled = true;
                data.isVFR = GDPManage.isVFR;
                data.isIFR = GDPManage.isIFR;

                $.ajax({
                    type: 'POST',
                    url: '/GDPManage/Edit',
                    dataType: 'json',
                    data: data,
                    success: function (gdpChart) {
                        $('#loading-overlay').hide();
                        if (!gdpChart.gdp && gdpChart.title == "gdp not found") {
                            $editModal.modal('hide');
                            var tdToast = $('#success-toast');
                            tdToast.find('.toast-body').text('Regulated demand parameter (' + data.designator + ') is successfully cancelled.');
                            tdToast.find('.success-toast-time').text(gdpChart.time);
                            tdToast.toast('show');
                            $designator.children().each(function () {
                                var $this = $(this);
                                if ($this.val() == data.id) {
                                    $this.remove();
                                    return false;
                                }
                            });

                            if (tdTableObj) {
                                GDPManage.gdpObj[tdTableObj.tdChart.gdp.id] = null;
                                tdTableObj.container.parent().remove();
                                if ($("#td-table-container").children().length < 2)
                                    $('#HideTimelineBtn').hide();
                                for (var i = 0; i < GDPManage.tdTables.length; i++) {
                                    if (GDPManage.tdTables[i].container == tdTableObj.container) GDPManage.tdTables.splice(i, 1);
                                }
                            }
                            if (tdChartObj) {
                                GDPManage.gdpObj[tdChartObj.tdChart.gdp.id] = null;
                                tdChartObj.container.parent().remove();
                                if ($("#td-chart-container").children().length < 2)
                                    $('#HideChartBtn').hide();
                                for (var i = 0; i < GDPManage.tdCharts.length; i++) {
                                    if (GDPManage.tdCharts[i].container == tdChartObj.container) GDPManage.tdCharts.splice(i, 1);
                                }
                            }
                        }
                    },
                    error: function (req, status, error) {
                        console.log(error);
                        alert('Unable to edit gdp measure');
                    }
                });
            });
            $confirmModal.modal({
                show: true,
                backdrop: 'static'
            });
        });
        //update gdp
        $editForm.on('submit', function (e) {
            var $alert = $(this).children().find('.alert');
            if ($(this).valid()) {
                e.preventDefault();
                var tdChartObj = $(this).data('tdChartObj');
                var tdTableObj = $(this).data('tdTableObj');
                var data = new Object();
                var obj = new Object();

                if (tdChartObj) obj = tdChartObj.tdChart.gdp;
                if (tdTableObj) obj = tdTableObj.tdChart.gdp;

                data.id = obj.id;
                data.designator = $('#DesignatorEdt').val();
                data.trafficAreaId = $('#TrafficAreaIdEdt').val();
                data.point = $('#PointEdt').val();
                data.lowerFlightLevel = $('#LowerFlightLevelEdt').val();
                data.upperFlightLevel = $('#UpperFlightLevelEdt').val();
                data.radiusNm = $('#RadiusNmEdt').val();
                data.capacityPerHr = $('#CapacityPerHrEdt').val();
                data.startTime = $('#StartTimeEdt').val().replace('/', '');
                data.endTime = $('#EndTimeEdt').val().replace('/', '');
                data.endRecoveryTime = $('#EndRecoveryTimeEdt').val().replace('/', '');
                data.capacityRecoveryPerHr = $('#CapacityRecoveryPerHrEdt').val();
                data.exemptADEP = $('#ExemptADEPEdt').val();
                data.exemptADES = $('#ExemptADESEdt').val();
                data.exemptAirlines = $('#ExemptAirlinesEdt').val();
                data.intervalMin = obj.intervalMin;
                data.isVFR = GDPManage.isVFR;
                data.isIFR = GDPManage.isIFR;

                $.ajax({
                    type: 'POST',
                    url: '/GDPManage/Edit',
                    dataType: 'json',
                    data: data,
                    success: function (gdpChart) {
                        if (!gdpChart.gdp) {

                            $alert.html('Error : ' + gdpChart.title);
                            $alert.show();
                            return;
                        }
                        $alert.hide();
                        $editModal.modal('hide');

                        var tdToast = $('#success-toast');
                        tdToast.find('.toast-body').text('Regulated demand parameter (' + data.designator + ') is successfully saved.');
                        tdToast.find('.success-toast-time').text(gdpChart.time);
                        tdToast.toast('show');

                        var id = gdpChart.gdp.id;
                        $.each(GDPManage.tdTables, function (i, item) {
                            if (item.tdChart.gdp.id == id) {
                                item.tdChart = gdpChart;
                                item.flightObj = {};
                                GDPManage.updateTable(item, null);
                            }
                        });
                        $.each(GDPManage.tdCharts, function (i, item) {
                            if (item.tdChart.gdp.id == id) {
                                item.tdChart = gdpChart;
                                GDPManage.updateChart(item);
                            }
                        });
                    },
                    error: function (req, status, error) {
                        console.log(error);
                        alert('Unable to edit gdp measure');
                    }
                });
            }
        });
        //preview-gdp
        $editPreview.on('click', function (e) {
            var preview = '<i class="fa fa-spinner fa-spin"></i>';
            $editPreview.prepend(preview);


            $editForm.find('.form-group').find('input').attr("disabled", true);
            $editForm.find('.form-group').find('select').attr("disabled", true);

            var tdChartObj = $editForm.data('tdChartObj');
            var tdTableObj = $editForm.data('tdTableObj');
            var data = new Object();
            var obj = new Object();

            if (tdChartObj) obj = tdChartObj.tdChart.gdp;
            if (tdTableObj) obj = tdTableObj.tdChart.gdp;

            data.id = -1;// obj.id;
            data.designator = $('#DesignatorEdt').val();
            data.trafficAreaId = $('#TrafficAreaIdEdt').val();
            data.point = $('#PointEdt').val();
            data.lowerFlightLevel = $('#LowerFlightLevelEdt').val();
            data.upperFlightLevel = $('#UpperFlightLevelEdt').val();
            data.radiusNm = $('#RadiusNmEdt').val();
            data.capacityPerHr = $('#CapacityPerHrEdt').val();
            data.startTime = $('#StartTimeEdt').val().replace('/', '');
            data.endTime = $('#EndTimeEdt').val().replace('/', '');
            data.endRecoveryTime = $('#EndRecoveryTimeEdt').val().replace('/', '');
            data.capacityRecoveryPerHr = $('#CapacityRecoveryPerHrEdt').val();
            data.exemptADEP = $('#ExemptADEPEdt').val();
            data.exemptADES = $('#ExemptADESEdt').val();
            data.exemptAirlines = $('#ExemptAirlinesEdt').val();
            data.isExecuted = false; //ให้ generate gdp ใหม่
            data.intervalMin = obj.intervalMin;
            // Add VFR/IFR
            data.isIFR = GDPManage.isIFR;
            data.isVFR = GDPManage.isVFR;

            $.ajax({
                type: 'POST',
                url: '/GDP/RequestChart',
                dataType: 'json',
                data: data,
                success: function (gdpChart) {
                    if (gdpChart.gdp) {
                        var $tdChart = $previewContainer.find('.td-chart-preview');
                        var chart = TrafficDemand.createChart(gdpChart, $tdChart);
                        //GDPManage.addEventGdpChart(gdpChart, $tdChart, chart);
                        $('.gdp-edit-btn-group').hide();
                        $('.gdp-chart-btn-group').show();
                        $editModal.find('.modal-dialog').addClass('modal-xl');
                        $previewContainer.show();
                    }
                    else
                        alert('Unable to request GDP Chart');
                },
                error: function (req, status, error) {
                    console.log(error);
                    alert('Unable to request GDP Chart');
                },
                complete: function () {
                    $editPreview.find('i').remove();
                    //$('#loading-overlay').hide();
                }
            });
        });
        //edit gdp click
        $editBtn.on('click', function () {
            $('.gdp-edit-btn-group').show();
            $('.gdp-chart-btn-group').hide();

            $previewContainer.find('.td-chart-preview').remove();
            $previewContainer.prepend('<canvas class="td-chart-preview"></canvas>');
            $editModal.find('.modal-dialog').removeClass('modal-xl');
            $previewContainer.hide();


            $editForm.find('.form-group').find('input').attr("disabled", false);
            $editForm.find('.form-group').find('select').attr("disabled", false);
        });

    },
    neweobtValid: function (newEobt, etot, eobt) {
        var nowUtcString = moment.utc().toISOString().replace('Z', '');
        var nowUtc = moment(nowUtcString);
        var freeze = GDPManage.ctotResult.newEobtBlock; // 45 min
        var freezeUtc = nowUtc.add(freeze, 'minutes');

        // if (moment(newEobt) > moment(freezeUtc).subtract(moment(etot) - moment(eobt)))
        if (moment(newEobt) > nowUtc)
            return true;
        else
            return false;
    },

    /******************* Helper Functions *********************/
    eventEditFlightModal: function (modalObj) {
        var $flightModal = $('#edit-flight-modal');
        var $flightModalEdit = $flightModal.find('.confirm-edit-flight-btn');
        //var $flightModalRemove = $flightModal.find('.remove-flight-btn'); celegana move to other event
        var $confirmModal = $('#confirm-modal');


        $flightModalEdit.on('click', function () {
            var $newEobt = $('#EobtFlightEdt').val();
            var $comment = $('#Comment').val();
            $flightModal.find('.comment-error').html('');
            $flightModal.find('.eobt-error').html('');
            if (GDPManage.neweobtValid($newEobt, modalObj.flight.etot, modalObj.flight.eobt)) {
                $flightModal.modal('hide');


                $confirmModal.find('.modal-title').text('Change OBT Confirmation');
                //var $TableTitle = $('<i></i>').append($tdTableTitle);
                var $table = $('<table class="table table-borderless td-table"></table>');
                var $thead = $('<thead><tr><th scope="col">Callsign</th><th scope="col">EOBT (FPL)</th><th scope="col">New OBT</th></tr></thead>');
                var $tbody = $('<tbody><tr></tr></tbody>');
                $('<td></td>').html(modalObj.flight.callsign).appendTo($tbody);
                $('<td></td>').html(GDPManage.formatDateTime(modalObj.flight.eobt)).appendTo($tbody);
                $('<td class="table-info"></td>').html($newEobt).appendTo($tbody);

                var $TableTitle = $table.append($thead).append($tbody);
                var confirmComment = ($comment != "" && $comment != null) ? $comment : "-";
                $confirmModal.find('.modal-body p').html('Confirm requesting new OBT for the following flight?<br><br>').append($TableTitle).append('<p><span style="font-weight:500">Comment  </span>' + confirmComment + '</p>');

                $confirmModal.modal({
                    show: true,
                    backdrop: 'static'
                });

                $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                    var preview = '<i class="fa fa-spinner fa-spin"></i>';
                    $confirmModal.find('.confirm-modal-btn').prepend(preview);
                    //test call change ctot                       
                    var data = {
                        gdpid: modalObj.gdp.id,
                        flight: modalObj.flight,
                        newEobt: $newEobt.replace('/', ''),
                        comment: $comment
                    };

                    $.ajax({
                        type: 'POST',
                        url: '/GDPManage/RequestCtot',
                        dataType: 'json',
                        data: data,
                        success: function (result) {
                            //GDPManage.ctotFlight = [];
                            $confirmModal.modal('hide');
                            console.log('trial resulr');
                            console.log(result);
                            if (!result.isGdpflight) { // not gdp
                                var $toast = $('#error-toast');
                                $toast.find('.toast-body').text(result.errMsg);
                                $toast.toast('show');
                                GDPManage.updateChartsTables();
                                return;
                            }
                            /* else {
                                 var $toast = $('#success-toast');
                                 $toast.find('.toast-body').text('Success Change CTOT');
                                 $toast.toast('show');
                                 //GDPManage.updateChartsTables();
                             } */
                            GDPManage.ctotResult = result;
                            /*   for (var i = 0; i < ctotFlights.length; i++) {
                                   GDPManage.ctotFlights.push(ctotFlights[i]);
                                 
                               }*/

                            var id = modalObj.gdp.id;
                            $.each(GDPManage.tdTables, function (i, item) {
                                if (item.tdChart.gdp.id == id) {
                                    GDPManage.updateTable(item, null);
                                }
                            });

                            $.each(GDPManage.tdCharts, function (i, item) {
                                if (item.tdChart.gdp.id == id)
                                    GDPManage.updateChart(item);
                            });



                            // }
                        },
                        error: function (req, status, error) {
                            console.log(error);
                            alert('Unable to request GDP information');
                        },
                        complete: function () {
                            $('#loading-overlay').hide();

                            $confirmModal.find('.fa-spin').remove();
                            console.log('requset cal ctot complete');
                        }
                    });
                });
            }
            else {
                $flightModal.find('.eobt-error').html('New OBT must not be earlier than the current time. ');
            }


            // $confirmModal.modal('show');
        });

        /* $flightModalRemove.on('click', function () {
             if (($flightModal.find('.comment').val() != "" && $flightModal.find('.comment').val() != null)) {
                 var removeStr = $(this).attr('removestr');
                 var CapitalremoveStr = removeStr.charAt(0).toUpperCase() + removeStr.slice(1);
                 var $flightInfo = modalObj.flightInfo.clone();
                 var comment = $flightModal.find('.comment').val();
                 $confirmModal.find('.modal-title').text('Confirm ' + CapitalremoveStr + ' Flight ' + modalObj.flight.callsign);
                 $confirmModal.find('.modal-body p').html('Do you want to ' + CapitalremoveStr + ' this following flight?<br><br>').append($flightInfo).append('<br/><p><span style="font-weight:500;">Comment  :  </span>' + comment + '</p>');
 
                 $flightModal.modal('hide');
                 $confirmModal.modal({
                     show: true,
                     backdrop: 'static'
                 });
 
                 $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                     var data = {
                         gdpid: modalObj.gdp.id,
                         fid: modalObj.flight.id,
                         removeStr: removeStr,
                         comment: comment // add later
                     };
                     GDPManage.removeCtotFlight(data, modalObj.flights, $confirmModal);
                 });
             }
             else
                 $flightModal.find('.comment-error').html('This field must not be blank');
         });*/
    },

    eventRemoveFlightModal: function (modalObj) {
        var $flightModal = $('#edit-flight-modal');
        var $flightModalRemove = $('.confirm-edit-flight-btn');
        var $confirmModal = $('#confirm-modal');
        $flightModalRemove.on('click', function () {
            if (($flightModal.find('.comment').val() != "" && $flightModal.find('.comment').val() != null)) {
                var removeStr = modalObj.removeStr;// $(this).attr('removestr');
                var CapitalremoveStr = removeStr.charAt(0).toUpperCase() + removeStr.slice(1);
                var $flightInfo = modalObj.flightInfo.clone();
                var comment = $flightModal.find('.comment').val();
                $confirmModal.find('.modal-title').text('Confirm ' + CapitalremoveStr + ' Flight ' + modalObj.flight.callsign);
                $confirmModal.find('.modal-body p').html('Do you want to ' + CapitalremoveStr + ' this following flight?<br><br>').append($flightInfo).append('<br/><p><span style="font-weight:500;">Comment  :  </span>' + comment + '</p>');

                $flightModal.modal('hide');
                $confirmModal.modal({
                    show: true,
                    backdrop: 'static'
                });

                $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                    var data = {
                        gdpid: modalObj.gdp.id,
                        fid: modalObj.flight.id,
                        removeStr: removeStr,
                        comment: comment // add later
                    };
                    GDPManage.removeCtotFlight(data, modalObj.flights, $confirmModal);
                });
            }
            else {
                $flightModal.find('.comment').addClass('comment-error-text');
                $flightModal.find('.comment-error').html('This field must not be blank');
            }
        });
    },

    eventCtotModalClose: function (countdown) {
        var $ctotModal = $('#ctot-modal');
        $ctotModal.on('hidden.bs.modal', function () {
            $('.expire-alert').html('CTOT Expires In ');
            clearInterval(countdown);
        })
    },
    isCtotRadioValid: function () {
        $('.comment-error').text('');

        if ($('input[name = "ctotradio"]').is(':checked'))
            return true;
        else {
            $('.comment-error').text('No Option Selected !');
            return false;
        }
    },

    eventFlightTableBtn: function (tdTableObj) {
        var $tdFlightEdit = tdTableObj.container.find('.edit-flight-btn');
        var $tdFlightRemove = tdTableObj.container.find('.remove-flight-btn');

        var $tdConfirmCtot = tdTableObj.container.find('.confirm-ctot-btn');
        var $tdCancelCtot = tdTableObj.container.find('.cancel-ctot-btn');
        var $tr = tdTableObj.container.find('tbody tr');


        //edit flight
        var $editFlightModal = $('#edit-flight-modal');
        var $ctotModal = $('#ctot-modal');
        var $tdExcludeCtot = $ctotModal.find('.exclude-flight-btn');
        var gdpid = tdTableObj.tdChart.gdp.id;

        $tr.on('click', function () {
            var fid = $(this).attr('fid');
            $(this).addClass('tr-activate').siblings().removeClass('tr-activate');

            $.each(tdTableObj.flightObj, function (i, item) {
                item.isActive = false;
            });
            tdTableObj.flightObj[fid].isActive = true;
        });

        $tdFlightEdit.on('click', function () {
            $editFlightModal.find('.comment').removeClass('comment-error-text');
            var fid = $(this).attr('fid');
            var $thistr = $(this).parent().parent();
            $editFlightModal.find('.comment').val('');
            $editFlightModal.find('.comment-error').html('');
            $editFlightModal.find('.eobt-error').html('');
            $editFlightModal.modal({
                show: true,
                backdrop: 'static'
            });
            $editFlightModal.find('.confirm-edit-flight-btn').text('Change');

            /*  $thistr.addClass('tr-activate')
                  .siblings().removeClass('tr-activate');
  
              tdTableObj.flightObj[fid].isActive = true;*/

            //var flights = tdTableObj.flights;
            for (var i = 0; i < tdTableObj.flights.length; i++) {
                if (fid == tdTableObj.flights[i].flight.id) {
                    var newEobt = (tdTableObj.flights[i].newEobt != null) ? tdTableObj.flights[i].newEobt : tdTableObj.flights[i].flight.eobt;
                    $editFlightModal.find('.modal-title').text('Edit Flight ' + tdTableObj.flights[i].flight.callsign);
                    $editFlightModal.find('#EobtFlightEdt').val(GDPManage.formatDateTime(newEobt));
                    // $editFlightModal.find('.callsign-info').html(tdTableObj.flights[i].flight.callsign);
                    $editFlightModal.find('.dep-info').html(tdTableObj.flights[i].flight.airportDeparture);
                    $editFlightModal.find('.arr-info').html(tdTableObj.flights[i].flight.airportArrival);
                    $editFlightModal.find('.eobt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.eobt));
                    $editFlightModal.find('.etot-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.etot));
                    $editFlightModal.find('.eldt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.eldt));
                    $editFlightModal.find('.eibt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.eibt));

                    //$editFlightModal.find('.cobt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.cobt));
                    var ctot = (tdTableObj.flights[i].flight.ctot != "" && tdTableObj.flights[i].flight.ctot != null) ? GDPManage.formatDateTime(tdTableObj.flights[i].flight.ctot) : '-'
                    var cldt = (tdTableObj.flights[i].flight.cldt != "" && tdTableObj.flights[i].flight.cldt != null) ? GDPManage.formatDateTime(tdTableObj.flights[i].flight.cldt) : '-'
                    $editFlightModal.find('.ctot-info').html(ctot);
                    $editFlightModal.find('.cldt-info').html(cldt);
                    //$editFlightModal.find('.cibt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.cibt));
                    var modalObj = {
                        container: $editFlightModal.find('.edit-flight-body'),
                        flight: tdTableObj.flights[i].flight,
                        flightInfo: $editFlightModal.find('.flight-info'),
                        //newEobt: $('#EobtFlightEdt').val(),
                        gdp: tdTableObj.tdChart.gdp,
                        flights: tdTableObj
                        //comment: $editFlightModal.find('.comment')
                    }
                    GDPManage.eventEditFlightModal(modalObj);

                    return;
                }
            }
        });
        //remove Flight (Exempt/Exclude)
        $tdFlightRemove.on('click', function () {
            $editFlightModal.find('.comment').removeClass('comment-error-text');
            var fid = $(this).attr('fid');
            var removeStr = $(this).attr('removestr');
            $editFlightModal.find('.form-group-new-eobt').hide();
            $editFlightModal.modal({
                show: true,
                backdrop: 'static'
            });
            $editFlightModal.find('.confirm-edit-flight-btn').text(removeStr.charAt(0).toUpperCase() + removeStr.slice(1));

            for (var i = 0; i < tdTableObj.flights.length; i++) {
                if (fid == tdTableObj.flights[i].flight.id) {
                    var newEobt = (tdTableObj.flights[i].newEobt != null) ? tdTableObj.flights[i].newEobt : tdTableObj.flights[i].flight.eobt;
                    $editFlightModal.find('.modal-title').text(removeStr.charAt(0).toUpperCase() + removeStr.slice(1) + ' Flight ' + tdTableObj.flights[i].flight.callsign);
                    $editFlightModal.find('#EobtFlightEdt').val(GDPManage.formatDateTime(newEobt));
                    // $editFlightModal.find('.callsign-info').html(tdTableObj.flights[i].flight.callsign);
                    $editFlightModal.find('.dep-info').html(tdTableObj.flights[i].flight.airportDeparture);
                    $editFlightModal.find('.arr-info').html(tdTableObj.flights[i].flight.airportArrival);
                    $editFlightModal.find('.eobt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.eobt));
                    $editFlightModal.find('.etot-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.etot));
                    $editFlightModal.find('.eldt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.eldt));
                    $editFlightModal.find('.eibt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.eibt));

                    //$editFlightModal.find('.cobt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.cobt));
                    var ctot = (tdTableObj.flights[i].flight.ctot != "" && tdTableObj.flights[i].flight.ctot != null) ? GDPManage.formatDateTime(tdTableObj.flights[i].flight.ctot) : '-'
                    var cldt = (tdTableObj.flights[i].flight.cldt != "" && tdTableObj.flights[i].flight.cldt != null) ? GDPManage.formatDateTime(tdTableObj.flights[i].flight.cldt) : '-'
                    $editFlightModal.find('.ctot-info').html(ctot);
                    $editFlightModal.find('.cldt-info').html(cldt);
                    //$editFlightModal.find('.cibt-info').html(GDPManage.formatDateTime(tdTableObj.flights[i].flight.cibt));
                    var modalObj = {
                        container: $editFlightModal.find('.edit-flight-body'),
                        flight: tdTableObj.flights[i].flight,
                        flightInfo: $editFlightModal.find('.flight-info'),
                        //newEobt: $('#EobtFlightEdt').val(),
                        gdp: tdTableObj.tdChart.gdp,
                        flights: tdTableObj,
                        removeStr: removeStr,
                        //comment: $editFlightModal.find('.comment')
                    }
                    GDPManage.eventRemoveFlightModal(modalObj); //celegana add remove btn
                    return;
                }
            }
        });
        //cancel trial ctot
        $tdCancelCtot.on('click', function () {
            var $thistr = $(this).parent().parent();
            var fid = $(this).attr('fid');
            var data = { fid: fid };

            /*  $thistr.addClass('tr-activate')
                  .siblings().removeClass('tr-activate');
  
              tdTableObj.flightObj[fid].isActive = true;*/

            $.ajax({
                type: 'POST',
                url: '/GDPManage/CancelCtot',
                dataType: 'json',
                data: data,
                success: function (result) {
                    if (result) {
                        var i = GDPManage.ctotResult.ctotFlights.length - 1;
                        for (i; i >= 0; i--) {
                            if (GDPManage.ctotResult.ctotFlights[i].flightId == fid)
                                GDPManage.ctotResult.ctotFlights.splice(i, 1);
                        }
                        GDPManage.updateTable(tdTableObj, null);
                        /*  $.each(GDPManage.tdCharts, function (i, item) {
                              if (item.tdChart.gdp.id == gdpid) {
                                  item.tdChart = gdpChart;
                                  GDPManage.updateChart(item);
                              }
                          }); cancel trial not effect chart */
                    }

                },
                error: function (req, status, error) {
                    console.log(error);
                    alert('error return save ctot');
                }

            });
        });

        // Confirm Edit Ctot
        $tdConfirmCtot.on('click', function () {
            $ctotModal.find('.ctot-confirm-form').show();
            var fid = $(this).attr('fid');
            var $thistr = $(this).parent().parent();
            var $comment = $ctotModal.find('.comment');
            var cflight;
            var requestedOBT;
            var currentCTOT;
            var $editFlightBody = $('.edit-flight-body');
            var $flightInfo = $editFlightBody.find('.flight-info').clone();
            $(".ctot-check").remove();
            var nowUtcString = moment.utc().toISOString().replace('Z', '');

            var eventTime = moment(nowUtcString);///moment(ctots[0].timeSaved);
            var currentTime = moment(nowUtcString);
            var expired = GDPManage.ctotResult.ctotTimeout;
            var interval = 1000;

            var $ctotConfirmBtn = $ctotModal.find('.confirm-modal-btn');
            var $getMannualCtotBtn = $ctotModal.find('.get-mannual-ctot');

            /*  $thistr.addClass('tr-activate')
                  .siblings().removeClass('tr-activate');
  
              tdTableObj.flightObj[fid].isActive = true;*/
            $ctotConfirmBtn.show();

            //$('input[name="ctotradio"]').attr('checked', false);
            $('input[name="ctotradio"]').each(function () { $(this).prop('checked', false); });
            $ctotModal.find('.comment-error').html('');
            $ctotModal.find('.mannual-ctot-result').html('');

            for (var i = 0; i < tdTableObj.flights.length; i++) {
                if (fid == tdTableObj.flights[i].flight.id) {
                    $ctotModal.find('.modal-title').text('CTOT Confirmation ' + tdTableObj.flights[i].flight.callsign);
                    cflight = tdTableObj.flights[i];
                    //return;
                }
            }

            // get requestedEOBT from CTOTFlight Output by fid
            for (var i = 0; i < GDPManage.ctotResult.ctotFlights.length; i++) {
                if (fid == GDPManage.ctotResult.ctotFlights[i].flightId) {
                    requestedOBT = GDPManage.ctotResult.ctotFlights[i].newEOBT;
                }
            }

            var ctotAlert = '<div class="row"><div class="col-md-6 col-sm-6"><p class="req-info-title title-obt">Requested OBT</p><p class="req-info-time info-obt">' + GDPManage.formatTime(cflight.flight.ctot) + '</p></div>';//'<div >Current CTOT : <span style:"font-weight: 400">' + + '</span></div>';
            ctotAlert += '<div class="col-md-6 col-sm-6"><p class="req-info-title ">Current CTOT</p><p class="req-info-time info-ctot">' + GDPManage.formatTime(requestedOBT) + '</p><div></div>';
            $('.ctot-alert').html(ctotAlert);

            ctotCheck = '<h6 class="ctot-check"><u>Please Confirm New CTOT Below :</u></h6>';
            var ctotArray = [];
            var ctotComment = "";
            var count = 0;
            for (var i = 0; i < GDPManage.ctotResult.ctotFlights.length; i++) {
                var ctotFlight = GDPManage.ctotResult.ctotFlights[i];
                if (fid == ctotFlight.flightId && count <= GDPManage.ctotResult.ctotOptions) {
                    eventTime = moment(ctotFlight.timeSaved);
                    var diff = currentTime.diff(eventTime);
                    if (diff < expired) {
                        count = count + 1;
                        var input = '<div class="form-check ctot-check"><input class="form-check-input" type="radio" name="ctotradio" id="ctotRadio' + count + '" value="' + ctotFlight.id + '">';
                        input += ' <label class="form-check-label" for="ctotRadio' + count + '">' + moment(ctotFlight.ctot).format("HH:mm") + ' (C' + count + ')' + '</label></div>';
                        ctotCheck += input;
                        ctotComment = ctotFlight.comment;
                        ctotArray.push(GDPManage.ctotResult.ctotFlights[i]);
                    }
                }
            }
            $('.ctot-confirm-form').prepend(ctotCheck);

            $comment.val(ctotComment);

            var countdown = setInterval(function () {
                var nowUtcString = moment.utc().toISOString().replace('Z', '');
                var currentTime = moment(nowUtcString);
                var diff = currentTime.diff(eventTime);
                var remain = moment.duration(expired - diff);
                //console.log('remain =' + remain + 'diff = ' + diff + "expired =" + expired);
                if (remain <= 0) {
                    $('.expire-alert').html('<h6><i class="fa fa-exclamation-triangle" aria-hidden="true"></i> CTOT Expired , Please try again</h6>');
                    $ctotModal.find('.ctot-confirm-form').hide();
                    $ctotConfirmBtn.hide();
                }
                else
                    $('.expire-alert').html('CTOT Expires In ' + moment(remain.as('milliseconds')).format('mm:ss') + ' Minutes');


            }, interval);

            $flightInfo.find('.dep-info').html(cflight.flight.airportDeparture);
            $flightInfo.find('.arr-info').html(cflight.flight.airportArrival);
            $flightInfo.find('.eobt-info').html(GDPManage.formatDateTime(cflight.flight.eobt));
            $flightInfo.find('.etot-info').html(GDPManage.formatDateTime(cflight.flight.etot));
            $flightInfo.find('.eldt-info').html(GDPManage.formatDateTime(cflight.flight.eldt));
            $flightInfo.find('.eibt-info').html(GDPManage.formatDateTime(cflight.flight.eibt));
            var ctot = (cflight.flight.ctot != "" && cflight.flight.ctot != null) ? GDPManage.formatDateTime(cflight.flight.ctot) : '-'
            var cldt = (cflight.flight.cldt != "" && cflight.flight.cldt != null) ? GDPManage.formatDateTime(cflight.flight.cldt) : '-'
            $flightInfo.find('.ctot-info').html(ctot);
            $flightInfo.find('.cldt-info').html(cldt);

            //var $getMannualCtotBtn = $ctotModal.find('.get-mannual-ctot');
            //get mauual ctot
            $getMannualCtotBtn.off('click').on('click', function () {
                $('.mannual-ctot-result').html('CTOT 10:00 (Sample)');
            });

            //var $ctotConfirmBtn = $ctotModal.find('.confirm-modal-btn');
            $ctotModal.modal({
                show: true,
                backdrop: 'static'
            });
            GDPManage.eventCtotModalClose(countdown);

            //exclude button
            var $confirmModal = $('#confirm-modal');
            $tdExcludeCtot.on('click', function () {
                if ($comment.val() != "" && $comment.val() != null) {
                    $ctotModal.modal('hide');
                    $confirmModal.find('.modal-title').text('Confirm Exclude Flight ' + cflight.flight.callsign);
                    var commentInfo = '<hr/><p><b>Comment : </b>' + $comment.val() + '</p>';
                    $confirmModal.find('.modal-body p').html('Do you want to Exclude this following flight?<br><br>').append($flightInfo).append(commentInfo);

                    $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                        var data = {
                            gdpid: gdpid,
                            fid: fid,
                            removeStr: "exclude",
                            comment: $comment.val()
                        };
                        GDPManage.removeCtotFlight(data, tdTableObj, $confirmModal);
                    });
                    $confirmModal.modal({
                        show: true,
                        backdrop: 'static'
                    });
                }
                else
                    $ctotModal.find('.comment-error').html('This field must not be blank');

            });
            //confirm button
            $ctotConfirmBtn.off('click').on('click', function () {

                if (GDPManage.isCtotRadioValid()) {
                    var select = $('input[name="ctotradio"]:checked').val();
                    //var comment = $('.comment').val();
                    if (select == "mannual") {

                    }
                    if (select == "exempt") {
                        clearInterval(countdown);
                        if ($ctotModal.find('.comment').val() != "" && $ctotModal.find('.comment').val() != null) {
                            /* $ctotModal.modal('hide');
                             $confirmModal.find('.modal-title').text('Confirm Exempt Flight' + cflight.flight.callsign);
                             var commentString = ($comment.val() != null && $comment.val() != "") ? $comment.val() : "-";
                             var commentInfo = '<hr/><p><b>Comment : </b>' + commentString + '</p>';
                             $confirmModal.find('.modal-body p').html('Do you want to Exclude this following flight?<br><br>').append($flightInfo).append(commentInfo);
 
                             $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {*/
                            var data = {
                                gdpid: gdpid,
                                fid: fid,
                                removeStr: select,
                                comment: $comment.val() // add later
                            };
                            GDPManage.removeCtotFlight(data, tdTableObj, $ctotModal);
                            // });
                            //  $confirmModal.modal('show');
                        }
                        else
                            $ctotModal.find('.comment-error').html('This field must not be blank');

                    }
                    else {
                        clearInterval(countdown);
                        var preview = '<i class="fa fa-spinner fa-spin"></i>';
                        $ctotConfirmBtn.prepend(preview);

                        var data = {
                            gid: gdpid,
                            cid: select,
                            comment: $comment.val(),
                            source: 'gdpManage'
                        };

                        $.ajax({
                            type: 'POST',
                            url: '/GDPManage/ConfirmCtot',
                            dataType: 'json',
                            data: data,
                            success: function (res) {
                                if (res.success)
                                    GDPManage.updateChartbyGid(gdpid, tdTableObj, fid);
                                else
                                    alert(res.error);

                            },
                            error: function (req, status, error) {
                                console.log(error);
                                alert('error return save ctot');
                            },
                            complete: function () {
                                $ctotModal.modal('hide');
                                $ctotConfirmBtn.find('.fa-spin').remove();
                            }

                        });
                    }
                }


            });
        });
    },
    updateChartbyGid: function (gid, tdTableObj, fid) {
        $.ajax({
            type: 'GET',
            url: '/GDPManage/UpdateChartbyGid/' + gid,
            dataType: 'json',
            success: function (gdpChart) {
                if (gdpChart.gdp) {
                    tdTableObj.tdChart = gdpChart;
                    var i = GDPManage.ctotResult.ctotFlights.length - 1;
                    for (i; i >= 0; i--) {
                        if (GDPManage.ctotResult.ctotFlights[i].flightId == fid) {
                            GDPManage.ctotResult.ctotFlights.splice(i, 1);
                        }
                    }
                    GDPManage.updateTable(tdTableObj, null);
                    $.each(GDPManage.tdCharts, function (i, item) {
                        if (item.tdChart.gdp.id == gdpChart.gdp.id) {
                            item.tdChart = gdpChart;
                            GDPManage.updateChart(item);
                        }
                    });
                    console.log('save ctot successfull');
                }
                $('#ctot-modal').modal('hide');
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable toUpdate GDP Chart');
            }
        });
    },
    removeCtotFlight: function (data, tdTableObj, $modal) {
        var $confirmRemoveFlightBtn = $('.confirm-modal-btn');
        var preview = '<i class="fa fa-spinner fa-spin"></i>';
        $confirmRemoveFlightBtn.prepend(preview);

        $.ajax({
            type: 'POST',
            url: '/GDPManage/RequestRemoveCtot',
            dataType: 'json',
            data: data,
            success: function (gdpChart) {
                $modal.modal('hide');

                if (!gdpChart.gdp) {
                    var $toast = $('#error-toast');
                    $toast.find('.toast-body').text(gdpChart.title);
                    $toast.toast('show');
                    return;
                }

                var $toast = $('#success-toast');
                $toast.find('.toast-body').text('Flight ' + data.removeStr + ' Successfully');
                $toast.find('.success-toast-time').text(gdpChart.time);
                $toast.toast('show');

                // tdTableObj.tdChart = gdpChart;  
                var i = GDPManage.ctotResult.ctotFlights.length - 1;
                for (i; i >= 0; i--) {
                    if (GDPManage.ctotResult.ctotFlights[i].flightId == data.fid) {
                        GDPManage.ctotResult.ctotFlights.splice(i, 1);
                    }
                }
                var flightArray = [];
                for (var i = 0; i < gdpChart.flightLists.length; i++) {
                    for (var j = 0; j < gdpChart.flightLists[i].length; j++) {
                        flightArray.push(gdpChart.flightLists[i][j]);
                    }
                }
                GDPManage.updateTable(tdTableObj, flightArray); /*change update after remove flight*/
                $.each(GDPManage.tdCharts, function (i, item) {
                    if (item.tdChart.gdp.id == gdpChart.gdp.id) {
                        item.tdChart = gdpChart;
                        GDPManage.updateChart(item);
                    }
                });
                console.log('remove ctot successfull');
            },
            error: function (req, status, error) {
                console.log(error);
                alert('error return remove ctot');
            },
            complete: function () {
                $confirmRemoveFlightBtn.find('i').remove();
            }
        });
    },

    eventTableBtn: function (tdTableObj) {
        var id = tdTableObj.tdChart.gdp.id;
        var $tdTableBtn = tdTableObj.container.siblings('.td-table-btn');
        var $tdTableRemove = $tdTableBtn.find('.td-table-remove');
        var $tdTableHide = $tdTableBtn.find('.td-table-hide');
        var $tdTableedit = $tdTableBtn.find('.td-table-edit');
        var $tdTableFilter = tdTableObj.container.siblings('.td-table-filter');
        var $tdTableTr = tdTableObj.container.find('tbody tr');
        var $tdTableTitle = tdTableObj.container.siblings('.td-table-header').find('.td-table-subtitle').text();
        var $tdFlightEdit = tdTableObj.container.find('.edit-flight-btn');
        var $tdAck = tdTableObj.container.siblings('.td-acknowledge');

        //ack btn
        $tdAck.off('click').on('click', function () {
            var flightArray = tdTableObj.flights;
            var i = flightArray.length - 1;
            for (i; i >= 0; i--) {
                if (flightArray[i].isUpdate)
                    flightArray[i].isUpdate = false;
                if (flightArray[i].isActive)
                    flightArray[i].isActive = false;
                if (flightArray[i].isRemove) {
                    flightArray.splice(i, 1);
                }
            }
            tdTableObj.flightObj = {};
            for (var i = 0; i < flightArray.length; i++) {
                tdTableObj.flightObj[flightArray[i].flight.id] = flightArray[i];
            }
            GDPManage.updateTable(tdTableObj, flightArray); //update from flightArray instead of gdpCartResult
        });

        if (tdTableObj.tdChart.trafficDemand.id < 0) {
            $tdTableedit.show();
            $tdTableedit.off('click').on('click', function () {
                // console.log('table 1');
                var $editModal = $('#edit-modal');
                var $editModalForm = $editModal.find('form');
                var $editForm = $editModal.find('#EditGdpForm');
                var $previewContainer = $('.preview-container');
                $editModalForm.trigger('reset');
                $editModal.find('.td-airport, .td-waypoint, .td-sector, .td-arr, .td-current, .td-specify').show();
                $editModal.find('.edit-modal-area').text(tdTableObj.tdChart.trafficDemand.trafficArea.type);
                $editModal.find('.edit-modal-point').text(tdTableObj.tdChart.trafficDemand.point);
                var trafficAreaId = tdTableObj.tdChart.trafficDemand.trafficAreaId;
                if (trafficAreaId == 1) {
                    $editModal.find('.td-waypoint, .td-sector').hide();
                    if (tdTableObj.tdChart.trafficDemand.isDep) $editModal.find('.edit-modal-dep').prop("checked", true);
                    if (tdTableObj.tdChart.trafficDemand.isArr) {
                        $editModal.find('.td-arr').show();
                        $editModal.find('.edit-modal-arr').prop("checked", true);
                    }
                    if (tdTableObj.tdChart.trafficDemand.isCombined) $editModal.find('.edit-modal-combined').prop("checked", true);
                } else {
                    if (trafficAreaId == 2) {
                        $editModal.find('.td-airport, .td-sector').hide();
                        $editModal.find('.td-waypoint').show();
                        if (tdTableObj.tdChart.trafficDemand.radiusNm) $editModal.find('.edit-modal-radius').text(tdTableObj.tdChart.trafficDemand.radiusNm);
                        else $editModal.find('.td-radius').hide();
                    } else {
                        $editModal.find('.td-airport, .td-waypoint').hide();
                        $editModal.find('.td-sector').show();
                    }
                    if (tdTableObj.tdChart.trafficDemand.lowerFlightLevel != null) $editModal.find('.edit-modal-lower').text(tdTableObj.tdChart.trafficDemand.lowerFlightLevel);
                    else $editModal.find('.td-lower').hide();
                    if (tdTableObj.tdChart.trafficDemand.upperFlightLevel != null) $editModal.find('.edit-modal-upper').text(tdTableObj.tdChart.trafficDemand.upperFlightLevel);
                    else $editModal.find('.td-upper').hide();
                }
                if (tdTableObj.tdChart.trafficDemand.startTime) {
                    $editModal.find('.edit-modal-specify').prop('checked', true);
                    $editModal.find('.td-current').hide();
                    var startArray = tdTableObj.tdChart.trafficDemand.startTime.split('T');
                    var endArray = tdTableObj.tdChart.trafficDemand.endTime.split('T');
                    $editModal.find('.edit-modal-start').text(startArray[0] + ' ' + startArray[1].substr(0, 5));
                    $editModal.find('.edit-modal-end').text(endArray[0] + ' ' + endArray[1].substr(0, 5));
                }
                else {
                    $editModal.find('.edit-modal-current').prop('checked', true);
                    $editModal.find('.td-specify').hide();
                    $editModal.find('.edit-modal-ahead').text(tdTableObj.tdChart.trafficDemand.aheadHour);
                }
                $editModal.find('.edit-modal-interval').text(tdTableObj.tdChart.trafficDemand.intervalMin);
                $editModal.find('.edit-modal-sch').prop('checked', tdTableObj.tdChart.trafficDemand.isSCH);
                $editModal.find('.edit-modal-fpl').prop('checked', tdTableObj.tdChart.trafficDemand.isFPL);
                $editModal.find('.edit-modal-atfm').prop('checked', tdTableObj.tdChart.trafficDemand.isATFM);
                $editModal.find('.edit-modal-atsmsg').prop('checked', tdTableObj.tdChart.trafficDemand.isATSMSG);
                $editModal.find('.edit-modal-sur').prop('checked', tdTableObj.tdChart.trafficDemand.isSUR);
                $editModal.find('.edit-modal-passed').prop('checked', tdTableObj.tdChart.trafficDemand.isPassed);
                //$editModalForm.data('tdTableObj', tdTableObj);

                /* clear modal preview*/
                console.log('edit table');
                $('.gdp-edit-btn-group').show();
                $('.gdp-chart-btn-group').hide();

                $previewContainer.find('.td-chart-preview').remove();
                $previewContainer.prepend('<canvas class="td-chart-preview"></canvas>');
                $editModal.find('.modal-dialog').removeClass('modal-xl');
                $previewContainer.hide();

                $editForm.find('.form-group').find('input').attr("disabled", false);
                $editForm.find('.form-group').find('select').attr("disabled", false);

                $editModal.modal({
                    show: true,
                    backdrop: 'static'
                });
            });
        }
        // add change state button
        var $state = $('<h4 style="top:0;"></h4>').addClass('position-absolute mt-1 ml-3 state');
        if (!tdTableObj.tdChart.gdp.isExecuted) {
            tdTableObj.container.parent().addClass('border border-info rounded-lg');
            $state.text('S').addClass('text-info');
        } else {
            tdTableObj.container.parent().addClass('border border-danger rounded-lg');
            $state.text('EXE').addClass('text-danger');
        }
        tdTableObj.container.parent().find('h4').remove();
        tdTableObj.container.siblings('.td-table-btn').addClass('mt-1 mr-2');
        tdTableObj.container.after($state);
        var $changeBtn = tdTableObj.container.siblings('.td-table-btn').find('.td-table-change');
        $changeBtn.show().on('click', function () {
            var $changeModal = $('#change-state-modal');
            $changeModal.find('.designator').text(tdTableObj.tdChart.gdp.designator);
            if (!tdTableObj.tdChart.gdp.isExecuted) {
                $changeModal.find('.execute-btn').show();
                $changeModal.find('.undo-btn').text('Unsave');
            } else {
                $changeModal.find('.execute-btn').hide();
                $changeModal.find('.undo-btn').text('Unexecute');
            }
            $changeModal.data('tdTableObj', tdTableObj);
            GDPManage.updateModalInfo($changeModal, tdTableObj);
        });

        var $tdTableExport = $tdTableBtn.find('.td-table-export');
        var $tdTableChange = $tdTableBtn.find('.td-table-change');
        $tdTableRemove.add($tdTableedit).add($tdTableExport).add($tdTableChange).tooltip();

        //filter
        if ($tdTableFilter.val() !== null) {
            var value = $tdTableFilter.val().toLowerCase();
            // tdTableObj.filterCallsign = value;
            $tdTableTr.filter(function () {
                //$(this).toggle($(this).find('.td-callsign').text().toLowerCase().indexOf(value) > -1)
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });
        }
        $tdTableFilter.on("keyup", function () {
            var value = $(this).val().toLowerCase();
            $tdTableTr.filter(function () {
                //$(this).toggle($(this).find('.td-callsign').text().toLowerCase().indexOf(value) > -1)
                $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1)
            });

        });
        //remove table
        var $confirmModal = $('#confirm-modal');
        $tdTableRemove.off('click').on('click', function () {
            $confirmModal.find('.modal-title').text('Remove Table');
            /** hidden table instead 
            for (var i = 0; i < GDPManage.tdCharts.length; i++) {
                if (GDPManage.tdCharts[i].tdChart.gdp.id == id)
                    GDPManage.tdCharts[i].container.siblings('.td-chart-btn').find('.td-chart-table').show();
            }*/

            var $TableTitle = $('<i></i>').append($tdTableTitle);

            $confirmModal.find('.modal-body p').html('Do you want to remove the following Table?<br><br>').append($TableTitle);
            $confirmModal.modal({
                show: true,
                backdrop: 'static'
            });
            $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                $confirmModal.modal('hide');
                tdTableObj.container.parent().remove();
                if ($("#td-table-container").children().length < 2)
                    $('#HideTimelineBtn').hide();
                GDPManage.gdpObj[id].isDisplayTable = false;
                for (var i = 0; i < GDPManage.tdTables.length; i++) {
                    if (GDPManage.tdTables[i].container == tdTableObj.container) GDPManage.tdTables.splice(i, 1);
                }
            });
            /** hidden table instead 
             * tdTableObj.container.parent().hide();
             * */

        });

        //edit
        if (id > 0) {
            var $gdpTableEdit = tdTableObj.container.siblings('.td-table-btn').find('.td-table-edit');
            $gdpTableEdit.off('click').on('click', function () {
                var $editModal = $('#edit-gdp-modal');
                var $editModalForm = $editModal.find('form');
                var $editForm = $editModal.find('#EditGdpForm');
                var $previewContainer = $('.preview-container');

                GDPManage.getGdpbyId($editModal, tdTableObj.tdChart);
                $editModalForm.data('tdTableObj', tdTableObj);
                /* clear modal preview*/
                $('.gdp-edit-btn-group').show();
                $('.gdp-chart-btn-group').hide();

                $previewContainer.find('.td-chart-preview').remove();
                $previewContainer.prepend('<canvas class="td-chart-preview"></canvas>');
                $editModal.find('.modal-dialog').removeClass('modal-xl');
                $previewContainer.hide();

                $editForm.find('.form-group').find('input').attr("disabled", false);
                $editForm.find('.form-group').find('select').attr("disabled", false);

                $editModal.modal({
                    show: true,
                    backdrop: 'static'
                });
            });
        }
        //export
        $tdTableExport.off('click').on('click', function () {
            $confirmModal.find('.modal-title').text('Export Regulated Demand Data');
            var $TableTitle = $('<i></i>').append($tdTableTitle);

            $confirmModal.find('.modal-body p').html('Do you want to export regulated demand data of the following Table?<br><br>').append($TableTitle);
            $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                $confirmModal.modal('hide');
                var filename = $TableTitle.text() + '.csv';
                var data = [];
                if (tdTableObj.tdChart.trafficDemand.isDep) {
                    for (var i = 0; i < tdTableObj.tdChart.flightDepLists.length; i++) {
                        for (var j = 0; j < tdTableObj.tdChart.flightDepLists[i].length; j++) {
                            data.push(tdTableObj.tdChart.flightDepLists[i][j].flight);
                        }
                    }
                }
                if (tdTableObj.tdChart.trafficDemand.isArr || tdTableObj.tdChart.trafficDemand.trafficAreaId != 1) {
                    for (var i = 0; i < tdTableObj.tdChart.flightLists.length; i++) {
                        for (var j = 0; j < tdTableObj.tdChart.flightLists[i].length; j++) {
                            data.push(tdTableObj.tdChart.flightLists[i][j].flight);
                        }
                    }
                }
                TrafficDemand.downloadCSV({
                    filename: filename,
                    data: data
                });
            });
            $confirmModal.modal({
                show: true,
                backdrop: 'static'
            });
        });
        //toggle comment
        tdTableObj.container.find('td.td-comment > span').off('click').on('click', function () {
            $(this).toggleClass("show");
        });
    },
    eventChartBtn: function (gdpChartObj) {
        var id = gdpChartObj.tdChart.gdp.id;

        var $tdChartBtn = gdpChartObj.container.siblings('.td-chart-btn');
        var $tdChartRemove = $tdChartBtn.find('.td-chart-remove');
        var $tdChartEdit = $tdChartBtn.find('.td-chart-edit');
        var $tdChartTable = $tdChartBtn.find('.td-chart-table');
        var $tdChartExport = $tdChartBtn.find('.td-chart-export');
        var $confirmModal = $('#confirm-modal');
        $tdChartEdit.tooltip();
        $tdChartTable.on('click', function () {
            for (var i = 0; i < GDPManage.tdTables.length; i++) {
                if (GDPManage.tdTables[i].tdChart.gdp.id == id)
                    GDPManage.tdTables[i].container.parent().show();
            }
            $tdChartTable.hide();
        });
        //Chart Export 
        $tdChartExport.on('click', function () {
            $confirmModal.find('.modal-title').text('Export Regulated Demand Data');
            var $modalText = $confirmModal.find('.modal-body p');
            $modalText.html('Do you want to export regulated demand data of the following chart?<br><br>');
        });
        $tdChartRemove.on('click', function () {
            $confirmModal.find('.confirm-modal-btn').on('click', function () {
                if ($("#td-chart-container").children().length < 2)
                    $('#HideChartBtn').hide();
                GDPManage.gdpObj[id].isDisplayChart = false;
                for (var i = 0; i < GDPManage.tdCharts.length; i++) {
                    if (GDPManage.tdCharts[i].container == gdpChartObj.container) GDPManage.tdCharts.splice(i, 1);
                }
            });

            /* remove table with same gdpid
            for (var i = 0; i < GDPManage.tdTables.length; i++) {
                if (GDPManage.tdTables[i].tdChart.gdp.id == id) {
                    GDPManage.tdTables[i].container.parent().remove();
                    GDPManage.tdTables.splice(i, 1);
                }
            }*/
        });
        if (id > 0) {
            $tdChartEdit.off('click').on('click', function () {
                var $editModal = $('#edit-gdp-modal');
                var $editModalForm = $editModal.find('form');
                var $editForm = $editModal.find('#EditGdpForm');
                var $previewContainer = $('.preview-container');
                GDPManage.getGdpbyId($editModal, gdpChartObj.tdChart);
                $editModalForm.data('tdChartObj', gdpChartObj);
                /* clear modal preview*/

                $('.gdp-edit-btn-group').show();
                $('.gdp-chart-btn-group').hide();

                $previewContainer.find('.td-chart-preview').remove();
                $previewContainer.prepend('<canvas class="td-chart-preview"></canvas>');
                $editModal.find('.modal-dialog').removeClass('modal-xl');
                $previewContainer.hide();

                $editForm.find('.form-group').find('input').attr("disabled", false);
                $editForm.find('.form-group').find('select').attr("disabled", false);

                $editModal.modal({
                    show: true,
                    backdrop: 'static'
                });
            });

            var $state = $('<h4 style="top:0;"></h4>').addClass('position-absolute mt-1 ml-3 state');
            if (!gdpChartObj.tdChart.gdp.isExecuted) {
                gdpChartObj.container.parent().addClass('border border-info rounded-lg');
                $state.text('S').addClass('text-info');
            } else {
                gdpChartObj.container.parent().addClass('border border-danger rounded-lg');
                $state.text('EXE').addClass('text-danger');
            }
            gdpChartObj.container.parent().find('h4').remove();
            gdpChartObj.container.siblings('.td-chart-btn').addClass('mt-1 mr-2');
            gdpChartObj.container.after($state);
            var $changeBtn = gdpChartObj.container.siblings('.td-chart-btn').find('.td-chart-change');
            $changeBtn.show().on('click', function () {
                var $changeModal = $('#change-state-modal');
                $changeModal.find('.designator').text(gdpChartObj.tdChart.gdp.designator);
                if (!gdpChartObj.tdChart.gdp.isExecuted) {
                    $changeModal.find('.execute-btn').show();
                    $changeModal.find('.undo-btn').text('Unsave');
                } else {
                    $changeModal.find('.execute-btn').hide();
                    $changeModal.find('.undo-btn').text('Unexecute');
                }
                $changeModal.data('tdChartObj', gdpChartObj);
                GDPManage.updateModalInfo($changeModal, gdpChartObj);
            });
        }
    },

    updateModalInfo: function ($saveModal, tdChartObj) {
        var $saveModalForm = $saveModal.find('form');
        $saveModalForm.trigger('reset');
        $saveModal.find('.td-airport, .td-waypoint, .td-sector, .td-adep, .td-ades, .td-airlines').show();
        $saveModal.find('.area').text(tdChartObj.tdChart.gdp.trafficArea.type);
        $saveModal.find('.point').text(tdChartObj.tdChart.gdp.point);
        $saveModal.find('.regul').val(tdChartObj.tdChart.gdp.regul);
        $saveModal.find('.regcause').val(tdChartObj.tdChart.gdp.regcause);
        $saveModal.find('.comment').val(tdChartObj.tdChart.gdp.comment);
        var trafficAreaId = tdChartObj.tdChart.gdp.trafficAreaId;
        if (trafficAreaId == 1) $saveModal.find('.td-waypoint, .td-sector').hide();
        else {
            if (trafficAreaId == 2) {
                $saveModal.find('.td-airport, .td-sector').hide();
                $saveModal.find('.td-waypoint').show();
                if (tdChartObj.tdChart.gdp.radiusNm) $saveModal.find('.radius').text(tdChartObj.tdChart.gdp.radiusNm);
                else $saveModal.find('.td-radius').hide();
            } else {
                $saveModal.find('.td-airport, .td-waypoint').hide();
                $saveModal.find('.td-sector').show();
            }
            if (tdChartObj.tdChart.gdp.lowerFlightLevel != null) $saveModal.find('.lower').text(tdChartObj.tdChart.gdp.lowerFlightLevel);
            else $saveModal.find('.td-lower').hide();
            if (tdChartObj.tdChart.gdp.upperFlightLevel != null) $saveModal.find('.upper').text(tdChartObj.tdChart.gdp.upperFlightLevel);
            else $saveModal.find('.td-upper').hide();
        }
        var startArray = tdChartObj.tdChart.gdp.startTime.split('T');
        var endArray = tdChartObj.tdChart.gdp.endTime.split('T');
        var endRecoveryArray = tdChartObj.tdChart.gdp.endRecoveryTime.split('T');
        $saveModal.find('.start').text(startArray[0] + ' ' + startArray[1].substr(0, 5));
        $saveModal.find('.end').text(endArray[0] + ' ' + endArray[1].substr(0, 5));
        $saveModal.find('.end-recovery').text(endRecoveryArray[0] + ' ' + endRecoveryArray[1].substr(0, 5));
        $saveModal.find('.capacity').text(tdChartObj.tdChart.gdp.capacityPerHr);
        $saveModal.find('.capacity-recovery').text(tdChartObj.tdChart.gdp.capacityRecoveryPerHr);
        if (tdChartObj.tdChart.gdp.exemptADEP) $saveModal.find('.adep').text(tdChartObj.tdChart.gdp.exemptADEP);
        else $saveModal.find('.td-adep').hide();
        if (tdChartObj.tdChart.gdp.exemptADES) $saveModal.find('.ades').text(tdChartObj.tdChart.gdp.exemptADES);
        else $saveModal.find('.td-ades').hide();
        if (tdChartObj.tdChart.gdp.exemptAirlines) $saveModal.find('.airlines').text(tdChartObj.tdChart.gdp.exemptAirlines);
        else $saveModal.find('.td-airlines').hide();
        $saveModal.find('.interval').text(tdChartObj.tdChart.gdp.intervalMin);
        $saveModalForm.data('tdChartObj', tdChartObj);
        $saveModal.modal({
            show: true,
            backdrop: 'static'
        });
    },

    formatDateTime: function (datetime) {
        return datetime.substr(0, 10) + ' ' + datetime.substr(11, 5);
    },
    formatTime: function (datetime) {
        return datetime.substr(11, 5);
    }


};

$.datetimepicker.setDateFormatter({
    parseDate: function (date, format) {
        var d = moment(date, format);
        return d.isValid() ? d.toDate() : false;
    },

    formatDate: function (date, format) {
        return moment(date).format(format);
    }
});

