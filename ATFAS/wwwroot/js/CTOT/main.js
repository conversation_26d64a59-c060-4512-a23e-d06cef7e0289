﻿/* CTOT Airine Management*/
var ctotFlights = [];
var flightObj = {};
var ctotTrial = {};
var filterValue = {};
var isEdit = false;
var isAdmin = false;
//var currentUtc = moment();
$(document).ready(function () {
    initCtotTable();
    filterEvent();
    searchEvent();
    ackEvent();
    setInterval(updateCtotTable, 30000);

});
function initCtotTable() {
    $('#loading-overlay').show();

    $.ajax({
        type: 'GET',
        url: '/api/FrontEnd',
        dataType: 'json',

        success: function (res) {
            for (var i = 0; i < res.length; i++) {
                if (res[i] == "CtotRequest" || res[i] == "CtotChange" || res[i] == "AccessAll")
                    isEdit = true;
                if (res[i] == "CtotChange" || res[i] == "AccessAll")
                    isAdmin = true;
            }

            filterValue = {
                select: $('input[name="filterradio"]:checked').val(),
                input: $('#CtotPoint').val().toUpperCase(),
                isAdmin: isAdmin
            }

            //queryFlights();       
            $.ajax({
                url: '/CTOTDistributor/QueryCtotflights',
                type: 'POST',
                data: filterValue,
                success: function (res) {
                    flightObj = {};
                    ctotFlights = res.ctotFlights;
                    ctotTrial = res.ctotTrial;
                    //currentUtc = res.currentUtc.replace('Z', '');
                    $('#loading-overlay').hide();

                    if ((ctotFlights.length < 1 || ctotFlights == null) && $('input[name="filterradio"]:checked').val() == 'all') {
                        $('.message').text('There is no published CTOT at this moment.');
                        return;
                    }
                    for (var i = 0; i < ctotFlights.length; i++) {
                        flightObj[ctotFlights[i].flight.id] = ctotFlights[i];
                        flightObj[ctotFlights[i].flight.id].index = i;
                    }

                    $('.search-ctot').show();
                    $('#CtotTable').bootstrapTable({ data: ctotFlights });

                },
                error: function (req, status, error) {
                    $('#loading-overlay').hide();
                    console.log('init : ' + JSON.stringify(req));
                    console.log('init : ' + status);
                    console.log('init : ' + error);
                    alert('Unable to load CTOT');
                }
            });
        },
        error: function (req, status, error) {
            console.log(error);
        }
    });


}

function filterEvent() {
    $('input[name="filterradio"]').click(function () {
        $('#CtotPoint').val('');
        $('.ctot-point-error').text('');
        if ($('.input-radio').is(':checked'))
            $('.ctot-input').prop("disabled", false);
        else
            $('.ctot-input').prop("disabled", true);
    });
}
function isQueryInputValid() {
    $('.ctot-point-error').text('');

    if ($('.input-radio').is(':checked') && ($('#CtotPoint').val() == '' || $('#CtotPoint').val() == null)) {
        $('.ctot-point-error').text('input must not be blank');
        return false;
    }
    else
        return true;
}
function isCtotRadioValid() {
    $('.ctot-radio-error').text('');

    if ($('input[name = "ctotradio"]').is(':checked'))
        return true;
    else {
        $('.ctot-radio-error').text('Please Select CTOT');
        return false;
    }

}
function neweobtValid(newEobt, etot, eobt) {
    var nowUtcString = moment.utc().toISOString().replace('Z', '');
    var nowUtc = moment(nowUtcString);
    var freeze = ctotTrial.newEobtBlock; // 45 min
    var freezeUtc = nowUtc.add(freeze, 'minutes');

    // if (moment(newEobt) > moment(freezeUtc).subtract(moment(etot) - moment(eobt)))
    if (moment(newEobt) > nowUtc)
        return true;
    else
        return false;
}

function searchEvent() {
    $('#allRadio').on('click', function () {
        if (filterValue.select != 'all') {
            $('#CtotTable').bootstrapTable('destroy');
            initCtotTable();
        }
    });
    $('#SubmitSearch').on('click', function () {
        if (isQueryInputValid()) {
            $('#CtotTable').bootstrapTable('destroy');
            initCtotTable();
        }
    });
}
function ackEvent() {
    $('#SubmitAck').on('click', function () {
        var position = $('#CtotTable').bootstrapTable('getScrollPosition');
        var i = ctotFlights.length - 1;
        for (i; i >= 0; i--) {
            if (ctotFlights[i].isUpdate !== "undefined")
                ctotFlights[i].isUpdate = false;
            if (ctotFlights[i].isRemove)
                ctotFlights.splice(i, 1);
        }
        $('#CtotTable').bootstrapTable('load', ctotFlights);
        $('#CtotTable').bootstrapTable('scrollTo', position);

        flightObj = {};
        for (var i = 0; i < ctotFlights.length; i++) {
            flightObj[ctotFlights[i].flight.id] = ctotFlights[i];
        }
    });
}

function updateCtotTable() {
    var position = $('#CtotTable').bootstrapTable('getScrollPosition');
    //console.log('updated data = ' + JSON.stringify(filterValue));
    $.ajax({
        type: 'POST',
        url: '/CTOTDistributor/QueryCtotflights',
        data: filterValue,

        success: function (results) {
            ctotFlights = results.ctotFlights;
            ctotTrial = results.ctotTrial;
            currentUtc = results.currentUtc.replace('Z', '');
            $('.last-update').html('Last Updated : ' + moment.utc().format('HH:mm:ss') + ' UTC');

            if (ctotFlights.length < 1 && $('input[name="filterradio"]:checked').val() == 'all') {
                $('.message').text('There is no published CTOT at this moment.');
                $('.search-ctot').hide();
                return;
            }

            $('.message').text('');
            /* 'load' method */
            //console.log('JSON = ' + JSON.stringify(flightObj));
            for (var i = 0; i < ctotFlights.length; i++) {
                if (flightObj[ctotFlights[i].flight.id]) {

                    //เจอใน A และ B
                    if (flightObj[ctotFlights[i].flight.id].flight.ctot != ctotFlights[i].flight.ctot || flightObj[ctotFlights[i].flight.id].forwardStatus != ctotFlights[i].forwardStatus || flightObj[ctotFlights[i].flight.id].flight.atot != ctotFlights[i].flight.atot || flightObj[ctotFlights[i].flight.id].flight.aldt != ctotFlights[i].flight.aldt)
                        ctotFlights[i].isUpdate = true;
                    else
                        ctotFlights[i] = flightObj[ctotFlights[i].flight.id];
                }
                else {
                    ctotFlights[i].isUpdate = true;
                }
                delete flightObj[ctotFlights[i].flight.id];
            }
            $.each(flightObj, function (key, value) {
                value.isRemove = true;
                ctotFlights.push(value);
            });
            ctotFlights.sort(function (a, b) {
                return moment(a.flight.ctot) - moment(b.flight.ctot);
            });

            $('.search-ctot').show();
            console.log("ctotflights");



            $('#CtotTable').bootstrapTable('load', ctotFlights);
            $('#CtotTable').bootstrapTable('scrollTo', position);


            flightObj = {};
            for (var i = 0; i < ctotFlights.length; i++) {
                flightObj[ctotFlights[i].flight.id] = ctotFlights[i];
            }

            /* New Method 
                for (var i = 0; i < ctotFlights.length; i++) {
                    if (flightObj[ctotFlights[i].flight.id]) {
                        //เจอใน A และ B
                        if (flightObj[ctotFlights[i].flight.id].flight.ctot != ctotFlights[i].flight.ctot || flightObj[ctotFlights[i].flight.id].forwardStatus != ctotFlights[i].forwardStatus)
                        {
                            ctotFlights[i].isUpdate = true;
    
                        }
                        else
                            ctotFlights[i] = flightObj[ctotFlights[i].flight.id];
                    }
                    else
                        ctotFlights[i].isUpdate = true;
                    delete flightObj[ctotFlights[i].flight.id];
                }
                $.each(flightObj, function (key, value) {
                    value.isRemove = true;
                    ctotFlights.push(value);
                }); */

            $('#loading-overlay').hide();
        },
        error: function (req, status, error) {
            console.log('update : ' + JSON.stringify(req));
            console.log('update : ' + status);
            console.log('update : ' + error);
            alert('Unable to request GDP Chart');
        }
    });
}


function eventTableBtn() {
    var $tdTable = $('.td-table');
    var undoBtn = $tdTable.find('.undo-flight-btn');
    var $confirmModal = $('#confirm-modal');
    var $tdFlightEdit = $tdTable.find('.edit-flight-btn');

    var $tdConfirmCtot = $tdTable.find('.confirm-ctot-btn');
    var $tdCancelCtot = $tdTable.find('.cancel-ctot-btn');


    //edit flight
    var $editFlightModal = $('#edit-flight-modal');
    var $ctotModal = $('#ctot-modal');
    var $alertModal = $('#alert-modal');
    var gdpid = 0;

    $tdFlightEdit.on('click', function () {
        var fid = $(this).attr('fid');
        $editFlightModal.find('.comment').val('');
        $editFlightModal.find('.comment-error').html('');
        $editFlightModal.find('.eobt-error').html('');


        for (var i = 0; i < ctotFlights.length; i++) {
            if (fid == ctotFlights[i].flight.id) {
                //CTOT Idep
                if (ctotFlights[i].isIdep) {
                    $alertModal.modal('show');
                    $alertModal.find('.info-box-icon').html('<i class="fas fa-phone-square text-danger"></i>');
                    $alertModal.find('.info-box-content').html('<p>Your CTOT is generated by <u>' + ctotFlights[i].flight.originator + '</u></p><p>Please contact ATFMU at +6622878024 to request a new CTOT.</p>');
                }
                else {
                    $editFlightModal.modal('show');
                    var newEobt = (ctotFlights[i].flight.eobtAirline != null) ? ctotFlights[i].flight.eobtAirline : ctotFlights[i].flight.eobt;
                    $editFlightModal.find('.modal-title').text('Edit Flight ' + ctotFlights[i].flight.callsign);
                    $editFlightModal.find('#EobtFlightEdt').val(formatDateTime(newEobt));
                    $editFlightModal.find('.dep-info').html(ctotFlights[i].flight.airportDeparture);
                    $editFlightModal.find('.arr-info').html(ctotFlights[i].flight.airportArrival);
                    $editFlightModal.find('.eobt-info').html(formatDateTime(ctotFlights[i].flight.eobt));
                    $editFlightModal.find('.etot-info').html(formatDateTime(ctotFlights[i].flight.etot));
                    $editFlightModal.find('.eldt-info').html(formatDateTime(ctotFlights[i].flight.eldt));
                    $editFlightModal.find('.eibt-info').html(formatDateTime(ctotFlights[i].flight.eibt));

                    var ctot = (ctotFlights[i].flight.ctot != "" && ctotFlights[i].flight.ctot != null) ? formatDateTime(ctotFlights[i].flight.ctot) : '-'
                    var cldt = (ctotFlights[i].flight.cldt != "" && ctotFlights[i].flight.cldt != null) ? formatDateTime(ctotFlights[i].flight.cldt) : '-'
                    $editFlightModal.find('.ctot-info').html(ctot);
                    $editFlightModal.find('.cldt-info').html(cldt);
                    var modalObj = {
                        container: $editFlightModal.find('.edit-flight-body'),
                        flight: ctotFlights[i].flight,
                        flightInfo: $editFlightModal.find('.flight-info'),
                        gdpid: (ctotFlights[i].measureStringId != null) ? getGdpId(ctotFlights[i].measureStringId) : 0,
                        isForwarding: ctotFlights[i].isForwarding,
                        isIdep: ctotFlights[i].isIdep,
                    }
                    eventEditFlightModal(modalObj);
                }

                return;
            }
        }
    });

    // Confirm Edit Ctot
    $tdConfirmCtot.on('click', function () {
        $ctotModal.find('.ctot-confirm-form').show(); //show  ctot select modal when click link confirm ctot 
        var $editRow = $(this).parent().parent();
        var fid = $(this).attr('fid');
        var $comment = $ctotModal.find('.comment');
        var cflight;
        var requestedOBT;
        var $editFlightBody = $('.edit-flight-body');
        var $flightInfo = $editFlightBody.find('.flight-info').clone();
        $(".ctot-check").remove();
        var nowUtcString = moment.utc().toISOString().replace('Z', '');

        var eventTime = moment(nowUtcString);///moment(ctots[0].timeSaved);
        var currentTime = moment(nowUtcString);
        var expired = ctotTrial.ctotTimeout;
        var interval = 1000;

        var $ctotConfirmBtn = $ctotModal.find('.confirm-modal-btn');
        var $ctotBackBtn = $ctotModal.find('.back-modal-btn');
        var $getMannualCtotBtn = $ctotModal.find('.get-mannual-ctot');
        $ctotConfirmBtn.show();

        //$('input[name="ctotradio"]').attr('checked', false);
        $('input[name="ctotradio"]').each(function () { $(this).prop('checked', false); });
        $ctotModal.find('.comment-error').html('');
        $ctotModal.find('.mannual-ctot-result').html('');

        for (var i = 0; i < ctotFlights.length; i++) {
            if (fid == ctotFlights[i].flight.id) {
                gdpid = ctotFlights[i].measureStringId;
                $ctotModal.find('.modal-title').text('CTOT Confirmation ' + ctotFlights[i].flight.callsign);
                cflight = ctotFlights[i];
                //return;
            }
        }
        // get requestedEOBT from CTOTFlight Output by fid
        for (var i = 0; i < ctotTrial.ctotFlights.length; i++) {
            if (fid == ctotTrial.ctotFlights[i].flight.id) {
                requestedOBT = ctotTrial.ctotFlights[i].newEOBT;
            }
        }
        //console.log('requestedOBT' + requestedOBT);
        var ctotAlert = '<div class="row"><div class="col-md-6 col-sm-6"><p class="req-info-title title-obt">Requested OBT</p><p class="req-info-time info-obt">' + moment(requestedOBT).format('HH:mm') + '</p></div>';//'<div >Current CTOT : <span style:"font-weight: 400">' + + '</span></div>';
        ctotAlert += '<div class="col-md-6 col-sm-6"><p class="req-info-title ">Current CTOT</p><p class="req-info-time info-ctot">' + moment(cflight.flight.ctot).format('HH:mm') + '</p><div></div>';//'<div>Requested OBT : ' + moment(requestedOBT).format('HH:mm') + '</div>';
        $('.ctot-alert').html(ctotAlert);

        var ctotCheck = '<h6 class="ctot-check"><u>Please Confirm New CTOT Below :</u></h6>';
        var ctotArray = [];
        var ctotComment = "";
        var count = 0;
        for (var i = 0; i < ctotTrial.ctotFlights.length; i++) {
            var ctotFlight = ctotTrial.ctotFlights[i];
            if (fid == ctotFlight.flightId && count <= ctotTrial.ctotOptions) {
                eventTime = moment(ctotFlight.timeSaved);
                var diff = currentTime.diff(eventTime);
                if (diff < expired) {
                    count = count + 1;
                    var input = '<div class="form-check ctot-check"><input class="form-check-input" type="radio" name="ctotradio" id="ctotRadio' + count + '" value="' + ctotFlight.id + '">';
                    input += ' <label class="form-check-label" for="ctotRadio' + count + '">' + moment(ctotFlight.ctot).format("HH:mm") + ' (C' + count + ')' + '</label></div>';
                    ctotCheck += input;
                    ctotComment = ctotFlight.comment;
                    ctotArray.push(ctotTrial.ctotFlights[i]);
                }
            }
        }
        $('.ctot-confirm-form').prepend(ctotCheck);

        $comment.val(ctotComment);

        var countdown = setInterval(function () {
            var nowUtcString = moment.utc().toISOString().replace('Z', '');
            var currentTime = moment(nowUtcString);
            var diff = currentTime.diff(eventTime);
            var remain = moment.duration(expired - diff);
            //console.log('remain =' + remain);
            if (remain <= 0) {
                $('.expire-alert').html('<h6><i class="fa fa-exclamation-triangle" aria-hidden="true"></i>CTOT expired, Please try again</h>');
                $('.back-modal-btn').removeClass('position-absolute');
                $ctotModal.find('.ctot-confirm-form').hide();
                $ctotConfirmBtn.hide();
            }
            else
                $('.expire-alert').html('CTOT expires in ' + moment(remain.as('milliseconds')).format('mm:ss') + ' Minutes');


        }, interval);

        $flightInfo.find('.dep-info').html(cflight.flight.airportDeparture);
        $flightInfo.find('.arr-info').html(cflight.flight.airportArrival);
        $flightInfo.find('.eobt-info').html(formatDateTime(cflight.flight.eobt));
        $flightInfo.find('.etot-info').html(formatDateTime(cflight.flight.etot));
        $flightInfo.find('.eldt-info').html(formatDateTime(cflight.flight.eldt));
        $flightInfo.find('.eibt-info').html(formatDateTime(cflight.flight.eibt));
        var ctot = (cflight.flight.ctot != "" && cflight.flight.ctot != null) ? formatDateTime(cflight.flight.ctot) : '-'
        var cldt = (cflight.flight.cldt != "" && cflight.flight.cldt != null) ? formatDateTime(cflight.flight.cldt) : '-'
        $flightInfo.find('.ctot-info').html(ctot);
        $flightInfo.find('.cldt-info').html(cldt);

        //var $getMannualCtotBtn = $ctotModal.find('.get-mannual-ctot');
        //get mauual ctot
        $getMannualCtotBtn.off('click').on('click', function () {
            $('.mannual-ctot-result').html('CTOT 10:00 (Sample)');
        });

        //var $ctotConfirmBtn = $ctotModal.find('.confirm-modal-btn');
        $ctotModal.modal('show');
        eventCtotModalClose(countdown);


        //confirm button
        $ctotConfirmBtn.off('click').on('click', function () {
            if (isCtotRadioValid()) {
                $('#loading-overlay').show();
                var select = $('input[name="ctotradio"]:checked').val();
                clearInterval(countdown);
                var data = {
                    gid: gdpid,
                    cid: select,
                    comment: $comment.val(),
                    source: 'ctotDistributor'
                };
                $.ajax({
                    type: 'POST',
                    url: '/GDPManage/ConfirmCtot',
                    dataType: 'json',
                    data: data,
                    success: function (res) {
                        if (res.success) {

                            var i = ctotTrial.ctotFlights.length - 1;
                            for (i; i >= 0; i--) {
                                if (ctotTrial.ctotFlights[i].flightId == fid) {
                                    ctotTrial.ctotFlights.splice(i, 1);
                                }
                            }

                            updateCtotTable();
                            var $toast = $('#success-toast');
                            $toast.find('.toast-body').text('Flight ' + cflight.flight.callsign + ' Change CTOT Successfully');
                            $toast.toast('show');
                            console.log('save ctot successfull');
                        }
                        else {
                            var $toast = $('#error-toast');
                            $toast.find('.toast-body').text(res.error);
                            console.log('save ctot error');
                        }
                    },
                    error: function (req, status, error) {
                        console.log(error);
                        alert('error return save ctot');
                    },
                    complete: function () {
                        $ctotModal.modal('hide');
                        $confirmModal.modal('hide');
                        $('#loading-overlay').hide();
                    }
                });

                // });
                // $confirmModal.modal('show');

            }


        });
        $ctotBackBtn.off('click').on('click', function () {
            $('.ctot-radio-error').text('');
        });

    });

    //cancel trial ctot

    $tdCancelCtot.on('click', function () {

        var fid = $(this).attr('fid');
        var data = { fid: fid };

        $.ajax({
            type: 'POST',
            url: '/GDPManage/CancelCtot',
            dataType: 'json',
            data: data,
            success: function (result) {
                if (result) {
                    var i = ctotTrial.ctotFlights.length - 1;
                    for (i; i >= 0; i--) {
                        if (ctotTrial.ctotFlights[i].flightId == fid) {
                            ctotTrial.ctotFlights.splice(i, 1);
                        }
                    }
                    updateCtotTable();
                }
            },
            error: function (req, status, error) {
                console.log(error);
                alert('error return save ctot');
            }

        });
    });


    $('td.td-comment > span').on('click', function () {
        $(this).toggleClass("show");
    });
} //func eventTableBtn

function getGdpId(str) {
    var resultstr = str;
    if (str.includes(","))
        resultstr = str.substring(0, str.indexOf(":"));
    return parseInt(resultstr);

}

function eventCtotModalClose(countdown) {
    var $ctotModal = $('#ctot-modal');
    $ctotModal.on('hidden.bs.modal', function () {
        $('.expire-alert').html('CTOT expires in ');
        clearInterval(countdown);
    })
}

function eventEditFlightModal(modalObj) {
    var $flightModal = $('#edit-flight-modal');
    var $flightModalEdit = $flightModal.find('.confirm-edit-flight-btn');
    var $confirmModal = $('#confirm-modal');
    var $alertModal = $('#alert-modal');

    var nowUtcString = moment.utc().toISOString().replace('Z', '');
    var current = moment(nowUtcString);
    //var $newEobt = $('#EobtEdt').val();

    $flightModalEdit.off('click').on('click', function () {
        $flightModal.find('.comment-error').html('');
        $flightModal.find('.eobt-error').html('');
        var $newEobt = $('#EobtFlightEdt').val();
        var $comment = $('#Comment').val();
        //celegana check New OBT must not less than Current Time
        if (neweobtValid($newEobt, modalObj.flight.etot, modalObj.flight.eobt)) {
            $flightModal.modal('hide');
            $confirmModal.find('.modal-title').text('Change OBT Confirmation');
            //var $TableTitle = $('<i></i>').append($tdTableTitle);
            var $table = $('<table class="table table-borderless td-table"></table>');
            var $thead = $('<thead><tr><th scope="col">Callsign</th><th scope="col">EOBT (FPL)</th><th scope="col">New OBT</th></tr></thead>');
            var $tbody = $('<tbody><tr></tr></tbody>');
            $('<td></td>').html(modalObj.flight.callsign).appendTo($tbody);//
            $('<td></td>').html(formatDateTime(modalObj.flight.eobt)).appendTo($tbody);//
            $('<td class="table-info"></td>').html(formatDateTime($newEobt)).appendTo($tbody);

            var $TableTitle = $table.append($thead).append($tbody);
            var confirmComment = ($comment != "" && $comment != null) ? $comment : "-";
            $confirmModal.find('.modal-body p').html('Confirm requesting new OBT for the following flight?<br><br>').append($TableTitle).append('<p><span style="font-weight:500">Comment  </span>' + confirmComment + '</p>');
            $confirmModal.modal('show');
            $confirmModal.find('.confirm-modal-btn').off('click').on('click', function () {
                var preview = '<i class="fa fa-spinner fa-spin"></i>';
                $confirmModal.find('.confirm-modal-btn').prepend(preview);


                //CTOT Forwarding
                if (modalObj.isForwarding) {

                    var data = { fid: modalObj.flight.id, neweobt: $newEobt, comment: $comment };

                    $.ajax({
                        type: 'POST',
                        url: '/CTOTDistributor/RequestEobtairline',
                        dataType: 'json',
                        data: data,
                        success: function (result) {
                            $confirmModal.modal('hide');
                            $confirmModal.find('.fa-spin').remove();
                            $('#loading-overlay').hide();
                            $alertModal.modal('show');
                            $alertModal.find('.info-box-icon').html('<i class="fa fa-exclamation-circle text-primary"></i>');
                            $alertModal.find('.info-box-content').html('<p>Your CTOT is generated by <u>' + modalObj.flight.originator + '</u></p><p>Bangkok ATFMU has been notified.</p>');
                            updateCtotTable();
                        },
                        error: function (req, status, error) {
                            console.log(error);
                            //alert('Unable to request GDP information');
                        }
                    });
                }
                //CTOT GDP
                else {
                    //test call change ctot                       
                    var data = {
                        gdpid: modalObj.gdpid,
                        flight: modalObj.flight,
                        newEobt: $newEobt,
                        comment: $comment
                    };

                    $.ajax({
                        type: 'POST',
                        url: '/GDPManage/RequestCtot',
                        dataType: 'json',
                        data: data,
                        success: function (result) {

                            ctotTrial = result;
                            if (!result.isGdpflight) {
                                var $toast = $('#error-toast');
                                $toast.find('.toast-body').text(result.errMsg);
                                $toast.toast('show');
                            }
                            else {
                                var $toast = $('#success-toast');
                                $toast.find('.toast-body').text('Success Request New OBT !');
                                $toast.toast('show');
                                //GDPManage.updateChartsTables();
                            }
                            updateCtotTable();
                            $confirmModal.modal('hide');
                        },
                        error: function (req, status, error) {
                            console.log(error);
                            alert('Unable to request GDP information');
                        },
                        complete: function () {

                            $confirmModal.find('.fa-spin').remove();
                            console.log('request cal ctot complete');
                        }
                    });
                }

            });
        }
        else {
            $flightModal.find('.eobt-error').html('New OBT must not be earlier than the current time.');
        }
    });
}

function callsignCellStyle(value, row) {
    if (row.flight.isExempt) {
        return {
            css: { color: 'red' }
        }
    }
    else
        return {
            css: { color: "#000" }
        }
}
function formatDateTime(datetime) {
    if (datetime == null || datetime == "")
        return "-"
    else
        return datetime.substr(0, 10) + ' ' + datetime.substr(11, 5);
}
function dateTimeFormatter(value) {
    if (value == null || value == "")
        return "-"
    else
        return value.substr(8, 2) + ' / ' + value.substr(11, 5);
}
function ctotFormatter(value, row) {
    var editBtn = '<button type="button" fid="' + value.id + '" class="btn btn-link no-padding edit-flight-btn text-primary"><i class="fas fa-pen"></i></button>';
    //CTOT Trial
    var nowUtcString = moment.utc().toISOString().replace('Z', '');
    var current = moment(nowUtcString);
    var count = 0;
    var remain = moment.duration(ctotTrial.ctotTimeout).minutes();
    var expired = ctotTrial.ctotTimeout;// 1800000  600000
    var tdCtot = '<u>New CTOT Available</u>';
    var isTrial = false;
    var tdListCtot = '';

    if (value.ctot == null || value.ctot == "") {
        isEdit = false;
        return "-"
    }
    else
        isEdit = true;

    if (ctotTrial.ctotFlights) {
        for (var i = 0; i < ctotTrial.ctotFlights.length; i++) {
            if (value.id == ctotTrial.ctotFlights[i].flightId) {

                var start = moment(ctotTrial.ctotFlights[i].timeSaved);
                var diff = current.diff(start);
                if (diff < expired && remain > 0) {
                    count = count + 1;
                    tdListCtot += '<h6>' + moment(ctotTrial.ctotFlights[i].ctot).format("HH:mm") + ' (C' + count + ')' + '</h6>';
                    isTrial = true;
                    remain = moment.duration(expired - diff).minutes();
                }
                tdCtot += '<div class="expired-time">*CTOT expires in ' + (remain + 1) + ' min</div>';
                tdCtot += tdListCtot;
                tdCtot += '<button type="button" fid="' + value.id + '" class="btn btn-link no-padding cancel-ctot-btn" >Cancel Request</button>';
                tdCtot += ' | ';
                tdCtot += '<button type="button" fid="' + value.id + '" class="btn btn-link no-padding confirm-ctot-btn">Select CTOT</button>';
            }
        }

    }
    if (isEdit) {
        //console.log('callsign = ' + value.callsign+ 'isedit =  ctot= ' + moment(value.ctot) + 'dif =' + moment(value.ctot).diff(current) + 'ATOT' + value.atot);
        if (isTrial)
            return tdCtot;
        else if ((ctotTrial.ctotAirlineBlock > 0 && moment(value.ctot).diff(current) <= (ctotTrial.ctotAirlineBlock)) || row.isRemove || value.atot != null || value.aldt != null) //block if lessandequal than block time or already departed
        {
            return moment(value.ctot).format('DD / HH:mm');
        }
        else
            return moment(value.ctot).format('DD / HH:mm') + ' ' + editBtn;
    }
    else
        return moment(value.ctot).format('DD / HH:mm');
}
function runningFormatter(value, row, index) {
    return index + 1;
}
function commentFormatter(value, row) {
    var comment = '<span>' + value + '</span>';
    var commentAppend = "";
    if (value != "-")
        commentAppend = '<i class="comment-append">(' + value + ')</i>';
    if (row.forwardStatus == 1) comment = '<span class="status-submit text-primary">Request Submitted ' + commentAppend + '</span>';
    if (row.forwardStatus == 2) comment = '<span class="status-progress">Request in Progress ' + commentAppend + '</span>';
    if (row.forwardStatus == 3) comment = '<span class="status-updated text-success">New CTOT Delivered ' + commentAppend + '</span>';
    if (row.forwardStatus == 4) comment = '<span class="status-terminated text-danger">Unable to Process, Please Contact Bangkok ATFMU at +6622878024 ' + commentAppend + '</span>';
    //console.log('ctotflight update =' + row.flight.callsign + '  ' + row.forwardStatus);
    return comment;

}
function rowStyle(row, index) {
    if (row.isUpdate)
        return { classes: 'table-info' }
    if (row.isRemove)
        return { classes: 'table-danger' }
    else
        return { classes: '' }
}