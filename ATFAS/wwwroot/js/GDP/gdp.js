﻿$(document).ready(function () {
    GDP.init();
});

GDP = {
    gdpObj: new Object(), // id => gdp
    capacityObj: new Object(), // point => capacityPerHr
    eventObj: new Object(), // id => capacityEvent
    gdpCharts: [], // executed tdChartObj

    init: function () {
        GDP.eventParamOptionRadio();
        GDP.requestGDPInfo();
        GDP.eventPointInput();
        GDP.eventCapacitySelect();
        TrafficDemand.formFn = GDP.formFn;
        GDP.eventSaveModalForm();
        GDP.eventDesignatorSelect();
        GDP.eventChangeModalBtn();
        setInterval(GDP.updateCharts, 60000);
    },

    updateCharts: function () {
        if (GDP.gdpCharts.length == 0) {
            if (TrafficDemand.tdCharts.length == 0) $('#td-update').hide();
            return;
        }
        var $tdUpdate = $('#td-update');
        var $tdUpdateTime = $tdUpdate.find('.td-update-time');
        for (var i = 0; i < GDP.gdpCharts.length; i++) {
            let tdChartObj = GDP.gdpCharts[i];
            var data = Object.assign({}, tdChartObj.tdChart.gdp, {gdpFlights: null});
            $.ajax({
                type: 'POST',
                url: '/GDP/RequestChart',
                dataType: 'json',
                data: data,
                success: function (gdpChart) {
                    tdChartObj.tdChart = gdpChart;
                    tdChartObj.chart.data.datasets.forEach((dataset) => {
                        if (dataset.order == 4) dataset.data = gdpChart.dataPassed;
                        else if (dataset.order == 5) dataset.data = gdpChart.dataSUR;
                        else if (dataset.order == 6) dataset.data = gdpChart.dataATSMSG;
                        else if (dataset.order == 7) dataset.data = gdpChart.dataATFM;
                        else if (dataset.order == 8) dataset.data = gdpChart.dataFPL;
                        else if (dataset.order == 11) dataset.data = gdpChart.capacities;
                    });
                    tdChartObj.chart.update();
                    $tdUpdate.show();
                    $tdUpdateTime.text(gdpChart.time);
                    tdChartObj.container.siblings('.td-chart-warning').hide();
                    TrafficDemand.addEventTdChart(gdpChart, tdChartObj.container, tdChartObj.chart);
                },
                error: function (req, status, error) {
                    console.log(error);
                    tdChartObj.container.siblings('.td-chart-warning').show();
                }
            });
        }
    },

    eventChangeModalBtn: function () {
        $changeModal = $('#change-state-modal');
        $changeModal.find('form').on('submit', function (e) {
            if ($(this).valid()) {
                e.preventDefault();
                GDP.handleEventChangeModalBtn($changeModal, $changeModal.find('.execute-btn'), $changeModal.find('.regul').val(), $changeModal.find('.regcause').val(), $changeModal.find('.comment').val());
            }
        });
        $changeModal.find('.undo-btn').on('click', function () {
            GDP.handleEventChangeModalBtn($changeModal, $(this), '', '', '');
        });
    },

    eventDesignatorSelect: function () {
        $('#measure-designator').on('change', function (e) {
            var objSelected = GDP.gdpObj[this.value];
            if (objSelected) {
                $('#TrafficAreaId').val(objSelected.trafficAreaId).trigger('change');
                $('#Point').val(objSelected.point).focus();
                $('#LowerFlightLevel').val(objSelected.lowerFlightLevel);
                $('#UpperFlightLevel').val(objSelected.upperFlightLevel);
                $('#RadiusNm').val(objSelected.radiusNm);
                var times = objSelected.startTime.split('T');
                $('#StartTime').val(times[0] + ' ' + times[1].substr(0, 5)).focus();
                times = objSelected.endTime.split('T');
                $('#EndTime').val(times[0] + ' ' + times[1].substr(0, 5)).focus();
                times = objSelected.endRecoveryTime.split('T');
                $('#EndRecoveryTime').val(times[0] + ' ' + times[1].substr(0, 5)).focus();
                $('#CapacityPerHr').val(objSelected.capacityPerHr).focus();
                $('#CapacityRecoveryPerHr').val(objSelected.capacityRecoveryPerHr).focus();
                $('#OnlyADEP').val(objSelected.onlyADEP).focus();
                $('#OnlyADES').val(objSelected.onlyADES).focus();
                $('#OnlyAirlines').val(objSelected.onlyAirlines).focus();
                if (objSelected.onlyADEP || objSelected.onlyADES || objSelected.onlyAirlines) $('#OnlyFilter').collapse('show');
                else $('#OnlyFilter').collapse('hide');
                $('#ExemptADEP').val(objSelected.exemptADEP).focus();
                $('#ExemptADES').val(objSelected.exemptADES).focus();
                $('#ExemptAirlines').val(objSelected.exemptAirlines).focus();
                if (objSelected.exemptADEP || objSelected.exemptADES || objSelected.exemptAirlines) $('#Exempt').collapse('show');
                else $('#Exempt').collapse('hide');
                $('#IntervalMin').val(objSelected.intervalMin).focus();
                $('#IsIFR').prop('checked', objSelected.isIFR);
                $('#IsVFR').prop('checked', objSelected.isVFR);
                if (!objSelected.isIFR && !objSelected.isVFR) {
                    $('#IsIFR').prop('checked', true);
                    $('#IsVFR').prop('checked', true);
                }
                $('#td-fieldset').prop('disabled', true).find('.field-validation-error').children().remove();
                $(this).focus();
            } else {
                $('#td-fieldset').prop('disabled', false);
            }
        });
    },

    eventParamOptionRadio: function () {
        $('#measure-radio').on('change', function (e) {
            if ($(this).is(':checked')) {
                $('#measure-designator, #td-form .measure-param').show();
                $('#designator, #td-form .td-param').hide();
                $('#td-clear').trigger('click');
                TrafficDemand.formFn = GDP.formFn;
            }
        });
        $('#demand-radio').on('change', function (e) {
            if ($(this).is(':checked')) {
                $('#designator, #td-form .td-param').show();
                $('#measure-designator, #td-form .measure-param').hide();
                $('#td-clear').trigger('click');
                TrafficDemand.formFn = TrafficDemand.tdFormFn;
            }
        });
    },

    requestGDPInfo: function () {
        $('#loading-overlay').show();
        $.ajax({
            type: 'POST',
            url: '/GDP/RequestGDPInfo',
            dataType: 'json',
            success: function (info) {
                var $designator = $('#measure-designator');
                var $last = $designator.children().last();
                $.each(info.gdpSaved, function (i, item) {
                    $last.before($('<option>', {
                        value: item.id,
                        text: item.designator
                    }));
                    GDP.gdpObj[item.id] = item;
                });
                $.each(info.gdpExecuted, function (i, item) {
                    $designator.append($('<option>', {
                        value: item.id,
                        text: item.designator
                    }));
                    GDP.gdpObj[item.id] = item;
                });
                $.each(info.capacities, function (i, item) {
                    GDP.capacityObj[item.point] = item.capacityPerHr;
                });
                var $eventSelect = $('#event-capacity-select');
                $.each(info.capacityEvents, function (i, item) {
                    $eventSelect.append($('<option>', {
                        value: item.id,
                        text: item.reason ? item.reason : GDP.formatDateTime(item.startTime) + ' (' + item.capacityPerHr + ')'
                    }));
                    GDP.eventObj[item.id] = item;
                });
                $.each(info.gdpCharts, function (i, item) {
                    GDP.handleGDPChartRequest(item);
                });
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to request GDP information');
            },
            complete: function () {
                $('#loading-overlay').hide();
            }
        });
    },

    eventPointInput: function () {
        $('#Point').on('blur', function () {
            $('#CapacityRecoveryPerHr').val(GDP.capacityObj[$(this).val()]).trigger('blur');
        });
    },

    eventCapacitySelect: function () {
        $('#event-capacity-select').on('change', function () {
            var capacityEvent = GDP.eventObj[$(this).val()];
            if (capacityEvent) {
                $('#StartTime').val(GDP.formatDateTime(capacityEvent.startTime)).trigger('blur');
                $('#EndTime').val(GDP.formatDateTime(capacityEvent.endTime)).trigger('blur');
                $('#CapacityPerHr').val(capacityEvent.capacityPerHr).trigger('blur');
            }
        });
    },

    formFn: function () {
        var data = {
            Id: $('#measure-designator').val(),
            Designator: $('#measure-designator option:selected').text(),
            TrafficAreaId: $('#TrafficAreaId').val(),
            Point: $('#Point').val(),
            LowerFlightLevel: $('#LowerFlightLevel').val(),
            UpperFlightLevel: $('#UpperFlightLevel').val(),
            RadiusNm: $('#RadiusNm').val(),
            CapacityRecoveryPerHr: $('#CapacityRecoveryPerHr').val(),
            StartTime: $('#StartTime').val(),
            EndTime: $('#EndTime').val(),
            EndRecoveryTime: $('#EndRecoveryTime').val(),
            CapacityPerHr: $('#CapacityPerHr').val(),
            OnlyADEP: $('#OnlyADEP').val(),
            OnlyADES: $('#OnlyADES').val(),
            OnlyAirlines: $('#OnlyAirlines').val(),
            ExemptADEP: $('#ExemptADEP').val(),
            ExemptADES: $('#ExemptADES').val(),
            ExemptAirlines: $('#ExemptAirlines').val(),
            IntervalMin: $('#IntervalMin').val(),
            IsIFR: $('#IsIFR').is(':checked'),
            IsVFR: $('#IsVFR').is(':checked')
        };
        $.ajax({
            type: 'POST',
            url: '/GDP/RequestChart',
            dataType: 'json',
            data: data,
            success: function (gdpChart) {
                GDP.handleGDPChartRequest(gdpChart);
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to request GDP Chart');
            },
            complete: function () {
                $('#loading-overlay').hide();
            }
        });
    },

    eventSaveModalForm: function () {
        var $saveModal = $('#save-gdp-modal');
        $saveModal.find('form').on('submit', function (e) {
            if ($(this).valid()) {
                e.preventDefault();
                $saveModal.modal('hide');
                var tdChartObj = $(this).data('tdChartObj');
                var data = tdChartObj.tdChart.gdp;
                data.designator = $saveModal.find('.designator').val();
                $.ajax({
                    type: 'POST',
                    url: '/GDP/Create',
                    dataType: 'json',
                    data: data,
                    success: function (gdpChart) {
                        if (!gdpChart.gdp) {
                            alert('Unable to save regulated demand parameter');
                            return;
                        }
                        var tdToast = $('#success-toast');
                        tdToast.find('.toast-body').text('Regulated demand parameter (' + data.designator + ') is successfully saved.');
                        tdToast.find('.success-toast-time').text(gdpChart.time);
                        tdToast.toast('show');
                        $('#measure-designator .executed').before($('<option>', {
                            value: gdpChart.gdp.id,
                            text: gdpChart.gdp.designator
                        }));
                        GDP.gdpObj[gdpChart.gdp.id] = gdpChart.gdp;
                        //tdChartObj.tdChart.trafficDemand = tdChart.trafficDemand;
                        tdChartObj.container.parent().find('.td-chart-save').hide();
                        var chartTitle = tdChartObj.chart.options.title.text;
                        tdChartObj.chart.options.title.text = [gdpChart.gdp.designator, chartTitle];
                        tdChartObj.chart.update();
                        tdChartObj.tdChart.gdp.id = gdpChart.gdp.id;
                        tdChartObj.tdChart.trafficDemand.id = gdpChart.gdp.id;
                        GDP.eventChartBtn(tdChartObj);
                    },
                    error: function (req, status, error) {
                        console.log(error);
                        alert('Unable to save traffic demand parameter');
                    }
                });
            }
        });
    },

    /******************* Helper Functions *********************/

    handleEventChangeModalBtn: function ($changeModal, $btn, regul, regcause, comment) {
        $changeModal.modal('hide');
        var tdChartObj = $changeModal.data('tdChartObj');
        var stateId = tdChartObj.tdChart.gdp.isExecuted ? 2 : 1;
        stateId += $btn.data('state');
        $.ajax({
            type: 'POST',
            url: '/GDP/ChangeState',
            dataType: 'json',
            data: {
                gdpId: tdChartObj.tdChart.gdp.id,
                stateId: stateId,
                regul: regul,
                regcause: regcause,
                comment: comment
            },
            success: function (gdpChart) {
                if (!gdpChart.gdp) {
                    var $toast = $('#error-toast');
                    $toast.find('.toast-body').text(gdpChart.title);
                    $toast.toast('show');
                    return;
                }
                var $toast = $('#success-toast');
                $toast.find('.toast-body').text('Regulated demand state (' + gdpChart.gdp.designator + ') is successfully changed.');
                $toast.find('.success-toast-time').text(gdpChart.time);
                $toast.toast('show');
                var $designator = $('#measure-designator');
                $designator.children().each(function () {
                    var $this = $(this);
                    if ($this.val() == gdpChart.gdp.id) {
                        $this.remove();
                        return false;
                    }
                });
                if (gdpChart.gdp.isExecuted) {
                    $designator.append($('<option>', {
                        value: gdpChart.gdp.id,
                        text: gdpChart.gdp.designator
                    }));
                    tdChartObj.container.parent().addClass('border-danger').removeClass('border-info');
                    tdChartObj.container.siblings('.state').text('EXE').addClass('text-danger').removeClass('text-info');
                    tdChartObj.container.siblings('.td-chart-btn').find('.td-chart-change').attr('data-original-title', 'show regulated demand information');
                    tdChartObj.tdChart.gdp = gdpChart.gdp;
                    GDP.gdpCharts.push(tdChartObj);
                } else if (!gdpChart.gdp.isCancelled) {
                    $designator.children('.executed').before($('<option>', {
                        value: gdpChart.gdp.id,
                        text: gdpChart.gdp.designator
                    }));
                    tdChartObj.container.parent().addClass('border-info').removeClass('border-danger');
                    tdChartObj.container.siblings('.state').text('S').addClass('text-info').removeClass('text-danger');
                    tdChartObj.container.siblings('.td-chart-btn').find('.td-chart-change').attr('data-original-title', 'change state');
                    tdChartObj.tdChart.gdp = gdpChart.gdp;
                    for (var i = 0; i < GDP.gdpCharts.length; i++) {
                        if (GDP.gdpCharts[i].container == tdChartObj.container) GDP.gdpCharts.splice(i, 1);
                    }
                } else {
                    GDP.gdpObj[gdpChart.gdp.id] = null;
                    var $chartBtn = tdChartObj.container.siblings('.td-chart-btn');
                    $chartBtn.find('.td-chart-change').hide();
                    $chartBtn.find('.td-chart-save').show();
                    var chartTitle = tdChartObj.chart.options.title.text;
                    tdChartObj.chart.options.title.text = chartTitle[1];
                    tdChartObj.chart.update();
                    tdChartObj.container.parent().removeClass('border border-info rounded-lg');
                    tdChartObj.container.siblings('.state').remove();
                    tdChartObj.container.siblings('.td-chart-btn').removeClass('mt-1 mr-2');
                    tdChartObj.tdChart.trafficDemand.id = -1;
                    tdChartObj.tdChart.gdp.id = -1;
                    GDP.eventChartBtn(tdChartObj);
                }
                //tdChartObj.tdChart.trafficDemand = tdChart.trafficDemand;
            },
            error: function (req, status, error) {
                console.log(error);
                alert('Unable to change regulated demand state');
            }
        });
    },

    handleGDPChartRequest: function (gdpChart) {
        if (!gdpChart.gdp) {
            TrafficDemand.showError(gdpChart.title);
            return;
        }
        var $tdChartContainer = $('#td-chart-container');
        $tdChartContainer.append($tdChartContainer.children().eq(0).clone().show());
        var $tdChart = $tdChartContainer.find('.td-chart').last();
        var chart = TrafficDemand.createChart(gdpChart, $tdChart);
        var tdChartObj = {
            tdChart: gdpChart,
            container: $tdChart,
            chart: chart
        };
        TrafficDemand.addEventChartBtn(tdChartObj);
        GDP.eventChartBtn(tdChartObj);
    },

    eventChartBtn: function (tdChartObj) {
        if (tdChartObj.tdChart.gdp.id < 0) {
            var $tdChartSave = tdChartObj.container.siblings('.td-chart-btn').find('.td-chart-save');
            $tdChartSave.off('click').on('click', function () {
                GDP.updateModalInfo($('#save-gdp-modal'), tdChartObj);
            });
        } else {
            var $state = $('<h4 style="top:0;"></h4>').addClass('position-absolute mt-1 ml-3 state');
            var $changeBtn = tdChartObj.container.siblings('.td-chart-btn').find('.td-chart-change');
            if (!tdChartObj.tdChart.gdp.isExecuted) {
                tdChartObj.container.parent().addClass('border border-info rounded-lg');
                $state.text('S').addClass('text-info');
                $changeBtn.attr('data-original-title', 'change state');
            } else {
                tdChartObj.container.parent().addClass('border border-danger rounded-lg');
                $state.text('EXE').addClass('text-danger');
                $changeBtn.attr('data-original-title', 'show regulated demand information');
                GDP.gdpCharts.push(tdChartObj);
            }
            tdChartObj.container.siblings('.td-chart-btn').addClass('mt-1 mr-2');
            tdChartObj.container.after($state);
            $changeBtn.show().off('click').on('click', function () {
                var $changeModal = $('#change-state-modal');
                $changeModal.find('.designator').text(tdChartObj.tdChart.gdp.designator);
                if (!tdChartObj.tdChart.gdp.isExecuted) {
                    $changeModal.find('.execute-btn, .regul-row, .regcause-row, .comment-row').show();
                    $changeModal.find('.undo-btn').text('Unsave').show();
                    $changeModal.find('.modal-title').text('Change Regulated Demand State');
                    $changeModal.find('.cancel-btn').text('Cancel');
                } else {
                    //$changeModal.find('.undo-btn').text('Unexecute');
                    $changeModal.find('.execute-btn, .undo-btn, .regul-row, .regcause-row, .comment-row').hide();
                    $changeModal.find('.modal-title').text('Regulated Demand Information');
                    $changeModal.find('.cancel-btn').text('Close Window');
                }
                $changeModal.data('tdChartObj', tdChartObj);
                GDP.updateModalInfo($changeModal, tdChartObj);
            });
        }    
    },

    updateModalInfo: function ($saveModal, tdChartObj) {
        var $saveModalForm = $saveModal.find('form');
        $saveModalForm.trigger('reset');
        $saveModal.find('.td-airport, .td-waypoint, .td-sector, .td-only-adep, .td-only-ades, .td-only-airlines, .td-adep, .td-ades, .td-airlines').show();
        $saveModal.find('.area').text(tdChartObj.tdChart.gdp.trafficArea.type);
        $saveModal.find('.point').text(tdChartObj.tdChart.gdp.point);
        var trafficAreaId = tdChartObj.tdChart.gdp.trafficAreaId;
        if (trafficAreaId == 1) $saveModal.find('.td-waypoint, .td-sector').hide();
        else {
            if (trafficAreaId == 2) {
                $saveModal.find('.td-airport, .td-sector').hide();
                $saveModal.find('.td-waypoint').show();
                if (tdChartObj.tdChart.gdp.radiusNm) $saveModal.find('.radius').text(tdChartObj.tdChart.gdp.radiusNm);
                else $saveModal.find('.td-radius').hide();
            } else {
                $saveModal.find('.td-airport, .td-waypoint').hide();
                $saveModal.find('.td-sector').show();
            }
            if (tdChartObj.tdChart.gdp.lowerFlightLevel != null) $saveModal.find('.lower').text(tdChartObj.tdChart.gdp.lowerFlightLevel);
            else $saveModal.find('.td-lower').hide();
            if (tdChartObj.tdChart.gdp.upperFlightLevel != null) $saveModal.find('.upper').text(tdChartObj.tdChart.gdp.upperFlightLevel);
            else $saveModal.find('.td-upper').hide();
        }
        var startArray = tdChartObj.tdChart.gdp.startTime.split('T');
        var endArray = tdChartObj.tdChart.gdp.endTime.split('T');
        var endRecoveryArray = tdChartObj.tdChart.gdp.endRecoveryTime.split('T');
        $saveModal.find('.start').text(startArray[0] + ' ' + startArray[1].substr(0, 5));
        $saveModal.find('.end').text(endArray[0] + ' ' + endArray[1].substr(0, 5));
        $saveModal.find('.end-recovery').text(endRecoveryArray[0] + ' ' + endRecoveryArray[1].substr(0, 5));
        $saveModal.find('.capacity').text(tdChartObj.tdChart.gdp.capacityPerHr);
        $saveModal.find('.capacity-recovery').text(tdChartObj.tdChart.gdp.capacityRecoveryPerHr);
        if (tdChartObj.tdChart.gdp.onlyADEP) $saveModal.find('.only-adep').text(tdChartObj.tdChart.gdp.onlyADEP);
        else $saveModal.find('.td-only-adep').hide();
        if (tdChartObj.tdChart.gdp.onlyADES) $saveModal.find('.only-ades').text(tdChartObj.tdChart.gdp.onlyADES);
        else $saveModal.find('.td-only-ades').hide();
        if (tdChartObj.tdChart.gdp.onlyAirlines) $saveModal.find('.only-airlines').text(tdChartObj.tdChart.gdp.onlyAirlines);
        else $saveModal.find('.td-only-airlines').hide();
        if (tdChartObj.tdChart.gdp.exemptADEP) $saveModal.find('.adep').text(tdChartObj.tdChart.gdp.exemptADEP);
        else $saveModal.find('.td-adep').hide();
        if (tdChartObj.tdChart.gdp.exemptADES) $saveModal.find('.ades').text(tdChartObj.tdChart.gdp.exemptADES);
        else $saveModal.find('.td-ades').hide();
        if (tdChartObj.tdChart.gdp.exemptAirlines) $saveModal.find('.airlines').text(tdChartObj.tdChart.gdp.exemptAirlines);
        else $saveModal.find('.td-airlines').hide();
        $saveModal.find('.interval').text(tdChartObj.tdChart.gdp.intervalMin);
        $saveModal.find('.ifr').prop('checked', tdChartObj.tdChart.gdp.isIFR);
        $saveModal.find('.vfr').prop('checked', tdChartObj.tdChart.gdp.isVFR);
        $saveModalForm.data('tdChartObj', tdChartObj);
        $saveModal.modal('show');
    },

    formatDateTime: function (datetime) {
        return datetime.substr(0, 10) + ' ' + datetime.substr(11, 5);
    }
};