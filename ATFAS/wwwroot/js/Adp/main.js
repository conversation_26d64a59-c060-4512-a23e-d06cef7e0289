﻿/* ADP */
var constraintArray = [];
var atfmArray = [];
var issueAray = [];
var createLocationConstraint = [];
var PreviousADP = [];
var TodayADP = [];
var NextADP = [];
var recentTodayADP = [];
var recentNextADP = [];
var WeatherBriefFileArray = [];
var AirspaceBriefFileArray = [];
var WeatherBriefFilePath4Preview = [];
var AirspaceBriefFilePath4Preview = [];
var adp = {};
var datetimeNow = new Date();
var month = ["JAN", "FEB", "MAR", "APR", "May", "JUN", "JUL", "AUG", "SEP", "OCT", "NOV", "DEC"];
var dateControl = document.querySelector('input[type="date"]');

$(document).ready(function () {
    $('#loading-overlay').show();

    $(".input-datetime").datetimepicker({
        format: "Y-m-d H:i", // typical xdan plugin format
        step: 30
    });

    $(document).on('click', '.removeFile', function () {
        $(this).parent().remove();
    });

    $(document).on('click', '.removeFileEdit', function () {
        $(this).parent().remove();
        $('.card-file-' + $(this).attr('value')).remove();
    });

    $(document).on('click', '.pop', function () {
        openModalImage($(this).find('img').attr('src'));
    });


    ////console.log(datetimeNow.toISOString().split('T')[0]);
    dateControl.min = datetimeNow.toISOString().split('T')[0];

    AddRowEvent();
    RemoveRowEvent();
    OnclickCreateandEditAdp();
    GetPreviousADP();
    GetTodayADP();
    GetNextAdp();
    CloseModal();
    OnSelectDatepicker();
    OnclickDownloadPdf();
    OnclickPreviewPDF();
    OnclickOpenModal();
    OnTableInputChange();
});

function GetTodayADP() {

    $('.adp-current').find('.WeatherImage').html('');
    $('.adp-current').find('.AirspaceImage').html('');
    $.ajax({
        type: 'GET',
        url: '/Adp/GetTodayAdp',
        dataType: 'json',
        success: function (res) {
            console.log("GetTodayAdp")
            console.log(res)
            TodayADP = res;
            if (TodayADP != undefined) {
                $("#edit-today-modal-btn").show();
                $("#downloadTodayPdf").show();
                var issueTime = new Date(res.issueTime);
                var displayIssueTime = issueTime.getDate() + ' ' + month[issueTime.getMonth()] + ' ' + issueTime.getFullYear() + ' ' + (issueTime.getHours() < 10 ? '0' : '') + issueTime.getHours() + (issueTime.getMinutes() < 10 ? '0' : '') + issueTime.getMinutes();
                var possibleArray = res.adpConstraints.filter(c => c.isConfirmed == 0)
                var constraintsArray = res.adpConstraints.filter(c => c.isConfirmed == 1)
                $('#TodayAdpRevision').html(res.revision);
                $('#TodayIssueTime').html(displayIssueTime + '(UTC)');
                $('#TodayOtherInfo').html(res.otherInformation);
                $('#TodayWeatherBriefing').html(res.weatherBriefing);
                $('#TodayAirspaceBriefing').html(res.airspaceStatusBriefing);
                if (res.username == null) {
                    $('#TodayCreateBy').html('Created by : -');
                }
                else {
                    $('#TodayCreateBy').html('Created by : ' + res.username);
                }

                for (var i = 0; i < constraintsArray.length; i++) {
                    if (constraintsArray[i].location != null) {
                        const constraintsStartTime = formatDateTime(constraintsArray[i].startTime);
                        const constraintsEndTime = formatDateTime(constraintsArray[i].endTime);
                        const detail = constraintsArray[i].detail == null ? " - " : constraintsArray[i].detail;
                        const remark = constraintsArray[i].remark == null ? " - " : constraintsArray[i].remark;
                        $('#tblTodayADPConstrains').append(` <tr>
                             <td>`+ constraintsArray[i].location + `</td>
                             <td style=" max-width: 130px;">`+ constraintsStartTime + ' - ' + constraintsEndTime + `</td>
                             <td style=" max-width: 300px;">`+ detail + `</td>
                             <td style=" max-width: 300px;">`+ remark + `</td>
                        </tr>`);
                    }
                }
                for (var i = 0; i < res.adpMeasures.length; i++) {
                    if (res.adpMeasures[i].location != null) {
                        const constraintsStartTime = formatDateTime(res.adpMeasures[i].startTime);
                        const constraintsEndTime = formatDateTime(res.adpMeasures[i].endTime);
                        //const measureType = res.adpMeasures[i].measureType == null ? " - " : res.adpMeasures[i].measureType;
                        const detail = res.adpMeasures[i].detail == null ? " - " : res.adpMeasures[i].detail;
                        $('#tblTodayADPMeasure').append(` <tr  style="line-break:anywhere">
                             <td>`+ res.adpMeasures[i].location + `</td>
                             <td style=" max-width: 130px;">`+ constraintsStartTime + ' - ' + constraintsEndTime + `</td>
                             
                             <td >`+ detail + `</td>
                        </tr>`);
                        // <td style=" max-width: 300px;">`+ measureType + `</td>
                    }
                }
                for (var i = 0; i < possibleArray.length; i++) {
                    if (possibleArray[i].location != null) {
                        const constraintsStartTime = formatDateTime(possibleArray[i].startTime);
                        const constraintsEndTime = formatDateTime(possibleArray[i].endTime);
                        const detail = possibleArray[i].detail == null ? " - " : possibleArray[i].detail;
                        const remark = possibleArray[i].remark == null ? " - " : possibleArray[i].remark;

                        $('#tblTodayADPPossible').append(` <tr>
                            <td>`+ possibleArray[i].location + `</td>
                            <td style=" max-width: 130px;">`+ constraintsStartTime + ' - ' + constraintsEndTime + `</td>
                            <td style=" max-width: 300px;">`+ detail + `</td>
                            <td style=" max-width: 300px;">`+ remark + `</td>
                        </tr>`);
                    }
                }

                if (res.weatherBriefingFileId != null) {
                    var weatherBriefingFilePath = res.weatherBriefingFileId.split(',');
                    weatherBriefingFilePath.forEach(element =>
                        $('.adp-current').find('.WeatherImage').append('<a href="#close" class="pop" onclick="openModalImage(' + element + ');"  style="width:200px; height:200px;display:inline-block;"><img src = "' + element + '" style="display: block; margin-left: auto; margin-right: auto; width: 200px; max-height:200px"/></a>'
                        ));

                }
                if (res.airspaceStatusBriefingFileId != null) {
                    var airspaceStatusBriefingFilePath = res.airspaceStatusBriefingFileId.split(',');
                    airspaceStatusBriefingFilePath.forEach(element =>

                        //$('.adp-current').find('.AirspaceImage').append('<span class="image-wrap" style="position:relative; display:inline-block; background:url(' + element + ') no-repeat center center; width: 200px; height: 200px;" />'));
                        $('.adp-current').find('.AirspaceImage').append('<a href="#close" class="pop" onclick="openModalImage(' + element + ');" style="width:200px; height:200px;display:inline-block;"><img src = "' + element + '" style="display: block; margin-left: auto; margin-right: auto; width: 200px;  max-height:200px"/></a>'
                        ));
                }
            }
            else {
                $("#edit-today-modal-btn").hide();
                $("#downloadTodayPdf").hide();
            }

        },
        error: function (res) {
            //console.log(res);
        }
    });
}

function GetPreviousADP() {
    $('.adp-previous').find('.WeatherImage').html('');
    $('.adp-previous').find('.AirspaceImage').html('');

    $.ajax({
        type: 'GET',
        url: '/Adp/GetPreviousAdp',
        dataType: 'json',
        success: function (res) {
            PreviousADP = res
            if (res != undefined) {
                $("#btnDownloadPerviousPdf").show();
                var issueTime = new Date(res.issueTime);
                var displayIssueTime = issueTime.getDate() + ' ' + month[issueTime.getMonth()] + ' ' + issueTime.getFullYear() + ' ' + (issueTime.getHours() < 10 ? '0' : '') + issueTime.getHours() + (issueTime.getMinutes() < 10 ? '0' : '') + issueTime.getMinutes();
                var possibleArray = res.adpConstraints.filter(c => c.isConfirmed == 0)
                var constraintsArray = res.adpConstraints.filter(c => c.isConfirmed == 1)

                $('#PreviousAdpRevision').html(res.revision);
                $('#PreviousIssueTime').html(displayIssueTime + '(UTC)');
                $('#PreviousOtherInfo').html(res.otherInformation);
                $('#PreviousWeatherBriefing').html(res.weatherBriefing);
                $('#PreviousAirspaceBriefing').html(res.airspaceStatusBriefing);
                if (res.username == null) {

                    $('#PreviousCreateBy').html('Created by : -');
                }
                else {
                    $('#PreviousCreateBy').html('Created by : ' + res.username);

                }

                for (var i = 0; i < constraintsArray.length; i++) {
                    const constraintsStartTime = formatDateTime(constraintsArray[i].startTime);
                    const constraintsEndTime = formatDateTime(constraintsArray[i].endTime);
                    const detail = constraintsArray[i].detail == null ? " - " : constraintsArray[i].detail;
                    const remark = constraintsArray[i].remark == null ? " - " : constraintsArray[i].remark;
                    $('#tblPreviousADPConstrains').append(` <tr style="line-break:anywhere">
                            <td>`+ constraintsArray[i].location + `</td>
                            <td style=" max-width: 130px;">`+ constraintsStartTime + ' - ' + constraintsEndTime + `</td>
                            <td style=" max-width: 300px;">`+ detail + `</td>
                            <td style=" max-width: 300px;">`+ remark + `</td>
                        </tr>`);
                }
                for (var i = 0; i < res.adpMeasures.length; i++) {
                    const adpMeasuresStartTime = formatDateTime(res.adpMeasures[i].startTime);
                    const adpMeasuresEndTime = formatDateTime(res.adpMeasures[i].endTime);
                    //const measureType = res.adpMeasures[i].measureType == null ? " - " : res.adpMeasures[i].measureType;
                    const detail = res.adpMeasures[i].detail == null ? " - " : res.adpMeasures[i].detail;
                    $('#tblPreviousADPMeasure').append(` <tr>
                            <td>`+ res.adpMeasures[i].location + `</td>
                            <td style=" max-width: 130px;">`+ adpMeasuresStartTime + ' - ' + adpMeasuresEndTime + `</td>
                            <td style=" max-width: 300px;">`+ detail + `</td>
                        </tr>`);
                    // <td style=" max-width: 300px;">`+ res.adpMeasures[i].measureType + `</td>
                }
                for (var i = 0; i < possibleArray.length; i++) {
                    const possibleStartTime = formatDateTime(possibleArray[i].startTime);
                    const possibleEndTime = formatDateTime(possibleArray[i].endTime);
                    const detail = possibleArray[i].detail == null ? " - " : constraintsArray[i].detail;
                    const remark = possibleArray[i].remark == null ? " - " : constraintsArray[i].remark;
                    $('#tblPreviousADPPossible').append(` <tr>
                            <td>`+ possibleArray[i].location + `</td>
                            <td style=" max-width: 130px;">`+ possibleStartTime + ' - ' + possibleEndTime + `</td>
                            <td style=" max-width: 300px;">`+ detail + `</td>
                            <td style=" max-width: 300px;">`+ remark + `</td>
                        </tr>`);
                }
                if (res.weatherBriefingFileId != null) {
                    var weatherBriefingFilePath = res.weatherBriefingFileId.split(',');
                    weatherBriefingFilePath.forEach(element =>
                        $('.adp-previous').find('.WeatherImage').append('<a href="#close" class="pop" onclick="openModalImage(' + element + ');"  style="width:200px; height:200px;display:inline-block;"><img src = "' + element + '" style="display: block; margin-left: auto; margin-right: auto; width: 200px; max-height:200px"/></a>'
                        ));
                }
                if (res.airspaceStatusBriefingFileId != null) {
                    var airspaceStatusBriefingFilePath = res.airspaceStatusBriefingFileId.split(',');
                    airspaceStatusBriefingFilePath.forEach(element =>

                        //$('.adp-current').find('.AirspaceImage').append('<span class="image-wrap" style="position:relative; display:inline-block; background:url(' + element + ') no-repeat center center; width: 200px; height: 200px;" />'));
                        $('.adp-previous').find('.AirspaceImage').append('<a href="#close" class="pop" onclick="openModalImage(' + element + ');" style="width:200px; height:200px;display:inline-block;"><img src = "' + element + '" style="display: block; margin-left: auto; margin-right: auto; width: 200px; max-height:200px"/></a>'
                        ));
                }
            }
            else {
                $("#btnDownloadPerviousPdf").hide();
            }
        },
        error: function (res) {
            //console.log(res);
            $('#loading-overlay').hide();
        }
    });
}

function GetNextAdp() {
    $('.adp-next').find('.WeatherImage').html('');
    $('.adp-next').find('.AirspaceImage').html('');
    $.ajax({
        type: 'GET',
        url: '/Adp/GetNextAdp',
        dataType: 'json',
        success: function (res) {
            NextADP = res;
            console.log(res)
            if (res != undefined) {
                $("#edit-tmr-modal-btn").show();
                $("#downloadTmrPdf").show();
                var issueTime = new Date(res.issueTime);
                var displayIssueTime = issueTime.getDate() + ' ' + month[issueTime.getMonth()] + ' ' + issueTime.getFullYear() + ' ' + (issueTime.getHours() < 10 ? '0' : '') + issueTime.getHours() + (issueTime.getMinutes() < 10 ? '0' : '') + issueTime.getMinutes();
                var possibleArray = res.adpConstraints.filter(c => c.isConfirmed == 0)
                var constraintsArray = res.adpConstraints.filter(c => c.isConfirmed == 1)
                $('#NextAdpRevision').html(res.revision);
                $('#NextIssueTime').html(displayIssueTime + '(UTC)');
                $('#NextOtherInfo').html(res.otherInformation);
                $('#NextWeatherBriefing').html(res.weatherBriefing);
                $('#NextAirspaceBriefing').html(res.airspaceStatusBriefing);
                if (res.username == null) {
                    $('#NextCreateBy').html('Created by : -');
                }
                else {
                    $('#NextCreateBy').html('Created by : ' + res.username);
                }

                for (var i = 0; i < constraintsArray.length; i++) {
                    if (constraintsArray[i].location != null) {
                        const constraintsStartTime = formatDateTime(constraintsArray[i].startTime);
                        const constraintsEndTime = formatDateTime(constraintsArray[i].endTime);
                        const detail = constraintsArray[i].detail == null ? " - " : constraintsArray[i].detail;
                        const remark = constraintsArray[i].remark == null ? " - " : constraintsArray[i].remark;
                        $('#tblNextADPConstrains').append(` <tr>
                             <td>`+ constraintsArray[i].location + `</td>
                             <td style=" max-width: 130px;">`+ constraintsStartTime + ' - ' + constraintsEndTime + `</td>
                             <td style=" max-width: 300px;">`+ detail + `</td>
                             <td style=" max-width: 300px;">`+ remark + `</td>
                        </tr>`);
                    }
                }
                for (var i = 0; i < res.adpMeasures.length; i++) {
                    if (res.adpMeasures[i].location != null) {
                        const constraintsStartTime = formatDateTime(res.adpMeasures[i].startTime);
                        const constraintsEndTime = formatDateTime(res.adpMeasures[i].endTime);
                        //const measureType = res.adpMeasures[i].measureType == null ? " - " : res.adpMeasures[i].measureType;
                        const detail = res.adpMeasures[i].detail == null ? " - " : res.adpMeasures[i].detail;
                        $('#tblNextADPMeasure').append(` <tr  style="line-break:anywhere">
                             <td>`+ res.adpMeasures[i].location + `</td>
                             <td style=" max-width: 130px;">`+ constraintsStartTime + ' - ' + constraintsEndTime + `</td>
                             <td style=" max-width: 300px;">`+ detail + `</td>
                        </tr>`);
                        // <td style=" max-width: 300px;">`+ measureType + `</td>
                    }
                }
                for (var i = 0; i < possibleArray.length; i++) {
                    if (possibleArray[i].location != null) {
                        const constraintsStartTime = formatDateTime(possibleArray[i].startTime);
                        const constraintsEndTime = formatDateTime(possibleArray[i].endTime);
                        const detail = possibleArray[i].detail == null ? " - " : constraintsArray[i].detail;
                        const remark = possibleArray[i].remark == null ? " - " : constraintsArray[i].remark;

                        $('#tblNextADPPossible').append(` <tr>
                            <td>`+ possibleArray[i].location + `</td>
                            <td style=" max-width: 130px;">`+ constraintsStartTime + ' - ' + constraintsEndTime + `</td>
                            <td style=" max-width: 300px;">`+ detail + `</td>
                            <td style=" max-width: 300px;">`+ remark + `</td>
                        </tr>`);
                    }
                }

                if (res.weatherBriefingFileId != null) {
                    var weatherBriefingFilePath = res.weatherBriefingFileId.split(',');
                    weatherBriefingFilePath.forEach(element =>
                        $('.adp-next').find('.WeatherImage').append('<a href="#close" class="pop" onclick="openModalImage(' + element + ');"  style="width:200px; height:200px;display:inline-block;"><img src = "' + element + '" style="display: block; margin-left: auto; margin-right: auto; width: 200px; max-height:200px"/></a>'
                        ));

                }
                if (res.airspaceStatusBriefingFileId != null) {
                    var airspaceStatusBriefingFilePath = res.airspaceStatusBriefingFileId.split(',');
                    airspaceStatusBriefingFilePath.forEach(element =>

                        //$('.adp-current').find('.AirspaceImage').append('<span class="image-wrap" style="position:relative; display:inline-block; background:url(' + element + ') no-repeat center center; width: 200px; height: 200px;" />'));
                        $('.adp-next').find('.AirspaceImage').append('<a href="#close" class="pop" onclick="openModalImage(' + element + ');" style="width:200px; height:200px;display:inline-block;"><img src = "' + element + '" style="display: block; margin-left: auto; margin-right: auto; width: 200px;  max-height:200px"/></a>'
                        ));
                }
            }
            else {
                $("#edit-tmr-modal-btn").hide();
                $("#downloadTmrPdf").hide();
            }
        },
        error: function (res) {
            //console.log(res);
            $('#loading-overlay').hide();
        }
    });
    $('#loading-overlay').hide();

}

function OnclickCreateandEditAdp() {
    $('.save-adp-btn').on('click', function (e) {
        var $adpModal = $('#adp-modal');
        var constraint_array = [];
        var atfm_array = [];
        WeatherBriefFileArray = []
        AirspaceBriefFileArray = []
        if (ValidateForm()) {
            $("#ConstraintTable tbody tr").each(function (index) {
                if (index > 0) {
                    constraint_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        "detail": $(this).find('td .input-details').val(),
                        "remark": $(this).find('td .input-remarks').val(),
                        "isConfirmed": 1,
                    });
                }
            });

            $("#PossibleTable tbody tr").each(function (index) {
                if (index > 0) {
                    constraint_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        "detail": $(this).find('td .input-details').val(),
                        "remark": $(this).find('td .input-remarks').val(),
                        "isConfirmed": 0,
                    });
                }
            });

            $("#AtfmTable tbody tr").each(function (index) {
                if (index > 0) {
                    atfm_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        //"MeasureType": $(this).find('td .input-type').val(),
                        "detail": $(this).find('td .input-details').val(),
                    });
                }
            });

            $('#formWeatherBrief').find('.removeFileEdit').each(function () {
                WeatherBriefFileArray.push($(this).attr('value'));
            });
            $('#formAirspace').find('.removeFileEdit').each(function () {
                AirspaceBriefFileArray.push($(this).attr('value'));
            });

            var data = {
                validTime: $('#ValidDate').val(),
                weatherBriefing: $('#WeatherBrief').val(),
                airspaceStatusBriefing: $('#AirspaceBrief').val(),
                otherInformation: $('#Info').val(),
                isPublished: false,
                ADPConstraints: constraint_array,
                ADPMeasures: atfm_array,
                WeatherBriefingFileId: WeatherBriefFileArray.toString(),
                AirspaceStatusBriefingFileId: AirspaceBriefFileArray.toString()
            }

            $.ajax({
                type: 'POST',
                url: '/Adp/CreateAndEditADP',
                dataType: 'json',
                data: data,
                success: function (res) {
                    createNewSaveADP(data);
                    clearTableDetail();
                    GetPreviousADP();
                    GetTodayADP();
                    GetNextAdp();
                    data.WeatherBriefingFileId = res.weatherBriefingFilePaths;
                    data.AirspaceStatusBriefingFileId = res.airspaceStatusBriefingFilePaths;
                },
                error: function (res) {
                    //console.log(res);
                },
                complete: function () {
                    $adpModal.modal('hide');
                }
            });
        }
    });

    $('.publish-adp-btn').on('click', async function (e) {
        var $adpModal = $('#adp-modal');
        var constraint_array = [];
        var atfm_array = [];
        WeatherBriefFileArray = []
        AirspaceBriefFileArray = []
        WeatherBriefFilePath = []
        AirspaceBriefFilePath = []
        if (ValidateForm() == true) {
            $("#ConstraintTable tbody tr").each(function (index) {
                if (index > 0) {
                    constraint_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        "detail": $(this).find('td .input-details').val(),
                        "remark": $(this).find('td .input-remarks').val(),
                        "isConfirmed": 1,
                    });
                }
            });

            $("#PossibleTable tbody tr").each(function (index) {
                if (index > 0) {
                    constraint_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        "detail": $(this).find('td .input-details').val(),
                        "remark": $(this).find('td .input-remarks').val(),
                        "isConfirmed": 0,
                    });
                }
            });

            $("#AtfmTable tbody tr").each(function (index) {
                if (index > 0) {
                    atfm_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        //"MeasureType": $(this).find('td .input-type').val(),
                        "detail": $(this).find('td .input-details').val(),
                    });
                }
            });

            $('#formWeatherBrief').find('.removeFileEdit').each(function () {
                WeatherBriefFileArray.push($(this).attr('value'));
            });
            $('#formAirspace').find('.removeFileEdit').each(function () {
                AirspaceBriefFileArray.push($(this).attr('value'));
            });

            $('#formWeatherBrief').find('.filepath').each(function () {
                WeatherBriefFilePath.push($(this).attr('value').slice(2));
            });
            $('#formAirspace').find('.filepath').each(function () {
                AirspaceBriefFilePath.push($(this).attr('value').slice(2));
            });

            var data = {
                validTime: $('#ValidDate').val(),
                weatherBriefing: $('#WeatherBrief').val(),
                airspaceStatusBriefing: $('#AirspaceBrief').val(),
                otherInformation: $('#Info').val(),
                isPublished: true,
                adpConstraints: constraint_array,
                adpMeasures: atfm_array,
                WeatherBriefingFileId: WeatherBriefFileArray.toString(),
                AirspaceStatusBriefingFileId: AirspaceBriefFileArray.toString(),
                weatherBriefingFileId: WeatherBriefFilePath.toString(),
                airspaceStatusBriefingFileId: AirspaceBriefFilePath.toString(),
                weatherBriefingFilePaths: '',
                airspaceStatusBriefingFilePaths: '',
                revision: $("#Revision").text() == '-' ? '1' : $("#Revision").text()
            };

            // Generate the PDF document
            const doc = await generatePDF(data);

            // Convert PDF to data URL for preview
            //const pdfDataUri = doc.output('datauristring');

            //// Show preview modal with generated PDF
            //$('#pdfPreviewIframe').attr('src', pdfDataUri);

            // Convert PDF to *blob* URL for preview
            const pdfBlobUrl = doc.output('bloburl');   // or: URL.createObjectURL(doc.output('blob'))
            $('#pdfPreviewIframe').attr('src', pdfBlobUrl);

            // Clean up when the modal closes
            $('#pdfPreviewModal').one('hidden.bs.modal', () => {
                URL.revokeObjectURL(pdfBlobUrl);
            });

            $('#pdfPreviewModal').modal('show');

            // Handle Confirm Publish button click
            $('#confirmPublishBtn').off('click').on('click', function () {

                // Proceed with the publish action
                $.ajax({
                    type: 'POST',
                    url: '/Adp/CreateAndEditADP',
                    dataType: 'json',
                    data: data,
                    success: function (res) {
                        console.log(data);
                        console.log(res);
                        createNewSaveADP(data);
                        clearTableDetail();
                        GetPreviousADP();
                        GetTodayADP();
                        GetNextAdp();
                        data.weatherBriefingFilePaths = res.weatherBriefingFilePaths;
                        data.airspaceStatusBriefingFilePaths = res.airspaceStatusBriefingFilePaths;
                        data.revision = res.revision;
                        uploadPDFandsendEmail(data);
                    },
                    error: function (res) {
                        //console.log(res);
                    },
                    complete: function () {
                        $adpModal.modal('hide');
                        $('#pdfPreviewModal').modal('hide'); // Hide the PDF preview modal
                    }
                });
            });
        }
    });
}

function ValidateForm() {
    var isValid = true;
    WeatherBriefFileArray = []
    AirspaceBriefFileArray = []
    $("#ConstraintTable tbody tr").each(function (index) {
        if (index > 0) {
            const location = $(this).find('td .input-location').val()
            const startTime = new Date($(this).find('td .startDate').val());
            const endTime = new Date($(this).find('td .endDate').val());

            if ($(this).find('td .startDate').val() == "") {
                $(this).find('td .startDate').prop('required', true);
                $(this).find('td .start-required').css('display', 'block');
                isValid = false;
            }
            if ($(this).find('td .endDate').val() == "") {
                $(this).find('td .endDate').prop('required', true);
                $(this).find('td .end-required').css('display', 'block');
                isValid = false;
            }
            if (startTime > endTime) {
                $(this).find('td .startDate').addClass("input-error");
                $(this).find('td .date-error').css('display', 'block');
                isValid = false;
            }
            if (location == "") {
                $(this).find('td .input-location').prop('required', true);
                $(this).find('td .location-required').css('display', 'block');
                isValid = false;
            }
        }
    });

    $("#PossibleTable tbody tr").each(function (index) {
        if (index > 0) {
            const location = $(this).find('td .input-location').val()
            const startTime = new Date($(this).find('td .startDate').val());
            const endTime = new Date($(this).find('td .endDate').val());

            if ($(this).find('td .startDate').val() == "") {
                $(this).find('td .startDate').prop('required', true);
                $(this).find('td .start-required').css('display', 'block');
                isValid = false;
            }
            if ($(this).find('td .endDate').val() == "") {
                $(this).find('td .endDate').prop('required', true);
                $(this).find('td .end-required').css('display', 'block');
                isValid = false;
            }
            if (startTime > endTime) {
                $(this).find('td .startDate').addClass("input-error");
                $(this).find('td .date-error').css('display', 'block');
                isValid = false;
            }
            if (location == "") {
                $(this).find('td .input-location').prop('required', true);
                $(this).find('td .location-required').css('display', 'block');
                isValid = false;
            }
        }
    });

    $("#AtfmTable tbody tr").each(function (index) {
        if (index > 0) {
            const location = $(this).find('td .input-location').val()
            const startTime = new Date($(this).find('td .startDate').val());
            const endTime = new Date($(this).find('td .endDate').val());

            if ($(this).find('td .startDate').val() == "") {
                $(this).find('td .startDate').prop('required', true);
                $(this).find('td .start-required').css('display', 'block');
                isValid = false;
            }
            if ($(this).find('td .endDate').val() == "") {
                $(this).find('td .endDate').prop('required', true);
                $(this).find('td .end-required').css('display', 'block');
                isValid = false;
            }
            if (startTime > endTime) {
                $(this).find('td .startDate').addClass("input-error");
                $(this).find('td .date-error').css('display', 'block');
                isValid = false;
            }
            if (location == "") {
                $(this).find('td .input-location').prop('required', true);
                $(this).find('td .location-required').css('display', 'block');
                isValid = false;
            }
        }
    });

    if (isValid === false) {
        return false;
    }
    return true;
}

function OnclickOpenModal() {
    var date = new Date();
    $('#edit-today-modal-btn').off('click').on('click', function (e) {
        var tempdate = date.setDate(date.getDate());
        var datetimeNow = new Date(tempdate).setHours(0, 0, 0, 0);
        var todayDate = new Date(datetimeNow);
        var validDateTime = NextADP == null ? todayDate.toISOString() : TodayADP.validTime;
        $('#ValidDate').prop("disabled", true);
        OpenModalADP(validDateTime);
    });

    $('#edit-tmr-modal-btn').off('click').on('click', function (e) {
        var tempdate = date.setDate(date.getDate() + 1);
        var nextDate = new Date(tempdate).setHours(0, 0, 0, 0);
        var newDate = new Date(nextDate);
        var validDateTime = NextADP == null ? newDate.toISOString() : NextADP.validTime;
        $('#ValidDate').prop("disabled", true);
        OpenModalADP(validDateTime);
    });

    $('#create-new-adp-modal-btn').off('click').on('click', function (e) {
        var tempdate = date.setDate(date.getDate());
        var nextDate = new Date(tempdate).setHours(0, 0, 0, 0);
        var newDate = new Date(nextDate);
        var validDateTime = newDate.toISOString();
        $('#ValidDate').prop("disabled", false);
        OpenModalADP(validDateTime);
    });
}

function OpenModalADP(validDateTime) {
    var data = {};
    $("#adp-modal").modal('show');
    $("#adp-modal").on('shown.bs.modal', function (e) {
        WeatherBriefFileArray = [];
        AirspaceBriefFileArray = [];

        default_text = 'Bangkok ATFMU Contact Information\n' +
            'E-Mail: <EMAIL>\n' +
            'Phone: +66 2287 8024 / +66 2287 8025\n' +
            'CTOT View Page: https://atfm.aerothai.aero'

        data = { validDate: validDateTime };
        $.ajax({
            type: 'POST',
            url: '/Adp/GetLastestAdp',
            dataType: 'json',
            data: data,
            success: function (res) {
                if (res != undefined) {
                    $("table.adp-table").each(function () {
                        $(this).find('tbody>tr').not(':first').each(function (index) {
                            $(this).remove();
                        });
                    });
                    //$('.removeDiv').html('');
                    var possibleArray = res.adpConstraints.filter(c => c.isConfirmed == 0);
                    var constraintsArray = res.adpConstraints.filter(c => c.isConfirmed == 1);
                    var tempValidtime = formatDate(res.validTime);
                    var issueTime = formatIssueDate(res.issueTime)

                    var $table = $('#ConstraintTable');

                    for (var i = 0; i < constraintsArray.length; i++) {
                        //console.log(constraintsArray[i]);
                        const tempStartTime = formatDateTime(constraintsArray[i].startTime);
                        const tempEndTime = formatDateTime(constraintsArray[i].endTime);
                        const $trow = $table.find('tbody').children().eq(0).clone().show();
                        $trow.find('.constraint-location').val(constraintsArray[i].location);
                        $trow.find('.constraint-details').val(constraintsArray[i].detail);
                        $trow.find('.constraint-remarks').val(constraintsArray[i].remark);
                        $trow.find('.startDate').val(tempStartTime);
                        $trow.find('.endDate').val(tempEndTime);
                        $trow.find('.input-datetime').datetimepicker(e);
                        $table.find('tbody').append($trow);
                        RemoveRowEvent();
                    }

                    $table = $('#AtfmTable');
                    for (var i = 0; i < res.adpMeasures.length; i++) {
                        tempStartTime = formatDateTime(res.adpMeasures[i].startTime);
                        tempEndTime = formatDateTime(res.adpMeasures[i].endTime);
                        $trow = $table.find('tbody').children().eq(0).clone().show();
                        $trow.find('.atfm-location').val(res.adpMeasures[i].location);
                        $trow.find('.atfm-details').val(res.adpMeasures[i].detail);
                        $trow.find('.startDate').val(tempStartTime);
                        $trow.find('.endDate').val(tempEndTime);
                        $trow.find('.input-datetime').datetimepicker(e);
                        $table.find('tbody').append($trow);
                        RemoveRowEvent();
                    }

                    $table = $('#PossibleTable');
                    for (var i = 0; i < possibleArray.length; i++) {
                        tempStartTime = formatDateTime(possibleArray[i].startTime);
                        tempEndTime = formatDateTime(possibleArray[i].endTime);
                        $trow = $table.find('tbody').children().eq(0).clone().show();
                        $trow.find('.possible-location').val(possibleArray[i].location);
                        $trow.find('.possible-details').val(possibleArray[i].detail);
                        $trow.find('.possible-remarks').val(possibleArray[i].remark);
                        $trow.find('.startDate').val(tempStartTime);
                        $trow.find('.endDate').val(tempEndTime);
                        $trow.find('.input-datetime').datetimepicker(e);
                        $table.find('tbody').append($trow);
                        RemoveRowEvent();
                    }

                    $("#IssueDate").text(issueTime);
                    $("#ValidDate").val(tempValidtime);
                    $("#Revision").text(res.revision);
                    $(".adpModal").find('#WeatherBrief').val(res.weatherBriefing);
                    $(".adpModal").find('#AirspaceBrief').val(res.airspaceStatusBriefing);
                    $(".adpModal").find('#Info').val(res.otherInformation);
                    $(".adp-table").find('.input-datetime').datetimepicker(e);
                    $('.input-datetime').datetimepicker({
                        format: 'YYYY-MM-DD HH:mm',
                        formatTime: 'HH:mm',
                        formatDate: 'YYYY-MM-DD',
                        minDate: 0,
                    });
                    if (res.weatherBriefingFileUploads != null) {
                        for (var i = 0; i < res.weatherBriefingFileUploads.length; i++) {
                            var file = "<div class='card card-file card-file-" + res.weatherBriefingFileUploads[i].id + "'><div class='card-body' style='padding: 0.8em;'><i class='fas fa-paperclip' style='margin-right: 1em;'></i>" + res.weatherBriefingFileUploads[i].filename;
                            file += "<span class='text-small' style='margin-left:2em;'>" + bytesToSize(res.weatherBriefingFileUploads[i].size) + "</span>";
                            file += "<div class='filepath' value='" + res.weatherBriefingFileUploads[i].filepath + "'  style='display:none;'><i class='fas fa-times' ></i></div>";
                            file += "<div class='btn btn-outline-danger btn-xs removeFileEdit' value='" + res.weatherBriefingFileUploads[i].id + "' style='float:right;'><i class='fas fa-times' ></i></div>" + "</div></div>";
                            $('#formWeatherBrief').find('.removeDiv').append(file);
                            WeatherBriefFileArray.push(res.weatherBriefingFileUploads[i].id);
                        }
                    }
                    if (res.airspaceFileUploads != null) {
                        for (var i = 0; i < res.airspaceFileUploads.length; i++) {
                            var file = "<div class='card card-file card-file-" + res.airspaceFileUploads[i].id + "'><div class='card-body' style='padding: 0.8em;'><i class='fas fa-paperclip' style='margin-right: 1em;'></i>" + res.airspaceFileUploads[i].filename;
                            file += "<span class='text-small' style='margin-left:2em;'>" + bytesToSize(res.airspaceFileUploads[i].size) + "</span>";
                            file += "<div class='filepath' value='" + res.airspaceFileUploads[i].filepath + "'  style='display:none;'><i class='fas fa-times' ></i></div>";
                            file += "<div class='btn btn-outline-danger btn-xs removeFileEdit' value='" + res.airspaceFileUploads[i].id + "' style='float:right;'><i class='fas fa-times' ></i></div>" + "</div></div>";
                            $('#formAirspace').find('.removeDiv').append(file);
                            AirspaceBriefFileArray.push(res.airspaceFileUploads[i].id);
                        }
                    }
                }
                else {
                    $("table.adp-table").each(function () {
                        $(this).find("tbody > tr").not(":first").remove();
                        const $trow = $(this).find("tbody").children().eq(0).clone().show();
                        // Switch to type="text" for startDate/endDate:
                        $trow.find(".startDate").attr("type", "text");
                        $trow.find(".endDate").attr("type", "text");
                        $trow.find(".input-datetime").datetimepicker(e);
                        $(this).find("tbody").append($trow);
                    });

                    //CLEAR old images, attachments:
                    $(".WeatherImage").html("");
                    $(".AirspaceImage").html("");
                    $("#formWeatherBrief .removeDiv").html("");
                    $("#formAirspace .removeDiv").html("");
                    WeatherBriefFileArray = [];
                    AirspaceBriefFileArray = [];

                    // 2) Clear textareas, attachments, etc.
                    $("#WeatherBrief").val("");
                    $("#AirspaceBrief").val("");
                    $("#IssueDate").text("");
                    $("#Info").val(default_text);
                    $("#Revision").text("-");
                    $("#airspaceFiles").val("");
                    $("#weatherFiles").val("");
                    $(".removeDiv").html("");
                    WeatherBriefFileArray = [];
                    AirspaceBriefFileArray = [];

                    // 3) IMPORTANT: Re-bind your events so + and - buttons still work
                    AddRowEvent();
                    RemoveRowEvent();

                    // 4) Also re-initialize the date-time pickers site-wide if needed
                    $(".input-datetime").datetimepicker({
                        format: "Y-m-d H:i", // if xdan plugin
                        step: 30
                    });
                }
            },
            error: function (res) {
                //console.log(res);
            }
        });
        $(this).off('shown.bs.modal');
    });
}

function createNewSaveADP(adpData) {
    var data = adpData
    data.isPublished = false;
    $.ajax({
        type: 'POST',
        url: '/Adp/CreateAndEditADP',
        dataType: 'json',
        data: data,
        success: function (res) {
            ////console.log(res);
        },
        error: function (res) {
            //console.log(res);
        },
    });
}

function CloseModal() {
    $("#adp-modal").on('hidden.bs.modal', function (e) {
        $('.adpModal form')[0].reset();
        $("table.adp-table").each(function () {
            $(this).find('tbody>tr').not(':first').each(function (index) {
                //if (index > 0) {
                $(this).remove();
                //}
            });
        });
        $('.removeDiv').html('');
        $('.adpModal').find('.removeDiv').html('');
        $('#airspaceFiles').val('');
        $('#weatherFiles').val('');
        WeatherBriefFileArray = [];
        AirspaceBriefFileArray = [];
        WeatherBriefFilePath4Preview = [];
        AirspaceBriefFilePath4Preview = [];
    });
}

function OnclickDownloadPdf() {

    $('#downloadTodayPdf').on('click', function (e) {
        $('#loading-overlay').show();
        DownloadPDF(TodayADP)
    });

    $('#btnDownloadPerviousPdf').on('click', function (e) {
        $('#loading-overlay').show();
        DownloadPDF(PreviousADP)
    });

    $('#downloadTmrPdf').on('click', function (e) {
        $('#loading-overlay').show();
        DownloadPDF(NextADP)
    });
}

function DownloadPDF(adp) {
    const pdf = generatePDF(adp)
    var issueTime = new Date(adp.validTime);
    var displayIssueTime = issueTime.getDate() + ' ' + month[issueTime.getMonth()] + ' ' + issueTime.getFullYear() + ',' + ' ' + (issueTime.getHours() < 10 ? '0' : '') + issueTime.getHours() + (issueTime.getMinutes() < 10 ? '0' : '') + issueTime.getMinutes() + ' UTC';
    pdf.save('ADP_VTBB' + displayIssueTime + '_' + adp.revision);
    $('#loading-overlay').hide();

}

function OnclickPreviewPDF() {

    $('.preview-pdf-btn').on('click', function (e) {
        $('#loading-overlay').show();

        var constraint_array = [];
        var atfm_array = [];
        WeatherBriefFilePath4Preview = [];
        AirspaceBriefFilePath4Preview = [];

        $("#ConstraintTable tbody tr").each(function (index) {
            if (index > 0) {
                if ($(this).find('td .input-location').val() != "") {
                    constraint_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        "detail": $(this).find('td .input-details').val() == "" ? '-' : $(this).find('td .input-details').val(),
                        "remark": $(this).find('td .input-remarks').val() == "" ? '-' : $(this).find('td .input-remarks').val(),
                        "isConfirmed": 1,
                    });
                }
            }
        });

        $("#PossibleTable tbody tr").each(function (index) {
            if (index > 0) {
                if ($(this).find('td .input-location').val() != "") {
                    constraint_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        "detail": $(this).find('td .input-details').val() == "" ? '-' : $(this).find('td .input-details').val(),
                        "remark": $(this).find('td .input-remarks').val() == "" ? '-' : $(this).find('td .input-remarks').val(),
                        "isConfirmed": 0,
                    });
                }
            }
        });

        $("#AtfmTable tbody tr").each(function (index) {
            if (index > 0) {
                if ($(this).find('td .input-location').val() != "") {
                    atfm_array.push({
                        "location": $(this).find('td .input-location').val().toUpperCase(),
                        "startTime": $(this).find('td .startDate').val(),
                        "endTime": $(this).find('td .endDate').val(),
                        //"MeasureType": $(this).find('td .input-type').val(),
                        "detail": $(this).find('td .input-details').val() == "" ? '-' : $(this).find('td .input-details').val(),
                    });
                }
            }
        });

        $('#formWeatherBrief').find('.filepath').each(function () {
            WeatherBriefFilePath4Preview.push($(this).attr('value').slice(2));
        });

        $('#formAirspace').find('.filepath').each(function () {
            AirspaceBriefFilePath4Preview.push($(this).attr('value').slice(2));
        });

        var data = {
            validTime: $('#ValidDate').val(),
            weatherBriefing: $('#WeatherBrief').val(),
            airspaceStatusBriefing: $('#AirspaceBrief').val(),
            otherInformation: $('#Info').val(),
            revision: $("#Revision").text() == '-' ? '1' : $("#Revision").text(),
            isPublished: true,
            adpConstraints: constraint_array,
            adpMeasures: atfm_array,
            weatherBriefingFileId: WeatherBriefFilePath4Preview.toString(),
            airspaceStatusBriefingFileId: AirspaceBriefFilePath4Preview.toString()
        }
        console.log('ADP: ' + data)
        PreviewPDF(data)
    });
}

async function PreviewPDF(adp) {

    const doc = await generatePDF(adp);

    window.open(doc.output('bloburl'), '_blank', "toolbar=no,status=no,menubar=no,scrollbars=no,resizable=no,modal=yes");
    // this opens a new popup, after this the PDF opens the print window view
    //doc.save('ADP_VTBB' + displayIssueTime + '_' + adp.revision);
    $('#loading-overlay').hide();

}

const dtpOptions = {
    format: "Y-m-d H:i",   // correct tokens for xdSoft picker
    step: 30,
    timepicker: true,
    datepicker: true,
    minDate: 0              // today forward
};

function AddRowEvent() {
    // Avoid double-binding
    $(".add-btn").off("click").on("click", function (e) {
        e.preventDefault();

        const $table = $(this).next("table");

        // 1) Clone the hidden template row
        const $trow = $table.find("tbody tr:first").clone().show();

        // 2) Inputs must be type="text" for the picker
        $trow.find(".startDate, .endDate").attr("type", "text");

        // 3) Use the date selected in #ValidDate (fallback = today)
        const baseDate = getSelectedDate();            // helper we added earlier
        $trow.find(".startDate, .endDate").val(`${baseDate} 00:00`);

        // 4) Attach the picker using the shared options
        $trow.find(".input-datetime").datetimepicker(dtpOptions);

        // 5) Inject the row and re-bind the trash-can ( “–” ) button
        $table.find("tbody").append($trow);
        RemoveRowEvent();
    });
}

function OnSelectDatepicker() {

    const default_text = 'Bangkok ATFMU Contact Information\n' +
        'E-Mail: <EMAIL>\n' +
        'Phone: +66 2287 8024 / +66 2287 8025\n' +
        'CTOT View Page: https://atfm.aerothai.aero';

    $("#ValidDate").off("change").on("change", function () {

        const isoDate = $(this).val();              // e.g. 2025-06-08
        if (!isoDate) return;

        // 1️⃣  Reset basic UI bits
        $(".adpModal .removeDiv").html("");
        $(".WeatherImage, .AirspaceImage").html("");
        WeatherBriefFileArray = [];
        AirspaceBriefFileArray = [];
        WeatherBriefFilePath4Preview = [];
        AirspaceBriefFilePath4Preview = [];

        // Set every existing start/end box to the new date but KEEP its time
        $(".input-datetime").each(function () {
            const oldVal = $(this).val();            // e.g. 2025-06-04 09:30
            const hhmm = oldVal ? oldVal.substr(11, 5) : "00:00";
            $(this).val(`${isoDate} ${hhmm}`);
        });

        // 2️⃣  Ask server for the latest ADP of that date
        $.ajax({
            type: 'POST',
            url: '/Adp/GetLastestAdp',
            dataType: 'json',
            data: { validDate: isoDate },
            success: function (res) {

                // ——--- TABLE & FORM RESET ---— //
                $("table.adp-table").each(function () {
                    $(this).find("tbody>tr").not(":first").remove();
                });

                let issueTimeUTC;
                if (res) {
                    // ------------------------------------------------------------------
                    // Populate tables from returned data …
                    // ------------------------------------------------------------------
                    const possibleArray = res.adpConstraints.filter(c => c.isConfirmed == 0);
                    const constraintsArray = res.adpConstraints.filter(c => c.isConfirmed == 1);
                    const measuresArray = res.adpMeasures;

                    fillConstraintTable(constraintsArray);
                    fillAtfmTable(measuresArray);
                    fillPossibleTable(possibleArray);

                    // text areas & misc
                    $("#WeatherBrief").val(res.weatherBriefing);
                    $("#AirspaceBrief").val(res.airspaceStatusBriefing);
                    $("#Info").val(res.otherInformation);
                    $("#Revision").text(res.revision);
                    issueTimeUTC = formatIssueDate(res.issueTime);

                    // attachments
                    rebuildAttachments(res);
                } else {
                    // ------------------------------------------------------------------
                    // No ADP found – start with empty template row
                    // ------------------------------------------------------------------
                    addOneBlankRowPerTable(isoDate);
                    $("#WeatherBrief, #AirspaceBrief").val("");
                    $("#Info").val(default_text);
                    $("#Revision").text("-");
                    issueTimeUTC = formatIssueDate(new Date());
                }

                // common updates for both branches
                $("#IssueDate").text(issueTimeUTC);

                // Re-initialise every visible picker with the **same options**
                $(".input-datetime").datetimepicker(dtpOptions);
            },
            error: function (xhr) {
                console.error("Failed loading ADP for", isoDate, xhr);
            }
        });
    });
}

/* ---------- helper pieces only used above ---------- */

function fillConstraintTable(arr) {
    const $tbl = $("#ConstraintTable");
    arr.forEach(item => {
        const $row = $tbl.find("tbody tr:first").clone().show();

        $row.find(".constraint-location").val(item.location);
        $row.find(".constraint-details").val(item.detail);
        $row.find(".constraint-remarks").val(item.remark);
        $row.find(".startDate").val(forceTodayWithOriginalTime(item.startTime));
        $row.find(".endDate").val(forceTodayWithOriginalTime(item.endTime));

        $row.find(".input-datetime").datetimepicker(dtpOptions);
        $tbl.find("tbody").append($row);
        RemoveRowEvent();
    });
}

function fillAtfmTable(arr) {
    const $tbl = $("#AtfmTable");
    arr.forEach(item => {
        const $row = $tbl.find("tbody tr:first").clone().show();

        $row.find(".atfm-location").val(item.location);
        $row.find(".atfm-details").val(item.detail);
        $row.find(".startDate").val(forceTodayWithOriginalTime(item.startTime));
        $row.find(".endDate").val(forceTodayWithOriginalTime(item.endTime));

        $row.find(".input-datetime").datetimepicker(dtpOptions);
        $tbl.find("tbody").append($row);
        RemoveRowEvent();
    });
}

function fillPossibleTable(arr) {
    const $tbl = $("#PossibleTable");
    arr.forEach(item => {
        const $row = $tbl.find("tbody tr:first").clone().show();

        $row.find(".possible-location").val(item.location);
        $row.find(".possible-details").val(item.detail);
        $row.find(".possible-remarks").val(item.remark);
        $row.find(".startDate").val(forceTodayWithOriginalTime(item.startTime));
        $row.find(".endDate").val(forceTodayWithOriginalTime(item.endTime));

        $row.find(".input-datetime").datetimepicker(dtpOptions);
        $tbl.find("tbody").append($row);
        RemoveRowEvent();
    });
}

function addOneBlankRowPerTable(baseDate) {   // <- add param
    ["#ConstraintTable", "#AtfmTable", "#PossibleTable"].forEach(sel => {
        const $tbl = $(sel);
        const $row = $tbl.find("tbody tr:first").clone().show();

        $row.find(".startDate, .endDate")
            .val(`${baseDate} 00:00`)
            .datetimepicker(dtpOptions);

        $tbl.find("tbody").append($row);
        RemoveRowEvent();
    });
}

function rebuildAttachments(res) {
    // clear areas first
    $("#formWeatherBrief .removeDiv, #formAirspace .removeDiv").empty();
    WeatherBriefFileArray = [];
    AirspaceBriefFileArray = [];

    (res.weatherBriefingFileUploads || []).forEach(f => {
        $("#formWeatherBrief .removeDiv").append(buildFileCard(f));
        WeatherBriefFileArray.push(f.id);
    });
    (res.airspaceFileUploads || []).forEach(f => {
        $("#formAirspace .removeDiv").append(buildFileCard(f));
        AirspaceBriefFileArray.push(f.id);
    });
}

function buildFileCard(fileObj) {
    return `
      <div class="card card-file card-file-${fileObj.id}">
        <div class="card-body" style="padding:0.8em;">
          <i class="fas fa-paperclip" style="margin-right:1em;"></i>${fileObj.filename}
          <span class="text-small" style="margin-left:2em;">${bytesToSize(fileObj.size)}</span>
          <div class="filepath" value="${fileObj.filepath}" style="display:none;"><i class="fas fa-times"></i></div>
          <div class="btn btn-outline-danger btn-xs removeFileEdit" value="${fileObj.id}" style="float:right;"><i class="fas fa-times"></i></div>
        </div>
      </div>`;
}

function OnTableInputChange() {
    $("#ConstraintTable").on('change', 'input', function () {

        $(this).parent().find('.location-required').css('display', 'none');

        if ($(this).attr("class").includes('startDate')) {
            $(this).parent().find('.startDate').removeClass("input-error");
            $(this).parent().find('.date-error').css('display', 'none');
            $(this).parent().find('.start-required').css('display', 'none');
        }
        if ($(this).attr("class").includes('endDate')) {
            $(this).parent().find('.startDate').removeClass("input-error");
            $(this).parent().find('.date-error').css('display', 'none');
            $(this).parent().find('.end-required').css('display', 'none');
        }
    });

    $("#AtfmTable").on('change', 'input', function () {

        $(this).parent().find('.location-required').css('display', 'none');

        if ($(this).attr("class").includes('startDate')) {
            $(this).parent().find('.startDate').removeClass("input-error");
            $(this).parent().find('.date-error').css('display', 'none');
            $(this).parent().find('.start-required').css('display', 'none');
        }
        if ($(this).attr("class").includes('endDate')) {
            $(this).parent().find('.startDate').removeClass("input-error");
            $(this).parent().find('.date-error').css('display', 'none');
            $(this).parent().find('.end-required').css('display', 'none');
        }
    });

    $("#PossibleTable").on('change', 'input', function () {

        $(this).parent().find('.location-required').css('display', 'none');

        if ($(this).attr("class").includes('startDate')) {
            $(this).parent().find('.startDate').removeClass("input-error");
            $(this).parent().find('.date-error').css('display', 'none');
            $(this).parent().find('.start-required').css('display', 'none');
        }
        if ($(this).attr("class").includes('endDate')) {
            $(this).parent().find('.startDate').removeClass("input-error");
            $(this).parent().find('.date-error').css('display', 'none');
            $(this).parent().find('.end-required').css('display', 'none');
        }
    });
}

function clearTableDetail() {
    $(".table-detail tr").remove();
}

function RemoveRowEvent() {
    $('.td-remove').on('click', 'div', function (e) {
        $(this).parent().parent().remove();
    });
}

function formatDate(datetime) {
    return datetime.substr(0, 10);
}

function formatDateTime(datetime) {
    if (datetime != undefined) {
        return datetime.substr(0, 10) + ' ' + datetime.substr(11, 5);
    }
    else { return ''; }
}

function formatADPDateTime(datetime) {
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    const months = ['JAN', 'FEB', 'MAR', 'APR', 'MAY', 'JUN', 'JUL', 'AUG', 'SEP', 'OCT', 'NOV', 'DEC'];

    const day = datetime.getDate().toString().padStart(2, '0');
    const month = months[datetime.getMonth()];
    const year = datetime.getFullYear();
    const hours = datetime.getHours().toString().padStart(2, '0');
    const minutes = datetime.getMinutes().toString().padStart(2, '0');

    return `${day} ${month} ${year}\n${hours}${minutes}`;
}

function formatIssueDate(datetime) {
    if (datetime != undefined) {
        var tempDate = new Date(datetime);
        // Using UTC methods instead of local time methods
        const formatIssueDate = tempDate.getUTCDate() + ' ' +
            month[tempDate.getUTCMonth()] + ' ' +
            tempDate.getUTCFullYear().toString().substr(-2) + ' ' +
            (tempDate.getUTCHours() < 10 ? '0' : '') + tempDate.getUTCHours() +
            ':' + // Added missing colon between hours and minutes
            (tempDate.getUTCMinutes() < 10 ? '0' : '') + tempDate.getUTCMinutes();
        return formatIssueDate;
    }
    else { return ''; }
}

async function uploadPDFandsendEmail(adpData) {
    ////console.log('uploadPDFandsendEmail');

    const doc = await generatePDF(adpData);
    //window.open(blobUrl); //// OPEN PDF ON NEW TAB  
    var binary = doc.output();
    var reqData = btoa(binary);

    //var adpDate = Date(adpData.validTime);
    var adpDate = adpData.validTime.split('-');
    const adpValidDate = adpDate[0] + adpDate[1] + adpDate[2]
    ////console.log(adpDate);
    $.ajax({
        url: '/Adp/UploadAdpPDFToServerAndSendEmail',
        data: { data: reqData, adpDate: adpValidDate, revision: adpData.revision },
        dataType: "json",
        type: "POST",
        success: function (res) {
            //console.log(res)
        },
        error: function (res) {
            //console.log(res)
        },
        complete: function () {

        }
    });
}

async function generatePDF(adp) {
    const doc = new jsPDF('p', 'pt', 'a4');
    doc.setFont('helvetica');
    doc.setFontSize(14);
    doc.setFontType('bold');
    doc.text(210, 60, 'ATFM DAILY PLAN (ADP)');

    var issueTime = formatIssueDate(datetimeNow);
    const parts = issueTime.split(' ');
    const day = parts[0];
    const month = parts[1];
    const year = '20' + parts[2]; // Convert '25' to '2025'
    const time = parts[3].replace(':', ''); // Convert '03:45' to '0345'

    const displayIssueTime = `${day} ${month} ${year} / ${time} UTC`;

    // Table 1: ORIGINATOR, DATE/TIME, VERSION
    doc.autoTable({
        columnStyles: { 0: { halign: 'left', fillColor: [184, 204, 228] } },
        margin: { top: 90, left: 30, right: 30 },
        body: [
            ['ORIGINATOR', 'THAILAND'],
            ['DATE / TIME OF ISSUANCE', displayIssueTime],
            ['VERSION', adp.revision],
        ],
        theme: 'grid',
        styles: { cellPadding: 2, fontSize: 10, textColor: 0, lineColor: [0, 0, 0], lineWidth: 1, setFontType: 'bold' },
    });

    // Table 2: CONSTRAINTS AND IMPACT
    const formattedConstraints = adp.adpConstraints.map(c => {
        const start = formatADPDateTime(new Date(c.startTime));
        const end = formatADPDateTime(new Date(c.endTime));
        return [c.location, start, end, c.detail, c.remark];
    });

    doc.autoTable({
        head: [
            [{
                content: 'CONSTRAINTS AND IMPACT',
                colSpan: 5,
                styles: {
                    halign: 'center',
                    fillColor: [184, 204, 228],
                    fontSize: 10,
                    fontStyle: 'bold'
                }
            }],
            [
                { content: 'LOCATION', rowSpan: 2, styles: { valign: 'middle' } },
                { content: 'APPLICABLE PERIOD (UTC)', colSpan: 2 },
                { content: 'DESCRIPTION', rowSpan: 2, styles: { valign: 'middle' } },
                { content: 'CAPACITY IMPACT', rowSpan: 2, styles: { valign: 'middle' } }
            ],
            [
                'START',
                'END',
                '',
                ''
            ]
        ],
        body: formattedConstraints,
        columnStyles: {
            0: { cellWidth: 70, halign: 'center' },
            1: { cellWidth: 100, halign: 'center' },
            2: { cellWidth: 100, halign: 'center' },
            3: { cellWidth: 130, halign: 'left' },
            4: { cellWidth: 130, halign: 'center' }
        },
        margin: { left: 30, right: 30 },
        headStyles: {
            halign: 'center',
            fillColor: [184, 204, 228],
            fontSize: 10,
            textColor: 0,
            lineWidth: 0.5
        },
        bodyStyles: {
            cellPadding: 4,
            fontSize: 9
        },
        theme: 'grid',
        styles: { cellPadding: 2, fontSize: 10, textColor: 0, lineColor: [0, 0, 0], lineWidth: 1, setFontType: 'bold' },
        didParseCell: (data) => {
            if (data.section === 'head' && data.row.index === 1) {
                data.cell.styles.valign = 'middle';
            }
            if (data.section === 'head') {
                if (data.row.index === 0) {
                    data.cell.styles.fontSize = 12;
                    data.cell.styles.cellPadding = 6;
                } else if (data.row.index === 2) {
                    data.cell.styles.fontSize = 9;
                }
            }
        }
    });

    // Table 3: ATFM MEASURE
    let adpMeasuresBody = adp.adpMeasures.map(m => [
        m.location,
        formatADPDateTime(new Date(m.startTime)),
        formatADPDateTime(new Date(m.endTime)),
        m.detail
    ]);

    doc.autoTable({
        head: [
            [{
                content: 'ATFM MEASURE',
                colSpan: 4,
                styles: {
                    halign: 'center',
                    fillColor: [184, 204, 228],
                    fontSize: 10,
                    fontStyle: 'bold'
                }
            }],
            [
                { content: 'LOCATION', rowSpan: 2, styles: { valign: 'middle' } },
                { content: 'APPLICABLE PERIOD (UTC)', colSpan: 2 },
                { content: 'DESCRIPTION', rowSpan: 2, styles: { valign: 'middle' } }
            ],
            [
                'START',
                'END',
                ''
            ]
        ],
        body: adpMeasuresBody,
        columnStyles: {
            0: { cellWidth: 70, halign: 'center' },
            1: { cellWidth: 100, halign: 'center' },
            2: { cellWidth: 100, halign: 'center' },
            3: { cellWidth: 260, halign: 'left' }
        },
        margin: { left: 30, right: 30 },
        headStyles: {
            halign: 'center',
            fillColor: [184, 204, 228],
            fontSize: 10,
            textColor: 0,
            lineWidth: 0.5
        },
        bodyStyles: {
            cellPadding: 4,
            fontSize: 9
        },
        theme: 'grid',
        styles: {
            cellPadding: 2,
            fontSize: 10,
            textColor: 0,
            lineColor: [0, 0, 0],
            lineWidth: 1,
            setFontType: 'bold'
        },
        didParseCell: (data) => {
            if (data.section === 'head' && data.row.index === 1) {
                data.cell.styles.valign = 'middle';
            }
            if (data.section === 'head') {
                if (data.row.index === 0) {
                    data.cell.styles.fontSize = 12;
                    data.cell.styles.cellPadding = 6;
                } else if (data.row.index === 2) {
                    data.cell.styles.fontSize = 9;
                }
            }
        }
    });

    // Table 4: OTHER INFORMATION
    doc.autoTable({
        head: [['OTHER INFORMATION']],
        body: [[adp.otherInformation]],
        headStyles: { halign: 'center', fillColor: [184, 204, 228], fontSize: 12 },
        bodyStyles: { halign: 'left' },
        theme: 'grid',
        styles: { cellPadding: 2, fontSize: 10, textColor: 0, lineColor: [0, 0, 0], lineWidth: 1 },
        margin: { left: 30, right: 30 },
    });

    // WEATHER BRIEFING with images
    doc.addPage();
    const weatherText = doc.splitTextToSize(adp.weatherBriefing, doc.internal.pageSize.width - 60);

    // First add the text content
    doc.autoTable({
        head: [['WEATHER BRIEFING']],
        body: [[weatherText]],
        headStyles: { halign: 'center', fillColor: [184, 204, 228], fontSize: 12 },
        bodyStyles: { halign: 'left' },
        styles: { cellPadding: 2, fontSize: 10, textColor: 0, lineColor: [0, 0, 0], lineWidth: 1 },
        margin: { left: 30, right: 30 }
    });

    // Then add images separately
    if (adp.weatherBriefingFileId && adp.weatherBriefingFileId.length > 0) {
        const imagePaths = adp.weatherBriefingFileId.split(',');
        let y = doc.lastAutoTable.finalY + 20; // Position after text

        for (const path of imagePaths) {
            if (!path || path.trim() === '') continue;

            try {
                // Get the full image URL
                const fullPath = path.startsWith('files/')
                    ? window.location.origin + '/' + path
                    : path;

                // Load and process the image
                console.log('🖼️ Trying to load image:', fullPath);
                const imgData = await loadImageForPDF(fullPath);
                if (!imgData) continue;

                // Calculate dimensions
                const maxWidth = doc.internal.pageSize.width - 60;
                const ratio = maxWidth / imgData.width;
                const imgHeight = imgData.height * ratio;

                // Check if we need a new page
                if (y + imgHeight > doc.internal.pageSize.height - 50) {
                    doc.addPage();
                    y = 30; // Reset Y position after new page
                }

                // Add the image to PDF
                console.log(`Adding image at Y: ${y}, Height: ${imgHeight}`);
                doc.addImage(
                    imgData.data,
                    imgData.format,
                    30, // x position
                    y,  // y position
                    maxWidth,
                    imgHeight
                );

                y += imgHeight + 20;
            } catch (e) {
                console.error('Error adding weather image:', e);
            }
        }
    }

    // AIRSPACE STATUS BRIEFING (same pattern as above)
    doc.addPage();
    const airspaceText = doc.splitTextToSize(adp.airspaceStatusBriefing, doc.internal.pageSize.width - 60);

    doc.autoTable({
        head: [['AIRSPACE STATUS BRIEFING']],
        body: [[airspaceText]],
        headStyles: { halign: 'center', fillColor: [184, 204, 228], fontSize: 12 },
        bodyStyles: { halign: 'left' },
        styles: { cellPadding: 2, fontSize: 10, textColor: 0, lineColor: [0, 0, 0], lineWidth: 1 },
        margin: { left: 30, right: 30 }
    });

    if (adp.airspaceStatusBriefingFileId && adp.airspaceStatusBriefingFileId.length > 0) {
        const imagePaths = adp.airspaceStatusBriefingFileId.split(',');
        let y = doc.lastAutoTable.finalY + 20;

        for (const path of imagePaths) {
            if (!path || path.trim() === '') continue;

            try {
                const fullPath = path.startsWith('files/')
                    ? window.location.origin + '/' + path
                    : path;

                const imgData = await loadImageForPDF(fullPath);
                if (!imgData) continue;

                const maxWidth = doc.internal.pageSize.width - 60;
                const ratio = maxWidth / imgData.width;
                const imgHeight = imgData.height * ratio;

                if (y + imgHeight > doc.internal.pageSize.height - 50) {
                    doc.addPage();
                    y = 30;
                }

                console.log(`Adding airspace image at Y: ${y}, Height: ${imgHeight}`);
                doc.addImage(
                    imgData.data,
                    imgData.format,
                    30,
                    y,
                    maxWidth,
                    imgHeight
                );

                y += imgHeight + 20;
            } catch (e) {
                console.error('Error adding airspace image:', e);
            }
        }
    }

    const pageCount = doc.getNumberOfPages();
    for (let i = 1; i <= pageCount; i++) {
        doc.setPage(i);
        doc.setFontSize(10);
        doc.setFont('helvetica', 'normal');

        const pageText = `Page ${i} / ${pageCount}`;
        const pageWidth = doc.internal.pageSize.getWidth();

        // Position top-right (e.g., 30pt margin from right, 20pt from top)
        doc.text(pageText, pageWidth - 30, 20, { align: 'right' });
    }
    return doc;
}

async function loadImageForPDF(url) {
    return new Promise((resolve) => {
        const img = new Image();
        img.crossOrigin = 'Anonymous';

        img.onload = function () {
            const canvas = document.createElement('canvas');
            canvas.width = img.width;
            canvas.height = img.height;

            const ctx = canvas.getContext('2d');
            ctx.fillStyle = '#FFFFFF'; // White background for transparency
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            ctx.drawImage(img, 0, 0);

            // Determine image format
            let format = 'JPEG';
            if (url.toLowerCase().endsWith('.png')) {
                format = 'PNG';
            }

            resolve({
                data: canvas.toDataURL(`image/${format.toLowerCase()}`),
                format: format,
                width: img.width,
                height: img.height
            });
        };

        img.onerror = function () {
            console.error('Error loading image:', url);
            resolve(null);
        };

        img.src = url;
    });
}


function uploadFiles(inputId, formId) {
    var input = document.getElementById(inputId);
    var files = input.files;
    var formData = new FormData();

    var removeClass = formId.search('Edit') == null ? 'removeFile' : 'removeFileEdit'

    WeatherBriefFileArray = [];
    AirspaceBriefFileArray = []; 
    if (files[0].type == 'image/jpeg' || files[0].type == 'image/png') {

        formData.append("files", files[0]);
        $("#PublishBtn").attr("disabled", "disabled");

        $.ajax(
            {
                url: "/Management/UploadFile",
                data: formData,
                processData: false,
                contentType: false,
                type: "POST",
                success: function (result) {
                    //console.log(result)
                    //fileids.push(result.id);
                    if (result.id < 1) {
                        alert('cannot upload this file');
                        return;
                    }

                    $("#PublishBtn").removeAttr("disabled");
                    //console.log("Files Uploaded! ID = " + result.id);
                    $('#' + formId).find("progress").hide();
                    var file = "<div class='card card-file card-file-" + result.id + "'><div class='card-body' style='padding: 0.8em;'><i class='fas fa-paperclip' style='margin-right: 1em;'></i>" + files[0].name;
                    file += "<span class='text-small' style='margin-left:2em;'>" + bytesToSize(result.size) + "</span>";
                    file += "<div class='filepath' value='" + result.filepath + "'  style='display:none;'><i class='fas fa-times' ></i></div>";
                    file += "<div class='btn btn-outline-danger btn-xs " + removeClass + " ' value='" + result.id + "' style='float:right;'><i class='fas fa-times' ></i></div>" + "</div></div>";
                    $('#' + formId).find('.removeDiv').append(file);
                    $('#' + inputId).val('');
                    //$('.removeDiv').append(file);
                    if (formId == 'formWeatherBrief') {
                        //console.log(result)
                        WeatherBriefFileArray.push(result.id.toString())
                    }
                    else if (formId == 'formAirspace') {
                        //console.log(result)
                        AirspaceBriefFileArray.push(result.id.toString())
                    }

                },
                xhr: function () {
                    var fileXhr = $.ajaxSettings.xhr();
                    if (fileXhr.upload) {
                        $('#' + formId).find("progress").show();
                        //$('#removeDiv').append("<div class='card'><div class='card-body' id='displayFile'><i class='fas fa-paperclip' style='margin-right: 1em;'></i>" + files[0].name + "</div></div>");
                        fileXhr.upload.addEventListener("progress", function (e) {
                            if (e.lengthComputable) {
                                $('#' + formId).find(".fileProgress").attr({
                                    value: e.loaded,
                                    max: e.total
                                });
                            }
                        }, false);
                    }
                    return fileXhr;
                }
            }
        );
    }
    else {
        alert('cannot upload this file type');
    }
}

function toDataURL(url, callback) {
    var xhr = new XMLHttpRequest();
    xhr.onload = function () {
        var reader = new FileReader();
        reader.onloadend = function () {
            callback(reader.result);
        }
        reader.readAsDataURL(xhr.response);
    };
    xhr.open('GET', url);
    xhr.responseType = 'blob';
    xhr.send();
}

function bytesToSize(bytes) {
    var sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    if (bytes == 0) return '0 Byte';
    var i = parseInt(Math.floor(Math.log(bytes) / Math.log(1024)));
    return Math.round(bytes / Math.pow(1024, i), 2) + ' ' + sizes[i];
}

function deleteFile(id) {
    $.ajax({
        url: '/Management/DeleteFile/' + id,
        type: "POST",
        success: function (result) {
            console.log(id)
            $("#files").val('');
            $('#fileProgress').hide();
            $('.card-file-' + id).remove();
            const airspaceBriefIndex = AirspaceBriefFileArray.indexOf(id);
            if (airspaceBriefIndex > -1) {
                AirspaceBriefFileArray.splice(airspaceBriefIndex, 1); // 2nd parameter means remove one item only
                //console.log(AirspaceBriefFileArray);
            }
            const weatherBriefIndex = WeatherBriefFileArray.indexOf(id);
            if (weatherBriefIndex > -1) {
                WeatherBriefFileArray.splice(weatherBriefIndex, 1); // 2nd parameter means remove one item only
                //console.log(WeatherBriefFileArray);
            }
        },
        error: function () {
            //console.log('error delete');
        }
    });
}

function openModalImage(imgPath) {
    //console.log(imgPath);
    $('.imagepreview').attr('src', imgPath);
    $('#imagemodal').modal('show');
    var img = new Image()
    img.src = imgPath
    //console.log(img.src)
};

function getDataUri(url, callback) {
    var image = new Image();
    image.setAttribute('crossOrigin', 'anonymous'); //getting images from external domain
    image.onload = function () {
        var canvas = document.createElement('canvas');
        canvas.width = this.naturalWidth;
        canvas.height = this.naturalHeight;

        //next three lines for white background in case png has a transparent background
        var ctx = canvas.getContext('2d');
        ctx.fillStyle = '#fff';  /// set white fill style
        ctx.fillRect(0, 0, canvas.width, canvas.height);
        canvas.getContext('2d').drawImage(this, 0, 0);
        // resolve(canvas.toDataURL('image/jpeg'));
        callback(canvas.toDataURL('image/jpeg'));
    };
    image.src = url;
}

$(".input-datetime").datetimepicker({
    format: "Y-m-d H:i", // xdan plugin format
    step: 30,            // for example
    timepicker: true,
    datepicker: true
});


$(document).ready(function () {
    // 1. Populate dropdown & auto‑load the most recent ADP:
    fillRecentAdpDropdown();

    // 2. When the user picks a date (or clears it):
    $("#adp7DaysDropdown").on("change", function () {
        var dateStr = $(this).val();
        if (dateStr) {
            // Convert "FRI, 4 APR 2025" --> "2025-04-04"
            var isoDate = moment(dateStr, "ddd, D MMM YYYY").format("YYYY-MM-DD");
            loadAdpForDate(isoDate);
        } else {
            clearAdpForm();
        }
    });
});

function fillRecentAdpDropdown() {
    $.ajax({
        url: '/Adp/GetRecent7Days',
        type: 'GET',
        dataType: 'json',
        success: function (dates) {
            var $ddl = $("#adp7DaysDropdown");
            $ddl.empty()
                .append('<option value="">-- Select Recent ADP --</option>');

            // dates is already sorted newest → oldest
            dates.forEach(function (dateStr) {
                $ddl.append(
                    $('<option>').val(dateStr).text(dateStr)
                );
            });

            // Auto‑select & load the newest (first) date, if any
            if (dates.length > 0) {
                $ddl.val(dates[0]).trigger("change");
            }
        },
        error: function (xhr) {
            console.error("Error loading recent ADP dates:", xhr);
        }
    });
}

/**
 * Call your existing POST /Adp/GetLastestAdp to load the ADP data 
 * for a given "YYYY-MM-DD"
 */
function loadAdpForDate(isoDate) {
    $.ajax({
        url: '/Adp/GetLastestAdp',
        type: 'POST',
        dataType: 'json',
        data: { validDate: isoDate },
        success: function (res) {
            if (res) {
                console.log("Loaded ADP for date:", isoDate, res);

                // fill textareas
                $("#WeatherBrief").val(res.weatherBriefing);
                $("#AirspaceBrief").val(res.airspaceStatusBriefing);
                $("#Info").val(res.otherInformation);

                // fill tables & attachments
                populateTablesFromAdp(res);
            } else {
                clearAdpForm();
            }
        },
        error: function (xhr) {
            console.error("Error loading ADP data:", xhr);
        }
    });
}

function populateTablesFromAdp(adpData, e) {

    // 1) Clear out all existing table rows (except the first "template" row)
    $("table.adp-table").each(function () {
        $(this).find("tbody>tr").not(":first").remove();
    });

    $(".WeatherImage").html("");   // Clear weather images in the DOM
    $(".AirspaceImage").html("");  // Clear airspace images in the DOM

    AddRowEvent();
    RemoveRowEvent();

    // 2) Clear existing attachments from the weather/airspace forms
    //    so we can re-populate from adpData.
    $("#formWeatherBrief").find(".removeDiv").html("");
    $("#formAirspace").find(".removeDiv").html("");
    // If you track file IDs in arrays, reset them here:
    WeatherBriefFileArray = [];
    AirspaceBriefFileArray = [];

    // 3) Separate constraints by confirmed vs. unconfirmed
    var constraintsArray = (adpData.adpConstraints || []).filter(c => c.isConfirmed == 1);
    var possibleArray = (adpData.adpConstraints || []).filter(c => c.isConfirmed == 0);
    var measuresArray = adpData.adpMeasures || [];

    // ---------------------------------------------------------
    // 4) Populate "Constraints & Impact" table (#ConstraintTable)
    // ---------------------------------------------------------
    var $constraintTable = $("#ConstraintTable");
    constraintsArray.forEach(function (item) {
        var $trow = $constraintTable.find("tbody tr:first").clone().show();

        var startDT = forceTodayWithOriginalTime(item.startTime);
        var endDT = forceTodayWithOriginalTime(item.endTime);

        $trow.find(".constraint-location").val(item.location);
        $trow.find(".constraint-details").val(item.detail);
        $trow.find(".constraint-remarks").val(item.remark);
        $trow.find(".startDate").val(startDT);
        $trow.find(".endDate").val(endDT);

        // Re-init your date-time picker on these new fields
        $trow.find(".input-datetime").datetimepicker(e);

        $constraintTable.find("tbody").append($trow);
        RemoveRowEvent(); // rebind remove-row button if needed
    });

    // ---------------------------------------------------------
    // 5) Populate "ATFM Measure" table (#AtfmTable)
    // ---------------------------------------------------------
    var $atfmTable = $("#AtfmTable");
    measuresArray.forEach(function (mItem) {
        var $trow = $atfmTable.find("tbody tr:first").clone().show();

        var startDT = forceTodayWithOriginalTime(mItem.startTime);
        var endDT = forceTodayWithOriginalTime(mItem.endTime);

        $trow.find(".atfm-location").val(mItem.location);
        $trow.find(".atfm-details").val(mItem.detail);
        $trow.find(".startDate").val(startDT);
        $trow.find(".startDate").val(startDT);
        $trow.find(".endDate").val(endDT);

        $trow.find(".input-datetime").datetimepicker(e);

        $atfmTable.find("tbody").append($trow);
        RemoveRowEvent();
    });

    // ---------------------------------------------------------
    // 6) Populate "Possible/Developing Issue" table (#PossibleTable)
    // ---------------------------------------------------------
    var $possibleTable = $("#PossibleTable");
    possibleArray.forEach(function (pItem) {
        var $trow = $possibleTable.find("tbody tr:first").clone().show();

        var startDT = forceTodayWithOriginalTime(pItem.startTime);
        var endDT = forceTodayWithOriginalTime(pItem.endTime);

        $trow.find(".possible-location").val(pItem.location);
        $trow.find(".possible-details").val(pItem.detail);
        $trow.find(".possible-remarks").val(pItem.remark);
        $trow.find(".startDate").val(startDT);
        $trow.find(".endDate").val(endDT);

        $trow.find(".input-datetime").datetimepicker(e);

        $possibleTable.find("tbody").append($trow);
        RemoveRowEvent();
    });

    // ---------------------------------------------------------
    // 7) Add Weather Briefing attachments
    // ---------------------------------------------------------
    if (adpData.weatherBriefingFileUploads && adpData.weatherBriefingFileUploads.length > 0) {
        adpData.weatherBriefingFileUploads.forEach(function (fileObj) {
            var fileSize = bytesToSize(fileObj.size); // your existing helper
            var fileCard = `
              <div class="card card-file card-file-${fileObj.id}">
                <div class="card-body" style="padding: 0.8em;">
                  <i class="fas fa-paperclip" style="margin-right: 1em;"></i>${fileObj.filename}
                  <span class="text-small" style="margin-left:2em;">${fileSize}</span>
                  <div class="filepath" value="${fileObj.filepath}" style="display:none;"><i class="fas fa-times"></i></div>
                  <div class="btn btn-outline-danger btn-xs removeFileEdit" value="${fileObj.id}" style="float:right;">
                    <i class="fas fa-times"></i>
                  </div>
                </div>
              </div>`;
            $("#formWeatherBrief").find(".removeDiv").append(fileCard);
            WeatherBriefFileArray.push(fileObj.id);
        });
    }

    // ---------------------------------------------------------
    // 8) Add Airspace Briefing attachments
    // ---------------------------------------------------------
    if (adpData.airspaceFileUploads && adpData.airspaceFileUploads.length > 0) {
        adpData.airspaceFileUploads.forEach(function (fileObj) {
            var fileSize = bytesToSize(fileObj.size);
            var fileCard = `
              <div class="card card-file card-file-${fileObj.id}">
                <div class="card-body" style="padding: 0.8em;">
                  <i class="fas fa-paperclip" style="margin-right: 1em;"></i>${fileObj.filename}
                  <span class="text-small" style="margin-left:2em;">${fileSize}</span>
                  <div class="filepath" value="${fileObj.filepath}" style="display:none;"><i class="fas fa-times"></i></div>
                  <div class="btn btn-outline-danger btn-xs removeFileEdit" value="${fileObj.id}" style="float:right;">
                    <i class="fas fa-times"></i>
                  </div>
                </div>
              </div>`;
            $("#formAirspace").find(".removeDiv").append(fileCard);
            AirspaceBriefFileArray.push(fileObj.id);
        });
    }

    // *** We do NOT overwrite #Revision here, so your current setting remains intact ***
}

function clearAdpForm() {
    // Remove all table rows except the hidden template
    $("table.adp-table").each(function () {
        $(this).find("tbody>tr:not(:first)").remove();
    });
    // Clear images
    $(".WeatherImage").html("");
    $(".AirspaceImage").html("");
    $("#formWeatherBrief .removeDiv").html("");
    $("#formAirspace .removeDiv").html("");
    WeatherBriefFileArray = [];
    AirspaceBriefFileArray = [];

    // Clear text fields
    $("#WeatherBrief").val("");
    $("#AirspaceBrief").val("");
    $("#Info").val("");
    $("#Revision").text("-");
    $("#IssueDate").text("");
    
    // Re-bind row events
    AddRowEvent();
    RemoveRowEvent();
    // Re-init datetime fields
    $(".input-datetime").datetimepicker({
        format: "Y-m-d H:i",
        // or "YYYY-MM-DD HH:mm" if that actually works for your plugin
        // step:30, etc.
    });
}

/** Return the date currently shown in #ValidDate (YYYY-MM-DD),
 *  or today() if empty. */
function getSelectedDate() {
    return $("#ValidDate").val() || new Date().toISOString().substr(0, 10);
}

function forceTodayWithOriginalTime(iso) {
    if (!iso) return "";
    const hhmm = iso.substr(11, 5);          // keep original time
    return `${getSelectedDate()} ${hhmm}`;   // “<ValidDate> HH:mm”
}