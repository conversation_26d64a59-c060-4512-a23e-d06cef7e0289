﻿using ATFAS.Models;
using System;

namespace ATFAS.ViewModels
{
    public class FlightData
    {
        public Flight Flight { get; set; }
        public int FlightSourceId { get; set; }
        public DateTime? TOT { get; set; }
        public DateTime? TO { get; set; }
        public DateTime? INB { get; set; }
        public DateTime? LDT { get; set; }
        public string OBTStr { get; set; }
        public string TOTStr { get; set; }
        public string TOStr { get; set; }
        public string INBStr { get; set; }
        public string OUTBStr { get; set; }
        public string LDTStr { get; set; }
        public string SOBTStr { get; set; }
        public string EOBTStr { get; set; }
        public string COBTStr { get; set; }
        public string AOBTStr { get; set; }
        public string STOTStr { get; set; }
        public string ETOTStr { get; set; }
        public string CTOTStr { get; set; }
        public string ATOTStr { get; set; }
        public string STOStr { get; set; }
        public string ETOStr { get; set; }
        public string CTOStr { get; set; }
        public string ETOByDepStr { get; set; }
        public string ETOBySurStr { get; set; }
        public string ETOByTMCSStr { get; set; }
        public string ATOStr { get; set; }
        public string SINBStr { get; set; }
        public string EINBStr { get; set; }
        public string CINBStr { get; set; }
        public string EINBByDepStr { get; set; }
        public string EINBBySurStr { get; set; }
        public string EINBByTMCSStr { get; set; }
        public string AINBStr { get; set; }
        public string SOUTBStr { get; set; }
        public string EOUTBStr { get; set; }
        public string COUTBStr { get; set; }
        public string EOUTBByDepStr { get; set; }
        public string EOUTBBySurStr { get; set; }
        public string EOUTBByTMCSStr { get; set; }
        public string AOUTBStr { get; set; }
        public string SLDTStr { get; set; }
        public string ELDTStr { get; set; }
        public string CLDTStr { get; set; }
        public string ELDTByDepStr { get; set; }
        public string ELDTBySurStr { get; set; }
        public string ELDTByTMCSStr { get; set; }
        public string ALDTStr { get; set; }
        public bool IsSent { get; set; } //gdp flight
        public string Comment { get; set; } //gdp flight
        public string Username { get; set; } //gdp flight
        public DateTime? NewEobt { get; set; } //gdp flight
        public bool IsGdpExempt { get; set; } //gdp flight
        public bool IsFlightExempt { get; set; } //gdp flight
        public int ATFMDelay { get; set; }
        public bool IsKickoffDelay { get; set; }

        public const string FORMAT_DATETIME_LONG = "yyyy-MM-dd / HH:mm"; 
        public const string FORMAT_DATETIME_SHORT = "dd / HH:mm";
        public const string FORMAT_TIME = "HH:mm";


        public static void AddGroundDelayFlightData(FlightData flightData, int groundDelay)
        {
            string formatDateTime = FORMAT_TIME;
            if (flightData.OBTStr.Length == 14) formatDateTime = FORMAT_DATETIME_SHORT;
            if (flightData.TOT != null)
            {
                flightData.TOT = flightData.TOT.Value.AddMinutes(groundDelay);
                flightData.COBTStr = flightData.Flight.COBT.Value.ToString(formatDateTime);
                flightData.CTOTStr = flightData.Flight.CTOT.Value.ToString(formatDateTime);
                flightData.CLDTStr = flightData.Flight.CLDT.Value.ToString(formatDateTime);
                flightData.OBTStr = flightData.COBTStr + " (GDP)";
                flightData.TOTStr = flightData.CTOTStr + " (GDP)";
                flightData.LDTStr = flightData.CLDTStr + " (GDP)";
            }
            if (flightData.TO != null)
            {
                flightData.TO = flightData.TO.Value.AddMinutes(groundDelay);
                flightData.CTOStr = flightData.TO.Value.ToString(formatDateTime);
                flightData.TOStr = flightData.CTOStr + " (GDP)";
            }
            if (flightData.INB != null)
            {
                flightData.INB = flightData.INB.Value.AddMinutes(groundDelay);
                flightData.CINBStr = flightData.INB.Value.ToString(formatDateTime);
                flightData.INBStr = flightData.CINBStr + " (GDP)";
                string[] EOUTBtimes = flightData.EOUTBStr[^5..].Split(':');
                string[] EINBtimes = flightData.EINBStr[^5..].Split(':');
                int EOUTBMin = (int.Parse(EOUTBtimes[0]) * 60) + int.Parse(EOUTBtimes[1]);
                int EINBMin = (int.Parse(EINBtimes[0]) * 60) + int.Parse(EINBtimes[1]);
                int minDiff = EOUTBMin - EINBMin;
                if (minDiff < 0) minDiff += 1440;
                DateTime COUTB = flightData.INB.Value.AddMinutes(minDiff);
                flightData.COUTBStr = COUTB.ToString(formatDateTime);
                flightData.OUTBStr = flightData.COUTBStr + " (GDP)";
            }
            if (flightData.LDT != null) flightData.LDT = flightData.LDT.Value.AddMinutes(groundDelay);
            flightData.ATFMDelay = groundDelay;
            flightData.FlightSourceId = 3;
        }


        public static FlightData GetFlightData(Trajectory trajectory, Trajectory[] trajectories, int flightSourceId, TrafficDemand trafficDemand, string formatDateTime)
        {
            FlightData flightData = GetFlightData(trajectory.Flight, flightSourceId, trafficDemand, formatDateTime);
            flightData.TO = trajectory.Time;
            flightData.TOStr = trajectory.Time.ToString(formatDateTime);
            flightData.TOStr += flightSourceId == 7 ? " (ENT)" : (flightSourceId == 6 || flightSourceId == 5) ? " (SUR)" : flightSourceId == 4 ? " (DEP)" : flightSourceId == 3 ? " (GDP)" : flightSourceId == 2 ? " (FPL)" : " (SCH)";
            foreach (Trajectory t in trajectories)
            {
                if (t.FlightSourceId == 7) flightData.ATOStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 6) flightData.ETOByTMCSStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 5) flightData.ETOBySurStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 4) flightData.ETOByDepStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 3) flightData.CTOStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 2) flightData.ETOStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 1) flightData.STOStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
            }
            return flightData;
        }

        public static FlightData GetFlightData(Trajectory trajectoryIn, Trajectory trajectoryOut, Trajectory[] trajectoriesIn, Trajectory[] trajectoriesOut, int flightSourceId, TrafficDemand trafficDemand, string formatDateTime)
        {
            FlightData flightData = GetFlightData(trajectoryIn.Flight, flightSourceId, trafficDemand, formatDateTime);
            flightData.INB = trajectoryIn.Time;
            flightData.INBStr = trajectoryIn.Time.ToString(formatDateTime);
            flightData.INBStr += flightSourceId == 7 ? " (ENT)" : (flightSourceId == 6 || flightSourceId == 5) ? " (SUR)" : flightSourceId == 4 ? " (DEP)" : flightSourceId == 3 ? " (GDP)" : flightSourceId == 2 ? " (FPL)" : " (SCH)";
            flightData.OUTBStr = trajectoryOut.Time.ToString(formatDateTime);
            flightData.OUTBStr += flightSourceId == 7 ? " (ENT)" : (flightSourceId == 6 || flightSourceId == 5) ? " (SUR)" : flightSourceId == 4 ? " (DEP)" : flightSourceId == 3 ? " (GDP)" : flightSourceId == 2 ? " (FPL)" : " (SCH)";
            foreach (Trajectory t in trajectoriesIn)
            {
                if (t.FlightSourceId == 7) flightData.AINBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 6) flightData.EINBByTMCSStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 5) flightData.EINBBySurStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 4) flightData.EINBByDepStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 3) flightData.CINBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 2) flightData.EINBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 1) flightData.SINBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
            }
            foreach (Trajectory t in trajectoriesOut)
            {
                if (t.FlightSourceId == 7) flightData.AOUTBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 6) flightData.EOUTBByTMCSStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 5) flightData.EOUTBBySurStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 4) flightData.EOUTBByDepStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 3) flightData.COUTBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 2) flightData.EOUTBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
                else if (t.FlightSourceId == 1) flightData.SOUTBStr = t.Time.ToString(FORMAT_DATETIME_SHORT);
            }
            return flightData;
        }

        public static FlightData GetFlightData(Flight flight, int flightSourceId, TrafficDemand trafficDemand, string formatDateTime)
        {
            FlightData flightData = new FlightData { Flight = flight, FlightSourceId = flightSourceId };
            if (flight.SOBT != null) flightData.SOBTStr = flight.SOBT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.EOBT != null) flightData.EOBTStr = flight.EOBT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.COBT != null) flightData.COBTStr = flight.COBT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.AOBT != null) flightData.AOBTStr = flight.AOBT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.STOT != null) flightData.STOTStr = flight.STOT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.ETOT != null) flightData.ETOTStr = flight.ETOT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.CTOT != null) flightData.CTOTStr = flight.CTOT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.ATOT != null) flightData.ATOTStr = flight.ATOT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.SLDT != null) flightData.SLDTStr = flight.SLDT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.ELDT != null) flightData.ELDTStr = flight.ELDT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.CLDT != null) flightData.CLDTStr = flight.CLDT.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.ELDTByDep != null) flightData.ELDTByDepStr = flight.ELDTByDep.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.ELDTBySur != null) flightData.ELDTBySurStr = flight.ELDTBySur.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.ELDTByTMCS != null) flightData.ELDTByTMCSStr = flight.ELDTByTMCS.Value.ToString(FORMAT_DATETIME_SHORT);
            if (flight.ALDT != null) flightData.ALDTStr = flight.ALDT.Value.ToString(FORMAT_DATETIME_SHORT);
            flightData.OBTStr = ((trafficDemand == null || trafficDemand.IsATFM) && flight.COBT.HasValue) ? flight.COBT.Value.ToString(formatDateTime) + " (GDP)" : ((trafficDemand == null || trafficDemand.IsFPL) && flight.EOBT.HasValue) ? flight.EOBT.Value.ToString(formatDateTime) + " (FPL)" : ((trafficDemand == null || trafficDemand.IsSCH) && flight.SOBT.HasValue) ? flight.SOBT.Value.ToString(formatDateTime) + " (SCH)" : "-";
            if ((trafficDemand == null || trafficDemand.IsATSMSG) && flight.ATOT.HasValue)
            {
                flightData.TOT = flight.ATOT;
                flightData.TOTStr = flight.ATOT.Value.ToString(formatDateTime) + " (DEP)";
            }
            else if ((trafficDemand == null || trafficDemand.IsATFM) && flight.CTOT.HasValue)
            {
                flightData.TOT = flight.CTOT;
                flightData.TOTStr = flight.CTOT.Value.ToString(formatDateTime) + " (GDP)";
            }
            else if ((trafficDemand == null || trafficDemand.IsFPL) && flight.ETOT.HasValue)
            {
                flightData.TOT = flight.ETOT;
                flightData.TOTStr = flight.ETOT.Value.ToString(formatDateTime) + " (FPL)";
            }
            else if ((trafficDemand == null || trafficDemand.IsSCH) && flight.STOT.HasValue)
            {
                flightData.TOT = flight.STOT;
                flightData.TOTStr = flight.STOT.Value.ToString(formatDateTime) + " (SCH)";
            }
            else flightData.TOTStr = "-";
            if ((trafficDemand == null || trafficDemand.IsPassed || (trafficDemand.IsDep && !trafficDemand.IsArr)) && flight.ALDT.HasValue)
            {
                flightData.LDT = flight.ALDT;
                flightData.LDTStr = flight.ALDT.Value.ToString(formatDateTime) + " (ARR)";
            }
            else if ((trafficDemand == null || trafficDemand.IsSUR || (trafficDemand.IsDep && !trafficDemand.IsArr)) && flight.ELDTByTMCS.HasValue)
            {
                flightData.LDT = flight.ELDTByTMCS;
                flightData.LDTStr = flight.ELDTByTMCS.Value.ToString(formatDateTime) + " (SUR)";
            }
            else if ((trafficDemand == null || trafficDemand.IsSUR || (trafficDemand.IsDep && !trafficDemand.IsArr)) && flight.ELDTBySur.HasValue)
            {
                flightData.LDT = flight.ELDTBySur;
                flightData.LDTStr = flight.ELDTBySur.Value.ToString(formatDateTime) + " (SUR)";
            }
            else if ((trafficDemand == null || trafficDemand.IsATSMSG) && flight.ELDTByDep.HasValue)
            {
                flightData.LDT = flight.ELDTByDep;
                flightData.LDTStr = flight.ELDTByDep.Value.ToString(formatDateTime) + " (DEP)";
            }
            else if ((trafficDemand == null || trafficDemand.IsATFM) && flight.CLDT.HasValue)
            {
                flightData.LDT = flight.CLDT;
                flightData.LDTStr = flight.CLDT.Value.ToString(formatDateTime) + " (GDP)";
            }
            else if ((trafficDemand == null || trafficDemand.IsFPL) && flight.ELDT.HasValue)
            {
                flightData.LDT = flight.ELDT;
                flightData.LDTStr = flight.ELDT.Value.ToString(formatDateTime) + " (FPL)";
            }
            else if ((trafficDemand == null || trafficDemand.IsSCH) && flight.SLDT.HasValue)
            {
                flightData.LDT = flight.SLDT;
                flightData.LDTStr = flight.SLDT.Value.ToString(formatDateTime) + " (SCH)";
            }
            else flightData.LDTStr = "-";
            if ((trafficDemand == null || trafficDemand.IsATFM) && flight.CTOT.HasValue) flightData.ATFMDelay = (int)(flight.CTOT - flight.ETOT).Value.TotalMinutes;
            return flightData;
        }
    }
}
