﻿using ATFAS.Models;
using Microsoft.ML.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace ATFAS.ViewModels
{
    public class CTOTTrialModel
    {
        public List<GDPFlight> CtotFlights { get; set; }
        public Boolean IsGdpflight { get; set; }
        public int CtotOptions { get; set; }
        public double CtotTimeout { get; set; }
        public double CtotAirlineBlock { get; set; }
        public double NewCtotBuffer{ get; set; }
        public int GdpId { get; set; }
        public string ErrMsg { get; set; }
        public DateTime? RequestedOBT { get; set; }

    }

  
  
}
