﻿using System;
using ATFAS.Models;

namespace ATFAS.ViewModels
{
    public class CTOTOptimize
    {
        public GDP[] Measures { get; set; }
        public CTOTFlight[] Flights { get; set; }
    }

    public class CTOTOptimizeInput
    {
        public CTOTMeasure[] Measures { get; set; }
        public CTOTAlgorithm Algorithm { get; set; }
        public CTOTFlightInput[] Flights { get; set; }
    }

    public class CTOTMeasure
    {
        public int MeasureId { get; set; }
        public string StartTime { get; set; }
        public string EndTime { get; set; }
        public string EndRecovery { get; set; }
        public int Capacity { get; set; }
        public int CapacityRecovery { get; set; }
        public string TrafficAreaType { get; set; }
        public string TrafficAreaName { get; set; }
    }

    public class CTOTAlgorithm
    {
        public string AlgorithmName { get; set; }
        public CTOTParameters Parameters { get; set; }
    }

    public class CTOTParameters
    {
        public string StartTime { get; set; }
        public string EndTime { get; set; }
        public bool HaveAirborneDelay { get; set; }
        public int HECPeriod { get; set; }
        public float Alpha { get; set; } //ยิ่งน้อยยิ่งกระจายตามไฟล์ทน้อย
        public int FastMaxDelayIndex { get; set; } // Delay/flight ที่รับได้มากที่สุด ถ้าเกินจะถูก
        public int MaxDelayIndex { get; set; } // Delay/flight ที่รับได้มากที่สุด ถ้าเกินจะถูก for Network-based Algorithm
        public int TimeLimit { get; set; } //เป็นค่า Maximum ที่ Branch and Cut ทำงาน ถ้าเกินจะ Terminate
        public int WindowStep { get; set; } // Slicing Window
    }

    public class CTOTFlight
    {
        public int Id { get; set; }
        public int[] MeasureIds { get; set; }
        public string Callsign { get; set; }
        public string Departure { get; set; }
        public string Arrival { get; set; }
        public DateTime OBT { get; set; }
        public DateTime TOT { get; set; }
        public DateTime LDT { get; set; }
        public DateTime IBT { get; set; }
        public DateTime? COBT { get; set; }
        public DateTime? CTOT { get; set; }
        public DateTime? CLDT { get; set; }
        public DateTime? CIBT { get; set; }
        public bool IsManaged { get; set; }
        public bool IsOutRecovery { get; set; }
        public bool IsExclude { get; set; }
        public bool IsExempt { get; set; }
    }

    public class CTOTFlightInput
    {
        public int FlightId { get; set; }
        public bool IsExcluded { get; set; }
        public bool IsExempted { get; set; }
        public bool IsManaged { get; set; }
        public string ArrivalAirport { get; set; }
        public string DepartureAirport { get; set; }        
        public string ETOT { get; set; }
        public string ELDT { get; set; }
        public SectorTrajectory[] SectorTrajectory { get; set; }
        public WaypointTrajectory[] WaypointTrajectory { get; set; }
    }

    public class SectorTrajectory
    {
        public string SectorName { get; set; }
        public string InboundTime { get; set; }
    }

    public class WaypointTrajectory
    {
        public string WaypointName { get; set; }
        public string ETO { get; set; }
    }


    public class CTOTOptimizeOutput
    {
        public CTOTAlgorithm Algorithm { get; set; }
        public CTOTMeasure[] Measures { get; set; }
        public int TotalAirborneDelay { get; set; }
        public int TotalGroundDelay { get; set; }
        public float PreprocessingTime { get; set; }
        public float AirborneExecutionTime { get; set; }
        public float GroundExecutionTime { get; set; }
        public CTOTAirborneDelayedFlight[] AirborneDelayedFlights { get; set; }
        public CTOTGroundDelayedFlight[] GroundDelayedFlights { get; set; }
        public string[] LogMessage { get; set; }
    }

    public class CTOTAirborneDelayedFlight
    {
        public int FlightId { get; set; }
        public int AirborneDelay { get; set; }
    }

    public class CTOTGroundDelayedFlight
    {
        public int FlightId { get; set; }
        public int GroundDelay { get; set; }
        public int[] MeasureIds { get; set; }
    }

    public class CTOTAirport
    {
        public int AirportId { get; set; }
        public string AirportName { get; set; }
        public int DefaultArrivalCapacity { get; set; }
        public int DefaultDepartureCapacity { get; set; }
    }

    public class CTOTAirports
    {
        public CTOTAirport[] Airports { get; set; }
    }
}
