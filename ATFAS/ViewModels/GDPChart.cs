﻿using System.Collections.Generic;
using ATFAS.Models;

namespace ATFAS.ViewModels
{
    public class GDPChart
    {
        public GDP GDP { get; set; }
        public TrafficDemand TrafficDemand { get; set; }
        public string Title { get; set; }
        public string Time { get; set; }
        public string[] Labels { get; set; }
        public string[] Titles { get; set; }
        public int[] DataFPL { get; set; }
        public int[] DataATFM { get; set; }
        public int[] DataATSMSG { get; set; }
        public int[] DataSUR { get; set; }
        public int[] DataPassed { get; set; }
        public int[] DataKickoffDelay { get; set; }
        public List<FlightData>[] FlightLists { get; set; }
        public int[] Capacities { get; set; }
    }
}
