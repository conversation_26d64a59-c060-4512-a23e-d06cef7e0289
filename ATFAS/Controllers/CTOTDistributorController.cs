﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Threading.Tasks;
using ATFAS.Data;
using ATFAS.Models;
using ATFAS.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ATFAS.Controllers;
using ATFAS.ActLog;
using Microsoft.AspNetCore.Identity;
using ATFAS.Areas.Identity.Data;
using DataLayer.EfCode;
using PermissionParts;
using Microsoft.AspNetCore.Http;
using ATFAS.Services;

namespace ATFAS.Controllers
{
    public class CTOTDistributorController : Controller
    {
        private readonly ATFASContext _context;
        private UserManager<AppUser> _userManager;
        private readonly ExtraAuthorizeDbContext _extraContext;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMailService _mailService;
        private readonly IViewRenderService _viewrenderService;

        public CTOTDistributorController(ATFASContext context, IHttpContextAccessor httpContextAccessor, UserManager<AppUser> userManager, ExtraAuthorizeDbContext extraContext, IMailService mailService, IViewRenderService viewrenderService)
        {

            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _userManager = userManager;
            _extraContext = extraContext;
            _mailService = mailService;
            _viewrenderService = viewrenderService;

        }
        public IActionResult Index()
        {
            return View();
        }

        [HttpPost]
        public async Task<CTOTModel> QueryCtotflights(string select, string input, bool isAdmin)
        {
            var userid = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var userroles = _extraContext.UserToRoles.Where(r => r.UserId.Equals(userid)).ToList();

            List<CTOTDisplayModel> flights = new List<CTOTDisplayModel>();
            GDPManageController gdpManage = new GDPManageController(_context, _httpContextAccessor, _userManager, _extraContext, _mailService, _viewrenderService);

            var gdps = await _context.GDP.Include(g => g.GDPFlights).ThenInclude(g => g.Flight).Where(g => !g.IsCancelled && g.IsExecuted && g.IsActive).ToListAsync();   //เพิ่ม ด้วย 
            var ctotTrials = await gdpManage.GetFlightTrial();

            DateTime time = DateTime.UtcNow.AddHours(-6);
            var forwardingflights = await _context.CTOTForwarding.Where(f => f.EOBT.Value > time && f.FPLId != null && !f.IsCancelled).ToListAsync();

            foreach (var itm in gdps)
            {
                var gdp = itm;
                var gdpflights = itm.GDPFlights.Where(g => !g.Flight.IsCancelled && g.Flight.IsManaged && !g.IsCancelled).ToArray();


                foreach (var fpl in gdpflights)
                {
                    var f = flights.Where(f => f.Flight.Id == fpl.Flight.Id).FirstOrDefault();
                    if (f != null)
                    {
                        //f.Measures.Add(gdp);
                        f.MeasureString = string.Join(", ", gdp.Designator);
                        f.MeasureStringId = string.Join(",", gdp.Id);
                    }
                    else
                    {
                        var ctot = new CTOTDisplayModel();
                        // ctot.Measures = new List<GDP>();
                        ctot.Flight = fpl.Flight;
                        //ctot.Measures.Add(gdp);
                        ctot.MeasureString = gdp.Designator;
                        ctot.MeasureStringId = gdp.Id.ToString();
                        ctot.Username = fpl.Username;
                        ctot.Comment = fpl.Comment == null ? "-" : fpl.Comment;
                        flights.Add(ctot);
                    }
                }

            }

            foreach (var fwd in forwardingflights)
            {
                var ctot = new CTOTDisplayModel();
                //ctot.Measures = new List<GDP>();
                ctot.Flight = new Flight();

                ctot.Flight.Callsign = fwd.AircraftId;
                ctot.Flight.Id = (fwd.FPLId.HasValue) ? fwd.FPLId.Value : 0;
                ctot.Flight.AirportDeparture = fwd.Departure;
                ctot.Flight.AirportArrival = fwd.Arrival;
                ctot.Flight.Originator = fwd.Originator;
                ctot.Flight.EOBT = fwd.EOBT;
                ctot.Flight.ETOT = fwd.ETOT;
                ctot.Flight.ELDT = fwd.ELDT;
                ctot.Flight.EIBT = fwd.EIBT;
                ctot.Flight.CTOT = fwd.CTOT;
                ctot.Flight.CLDT = (fwd.CTOT.HasValue && fwd.ELDT.HasValue && fwd.ETOT.HasValue) ? fwd.CTOT + (fwd.ELDT - fwd.ETOT) : null;

                ctot.MeasureString = "CTOT Forward From " + fwd.Originator;
                ctot.IsForwarding = true;
                ctot.Comment = fwd.UserComment == null ? "-" : fwd.UserComment;
                ctot.ForwardStatus = GetForwardingStatus(fwd);

                if (!flights.Any(f => f.Flight.Id.Equals(ctot.Flight.Id)))
                    flights.Add(ctot);
            }

            if (input != null)
            {
                var inputStr = input.Split(',');
                var flightselect = new List<CTOTDisplayModel>();

                if (select == "acid")
                {
                    foreach (var itm in inputStr)
                        flightselect.AddRange(flights.Where(g => g.Flight.Callsign.StartsWith(itm.Trim())).ToList());
                    flights = flightselect.Distinct().ToList();
                }

                if (select == "adep")
                {
                    foreach (var itm in inputStr)
                        flightselect.AddRange(flights.Where(g => g.Flight.AirportDeparture.StartsWith(itm.Trim())).ToList());
                    flights = flightselect.Distinct().ToList();
                }

                if (select == "ades")
                {
                    foreach (var itm in inputStr)
                        flightselect.AddRange(flights.Where(g => g.Flight.AirportArrival.StartsWith(itm.Trim())).ToList());
                    flights = flightselect.Distinct().ToList();
                }

            }


            var flightselectairline = new List<CTOTDisplayModel>();
            var flightselectairport = new List<CTOTDisplayModel>();
            var usrProfiles = GetAirspaceUserProfiles();

            if (usrProfiles.Count > 0 && !isAdmin)
            {
                var usrAirlines = usrProfiles.Where(u => u.ProfileName == "User Airline Codes").Select(u => u.ProfileValue).FirstOrDefault();
                var usrAirports = usrProfiles.Where(u => u.ProfileName == "User Airport Codes").Select(u => u.ProfileValue).FirstOrDefault();
                if (!String.IsNullOrEmpty(usrAirlines))
                {
                    var alStr = usrAirlines.Split(',');
                    foreach (var itm in alStr)
                    {
                        if (itm != "")
                            flightselectairline.AddRange(flights.Where(g => g.Flight.Callsign.StartsWith(itm)).ToList());
                    }
                    flights = flightselectairline.Intersect(flights).ToList();
                }
                if (!String.IsNullOrEmpty(usrAirports))
                {
                    var apStr = usrAirports.Split(',');
                    foreach (var itm in apStr)
                    {
                        if (itm != "")
                            flightselectairport.AddRange(flights.Where(g => g.Flight.AirportArrival.StartsWith(itm.Trim()) || g.Flight.AirportDeparture.StartsWith(itm.Trim())).ToList());
                        //check Departure ATFMU 
                        if (userroles.Where(r => r.RoleName == "Departure ATFMU").ToList() != null)
                            flightselectairport.AddRange(flights.Where(g => g.Flight.AirportDeparture.StartsWith(itm.Trim())).ToList());
                    }


                    flights = flightselectairport.Intersect(flights).ToList();
                }
                if (String.IsNullOrEmpty(usrAirports) && String.IsNullOrEmpty(usrAirlines))
                    flights = flightselectairline.Intersect(flightselectairport).ToList();
            }


            return new CTOTModel { CtotFlights = flights.OrderBy(f => f.Flight.CTOT).ThenBy(f => f.Flight.CLDT).ToList(), CtotTrial = ctotTrials, CurrentUtc = DateTime.UtcNow };
        }

        [HttpPost]
        public async Task<bool> RequestEobtairline(int fid, DateTime? neweobt, string comment)
        {
            var ipaddress = HttpContext.Connection.RemoteIpAddress.ToString();
            var username = User.FindFirstValue(ClaimTypes.Name);
            CTOTForwarding fwds = await _context.CTOTForwarding.Where(f => f.FPLId.Equals(fid)).FirstOrDefaultAsync();
            fwds.NEWEOBT = neweobt;
            fwds.Status = 1;
            fwds.TimeStamp = DateTime.UtcNow;
            fwds.Username = username;
            fwds.Ip = ipaddress;
            fwds.UserComment = comment;

            await _context.SaveChangesAsync();
            ActionLog("Request New OBT : Callsign " + fwds.AircraftId + " New OBT " + neweobt);
            return true;
        }

        private int GetForwardingStatus(CTOTForwarding ctot)
        {
            if (ctot.Status == 1) return 1;
            if (ctot.Status == 2 && !ctot.IsUpdated) return 2;
            if (ctot.Status == 2 && ctot.IsUpdated) return 3;
            if (ctot.Status == 3) return 4;
            else return 0;

        }
        private List<UserProfile> GetAirspaceUserProfiles()
        {
            var id = User.FindFirstValue(ClaimTypes.NameIdentifier);
            return _context.UserProfile.Where(u => u.UserId.Equals(id)).ToList();
        }

        private void ActionLog(string source)
        {
            ActLog.ActionLog log = new ActLog.ActionLog();
            log.UserName = User.FindFirstValue(ClaimTypes.NameIdentifier);
            log.UserMachineIP = HttpContext.Connection.RemoteIpAddress.ToString();
            log.ActionMessage = "Test on log at " + DateTime.Today.ToString("dd-MM-yyyy HH:mm:ss");
            log.Source = source;
            Logger.LogMessage(log);
        }

        public class CTOTModel
        {
            public List<CTOTDisplayModel> CtotFlights { get; set; }
            public CTOTTrialModel CtotTrial { get; set; }
            public DateTime CurrentUtc { get; set; }
        }

        public class CTOTDisplayModel
        {
            public Flight Flight { get; set; }
            public string MeasureStringId { get; set; }
            public string MeasureString { get; set; }
            public string Comment { get; set; }
            public string Username { get; set; }
            public bool IsForwarding { get; set; }
            public int ForwardStatus { get; set; } // 0 = init , 1 = sent req , 2=  idle ,3= updated,4= terminated
            public bool IsIdep { get; set; }
        }


    }
}
