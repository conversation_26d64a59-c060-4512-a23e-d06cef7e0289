﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Threading.Tasks;
using System.Web;
using ATFAS.Data;
using ATFAS.Models;
using ATFAS.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using ATFAS.Controllers;
using ATFAS.ActLog;
using Microsoft.AspNetCore.Identity;
using ATFAS.Areas.Identity.Data;
using DataLayer.EfCode;
using ATFAS.Controllers.CTOTHelpers;
using RazorEngineCore;
using ATFAS.Services;
using MimeKit;
using System.IO;
using PermissionParts;
using FeatureAuthorize.PolicyCode;
using Microsoft.Extensions.Configuration;
using static ATFAS.Controllers.GDPManageController;

namespace ATFAS.Controllers
{
    public class CTOTForwardReviewController : Controller
    {
        private readonly ATFASContext _context;
        private UserManager<AppUser> _userManager;
        static IConfiguration conf = (new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json").Build());
        private readonly ExtraAuthorizeDbContext _extraContext;
        private readonly IMailService _mailService;
        private readonly IViewRenderService _viewrenderService;
        public static string IdepRegional = conf["idepRegionalIP"];
        public static bool EnableCTOTtoIdep = bool.Parse(conf["EnableCTOTtoIdep"].ToString());
        //public static string IdepBKK = conf["idepbkkIP"].ToString();
        //public static string IdepCNX = conf["idepcnxIP"].ToString();
        //public static string IdepHKT = conf["idephktIP"].ToString();
        public static TimeSpan _STT;

        public CTOTForwardReviewController(UserManager<AppUser> userManager, ATFASContext context, ExtraAuthorizeDbContext extraContext, IMailService mailService, IViewRenderService viewrenderService)
        {
            _context = context;
            _userManager = userManager;
            _extraContext = extraContext;
            _mailService = mailService;
            _viewrenderService = viewrenderService;

        }
        [HasPermission(Permissions.CtotChange)]
        public IActionResult Index()
        {
            try
            {
                var conf = _context.GeneralConfiguration.FirstOrDefault();
                _STT = new TimeSpan(0, 20, 0);
                if (conf != null)
                {
                    ViewData["comment"] = conf.ForwardComment;
                    _STT = conf.DefaultTaxiTime;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message); //log
            }
            return View();
        }

        [HttpPost]
        public async Task<IActionResult> ForwardSlotMessage(string idString, string comment)
        {
            try
            { 
                try
                {
                    var ctotConfig = _context.GeneralConfiguration.FirstOrDefault();

                    if (ctotConfig.ForwardComment != comment)
                    {
                        ctotConfig.ForwardComment = comment;
                        _context.SaveChanges();
                    }

                    //else
                    //{
                    //    ctotConfig = new CTOTConfig();
                    //    ctotConfig.Param = "ForwardComment";
                    //    ctotConfig.String = comment;
                    //    _context.CTOTConfigs.Add(ctotConfig);
                    //    _context.SaveChanges();
                    //}
                }
                catch (Exception e) { Console.WriteLine(e); }

                string[] ids = idString.Substring(1, idString.Length - 2).Split(',');
                List<ForwardSlotMessageReview> messageList = new List<ForwardSlotMessageReview>();
                if (ids != null && ids.Length > 0 && ids[0].Length > 0)
                {
                    foreach (string id in ids)
                    {
                        try
                        {
                            ForwardSlotMessageReview message = _context.ForwardSlotMessageReview.Find(int.Parse(id));
                            if (message == null) continue;
                            CTOTForwarding ctotForward = (from c in _context.CTOTForwarding
                                                          where c.AircraftId.Equals(message.AircraftId) && (c.EOBT.Value > message.EOBT.Value.AddHours(-6) || c.EOBT.Value < message.EOBT.Value.AddHours(6))
                                                          //where c.AircraftId.Equals(message.AircraftId) && c.EOBT.Value.Equals(message.EOBT.Value)
                                                          select c).FirstOrDefault();
                            Flight flightInfo = (from f in _context.Flight
                                                 where f.Callsign.Equals(message.AircraftId) && f.AirportDeparture.Equals(message.Departure) && f.AirportArrival.Equals(message.Arrival) && (f.EOBT.Value > message.EOBT.Value.AddHours(-6) || f.EOBT.Value < message.EOBT.Value.AddHours(6))
                                                 //where f.Callsign.Equals(message.AircraftId) && f.AirportDeparture.Equals(message.Departure) && f.AirportArrival.Equals(message.Arrival) && f.EOBT.Value.Equals(message.EOBT.Value)
                                                 select f).FirstOrDefault();
                           // var idepIP = (message.Departure == "VTSP") ? IdepHKT : (message.Departure == "VTCC") ? IdepCNX : IdepBKK; แก้ให้ไปใช้ regional
                            // add idep Flight

                            Flight idepFlight = new Flight();
                            idepFlight.Id = 0;
                            idepFlight.Callsign = message.AircraftId;
                            idepFlight.AirportDeparture = message.Departure;
                            idepFlight.AirportArrival = message.Arrival;
                            idepFlight.EOBT = message.EOBT;


                            if (message.MessageType.Equals("SAM") && ctotForward == null)
                            {
                                ctotForward = new CTOTForwarding();
                                ctotForward.AircraftId = message.AircraftId;
                                ctotForward.Arrival = message.Arrival;
                                ctotForward.COMMENT = createComment(message, comment);
                                ctotForward.CTOT = message.CTOT;
                                ctotForward.Departure = message.Departure;
                                ctotForward.EOBT = message.EOBT;
                                ctotForward.IsCancelled = false;
                                ctotForward.Originator = message.Originator;
                                ctotForward.RawMessage = message.RawMessage;
                                ctotForward.REASON = message.REASON;
                                ctotForward.REGCAUSE = message.REGCAUSE;
                                ctotForward.REGUL = message.REGUL;
                                ctotForward.Status = 0;
                                if (flightInfo != null)
                                {
                                    ctotForward.AircraftType = flightInfo.AircraftType;
                                    ctotForward.EIBT = flightInfo.EIBT;
                                    ctotForward.ELDT = flightInfo.ELDT;
                                    ctotForward.ETOT = flightInfo.ETOT;
                                    ctotForward.FPLId = flightInfo.Id;
                                    flightInfo.CTOT = ctotForward.CTOT;

                                }
                                idepFlight.CTOT = message.CTOT;
                                _context.CTOTForwarding.Add(ctotForward);
                                 if(EnableCTOTtoIdep) await updateCTOTtoIdepAsync(idepFlight, "SAM" ,IdepRegional);  /*เอาคอมเม้นออกตอนเทสด้วย*/
                            }
                            else
                            {
                                if (ctotForward == null) continue;

                                if (message.MessageType.Equals("SRM"))
                                {
                                    ctotForward.CTOT = message.NEWCTOT;
                                    ctotForward.IsUpdated = true;
                                    idepFlight.CTOT = message.NEWCTOT;
                                    //update eobtAirline in Flight
                                    if (flightInfo != null) flightInfo.EOBTAirline = ctotForward.NEWEOBT;

                                    if (EnableCTOTtoIdep) await updateCTOTtoIdepAsync(idepFlight, "SRM",IdepRegional);  /* เอาคอมเม้นออกตอนเทสด้วย*/
                                }
                                else if (message.MessageType.Equals("SLC"))
                                {
                                    ctotForward.IsCancelled = true;
                                    if (EnableCTOTtoIdep) await cancelCTOTtoIdepAsync(idepFlight, "SLC", IdepRegional);    /*  เอาคอมเม้นออกตอนเทสด้วย */
                                }
                                if (flightInfo != null) flightInfo.CTOT = ctotForward.CTOT;
                            }
                            _context.SaveChanges();
                            message.CTOTForwardingID = ctotForward.Id;
                            message.IsReviewed = true;
                            message.TimeReviewed = DateTime.UtcNow;
                            messageList.Add(message);
                        }
                        catch (Exception e)
                        {
                            Console.WriteLine(e);
                            continue;
                        }
                    }

                    string url = Url.ActionLink("Index", "CTOTDistributor");
                    await SendEmailAFTN(_context, messageList, comment, url);
                }
                return Json("");
            }
            catch (Exception e)
            {
                Console.WriteLine(e); //log
                return Json(null);
            }
        }

        [HttpPost]
        public IActionResult GetForwardSlotMessageList()
        {
            ForwardSlotMessageList forwardSlotMessageList;
            try
            {

                /* For Dev */
                DateTime time = DateTime.UtcNow.AddHours(-6);
                ForwardSlotMessageReview[] messages = _context.ForwardSlotMessageReview.Where(f => f.EOBT.Value > time).ToArray();
                /* For Test 
                ForwardSlotMessageReview[] messages = _context.ForwardSlotMessageReview.ToArray(); */
                List<ForwardSlotMessageReview> messageList = new List<ForwardSlotMessageReview>();
                foreach (ForwardSlotMessageReview message in messages)
                {
                    Boolean isValid = true;
                    if (message.MessageType.Equals("SAM") && !message.IsReviewed)
                    {
                        foreach (ForwardSlotMessageReview msg in messages)
                        {
                            if (message.Id != msg.Id && msg.MessageType.Equals("SAM") && msg.AircraftId.Equals(message.AircraftId) && msg.FilingTime != null && msg.FilingTime > message.FilingTime)
                            {
                                isValid = false;
                                break;
                            }
                        }
                    }
                    if (isValid) messageList.Add(message);
                }
                var fwd = messageList.OrderBy(m => m.EOBT).First();
                var flights = _context.Flight.Where(f => f.EOBT >= fwd.EOBT).ToArray();
                forwardSlotMessageList = new ForwardSlotMessageList(messageList.ToArray(), flights);
            }
            catch (Exception e) { return Json(null); }
            return Json(forwardSlotMessageList);
        }
        [HttpGet]
        public async Task<List<CTOTForwardingModel>> GetCTOTForwarding()
        {
            List<AppUser> userList = await _userManager.Users.ToListAsync();
            List<Pointofcontact> PocList = await _context.Pointofcontact.ToListAsync();

            List<CTOTForwardingModel> result = new List<CTOTForwardingModel>();
            DateTime time = DateTime.UtcNow.AddHours(-6);
            var ctots = await _context.CTOTForwarding.Where(f => !f.IsCancelled && f.Status > 0 && f.EOBT.Value > time).ToListAsync();
            var reqs = ctots.Where(r => r.Status == 1).OrderByDescending(r => r.TimeStamp).ThenBy(r => r.NEWEOBT).ToList();
            var stats = ctots.Where(s => s.Status > 1).OrderByDescending(r => r.TimeStamp).ThenBy(r => r.AircraftId).ToList();

            foreach (var itm in reqs)
            {
                result.Add(new CTOTForwardingModel(userList, PocList, itm));
            }
            foreach (var itm in stats)
            {
                result.Add(new CTOTForwardingModel(userList, PocList, itm));
            }



            //result.AddRange(reqs);
            //result.AddRange(stats);

            return result;
        }

        [HttpPost]
        public async Task<List<CTOTForwardingModel>> ResponseEobtairline(int cid, int status, string atfmuComment)
        {
            CTOTForwarding fwds = await _context.CTOTForwarding.Where(f => f.Id.Equals(cid)).FirstOrDefaultAsync();
            fwds.Status = status;
            fwds.TimeStamp = DateTime.UtcNow;
            fwds.ATFMUComment = atfmuComment;
            await _context.SaveChangesAsync();

            return await GetCTOTForwarding();

            //return new CTOTModel { CtotFlights = flights.OrderBy(f => f.Flight.CTOT).ToList(), CtotTrial = ctotTrials };
        }
        [HttpGet]
        public async Task<Atfmu> GetAtfmuByOriginator(string originator)
        {
            Atfmu res = new Atfmu();
            res = await _context.Atfmu.Where(a => a.AftnAddress.Contains(originator)).FirstOrDefaultAsync();

            return res;
        }

        /****************************** Helper Methods ******************************/

        /**Update CTOT to Idep ***/
        public async Task updateCTOTtoIdepAsync(Flight flight, string crudFlag, string IP)
        {
            CtotFlightModel cm = new CtotFlightModel();
            cm.FlightId = flight.Id;
            cm.Callsign = flight.Callsign;
            cm.Departure = flight.AirportDeparture;
            cm.Arrival = flight.AirportArrival;
            cm.EOBT = flight.EOBT;
            cm.CTOT = flight.CTOT;
            cm.CLDT = flight.CLDT;
            cm.ETOT = flight.ETOT;
            cm.ELDT = flight.ELDT;
            cm.CrudFlag = crudFlag;

            Ctot ctot = new Ctot();
            ctot.ctot = cm;

            HttpClient client = new HttpClient { BaseAddress = new Uri(IP) };
            try
            {
                HttpResponseMessage response = await client.PostAsJsonAsync("api/flightProcessingRegional/updateCTOTtoIdepGW", cm); //ต้องเขียนใหม่ในกรณี Regional
                if (response.IsSuccessStatusCode)
                {
                    // return response.Content.ReadAsStringAsync().Result;
                }
                else
                {
                    var log = "CTOT - Error Call updateCTOTtoIdepGW : " + response.Content.ReadAsStringAsync().Result;
                    ActionLog(log);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);

            }

        }

        public async Task cancelCTOTtoIdepAsync(Flight flight, string crudFlag, string IP)
        {
            CtotFlightModel cm = new CtotFlightModel();
            cm.FlightId = flight.Id;
            cm.Callsign = flight.Callsign;
            cm.Departure = flight.AirportDeparture;
            cm.Arrival = flight.AirportArrival;
            cm.EOBT = flight.EOBT;
            cm.CTOT = flight.CTOT;
            cm.CLDT = flight.CLDT;
            cm.ETOT = flight.ETOT;
            cm.ELDT = flight.ELDT;

            cm.CrudFlag = crudFlag;

            Ctot ctot = new Ctot();
            ctot.ctot = cm;

            HttpClient client = new HttpClient { BaseAddress = new Uri(IP) };


            // HttpClient client = new HttpClient { BaseAddress = new Uri(IdepBkkIP) };
            try
            {
                HttpResponseMessage response = await client.PostAsJsonAsync("api/flightProcessingRegional//cancelCTOTtoIdepGW", cm);
                if (response.IsSuccessStatusCode)
                {
                    //return ;
                }
                else
                {
                    var log = "CTOT - Error Call cancelCTOTtoIdepGW : " + response.Content.ReadAsStringAsync().Result;
                    ActionLog(log);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);

            }
        }
        public List<MailingList> GetMailingList()
        {
            List<MailingList> mails = new List<MailingList>();
            var userprofilelist = _context.UserProfile.ToList();
            foreach (AppUser user in _userManager.Users
                .Where(x => x.EmailConfirmed && x.UserName != "superadmin"))
            {

                var roles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(user.Id)).Select(x => x.RoleName).ToList();
                var permissions = getPermissionInRole(roles);
                var userprofiles = userprofilelist.Where(p => p.UserId.Equals(user.Id)).ToList();
                var airport = userprofiles.Where(p => p.ProfileName.Contains("Airport Codes")).FirstOrDefault();
                var airline = userprofiles.Where(p => p.ProfileName.Contains("Airline Codes")).FirstOrDefault();
                var subemail = userprofiles.Where(p => p.ProfileName.Contains("Subscribe CTOT Email")).FirstOrDefault();
                var subaftn = userprofiles.Where(p => p.ProfileName.Contains("Subscribe CTOT AFTN")).FirstOrDefault();
                var address = userprofiles.Where(p => p.ProfileName.Contains("User AFTN Addresses")).FirstOrDefault();
                if (permissions.Contains(Permissions.AirlineAftn) || permissions.Contains(Permissions.AirportAftn) || permissions.Contains(Permissions.AnspAftn))
                {
                    MailingList mail = new MailingList();
                    mail.Organization = user.FullName;
                    mail.Role = permissions.Contains(Permissions.AirportAftn) ? 1 : permissions.Contains(Permissions.AirlineAftn) ? 2 : 3;
                    mail.Email = user.Email;
                    mail.Telephone = user.PhoneNumber;
                    mail.Airport = (airport != null) ? airport.ProfileValue : "";
                    mail.Callsign = (airline != null) ? airline.ProfileValue : "";
                    mail.DistributeByEmail = (subemail != null) ? subemail.IsActive : false;
                    mail.DistributeByAFTN = (subaftn != null) ? subaftn.IsActive : false;
                    mail.AftnAddress = (address != null) ? address.ProfileValue : "";

                    mails.Add(mail);
                }
            }
            return mails;

        }

        private List<Permissions> getPermissionInRole(List<string> roles)
        {
            List<Permissions> permissions = new List<Permissions>();
            foreach (var role in roles)
            {
                var pem = _extraContext.RolesToPermissions.Where(x => x.RoleName.Equals(role)).Select(x => x.PermissionsInRole).FirstOrDefault();
                permissions.AddRange(pem);
            }
            return permissions;
        }
        private static string createComment(ForwardSlotMessageReview message, string comment)
        {
            string newComment = "";
            if (!(String.IsNullOrEmpty(message.COMMENT))) newComment += message.COMMENT + " ";
            newComment += "MSG FORWARDED FM " + ((DateTime)message.FilingTime).ToString("ddHHmm") + " " + message.Originator + " BY BANGKOK ATFMU STP " + comment;
            return newComment;
        }


        private async Task SendEmailAFTN(ATFASContext _context, List<ForwardSlotMessageReview> messageList, string comment, string url)
        {
            try
            {
                List<SAMMessage> samMessage = new List<SAMMessage>();
                //List<MailingList> mailingLists = _context.MailingLists.Select(m => m).ToList();
                List<MailingList> mailingLists = GetMailingList();
                foreach (MailingList mailingList in mailingLists.Where(m => (m.DistributeByEmail && m.Email != null) || (m.DistributeByAFTN && m.AftnAddress != null)))
                {
                    List<ForwardSlotMessageReview> msgList = null;
                    bool isSendEmail = false;
                    bool isSendAftn = false;
                    try
                    {
                        if ((MailingListRole)mailingList.Role == MailingListRole.Airline)
                        {
                            List<string> callsignList = new List<string>();
                            foreach (string c in mailingList.Callsign.Split(','))
                            {
                                string callsign = c.Trim();
                                if (!String.IsNullOrEmpty(callsign)) callsignList.Add(callsign.ToUpper());
                            }
                            msgList = messageList.Where(msg => callsignList.Any(cs => msg.AircraftId.StartsWith(cs))).ToList();
                        }
                        else if ((MailingListRole)mailingList.Role == MailingListRole.Airport || (MailingListRole)mailingList.Role == MailingListRole.ANSP)
                        {
                            List<string> airportList = new List<string>();
                            foreach (string a in mailingList.Airport.Split(','))
                            {
                                string airport = a.Trim();
                                if (!String.IsNullOrEmpty(airport)) airportList.Add(airport.ToUpper());
                            }
                            msgList = messageList.Where(msg => airportList.Any(ap => msg.Departure.StartsWith(ap) || msg.Arrival.StartsWith(ap))).ToList();
                        }
                    }
                    catch (Exception e) { }

                    if (msgList != null && msgList.Count > 0)
                    {
                        isSendEmail = mailingList.DistributeByEmail;
                        isSendAftn = mailingList.DistributeByAFTN;
                    }

                    if (isSendEmail)
                    {
                        try
                        {
                            CTOTForwardMail mail = new CTOTForwardMail();
                            mail.Comment = comment;
                            mail.Recipient = mailingList.Organization;
                            mail.MessageList = msgList;
                            mail.Url = url;
                            await SendEmail(new List<string>(mailingList.Email.Split(new char[] { ',', ' ' })), "CTOT INFORMATION (FORWARDED BY BANGKOK ATFMU)", mail);
                        }
                        catch (Exception e) { }
                    }

                    if (isSendAftn)
                    {
                        foreach (ForwardSlotMessageReview msg in msgList)
                        {
                            try
                            {
                                string oldComment = createComment(msg, comment);
                                string newComment = "";
                                for (int i = 0; i < oldComment.Length; i++)
                                {
                                    if ((i > 0) && (i % 69 == 0)) newComment += Environment.NewLine;
                                    newComment += oldComment.ElementAt(i);
                                }
                                newComment += Environment.NewLine;
                                string message = msg.RawMessage;
                                int indexStart = message.IndexOf("-COMMENT");
                                if (indexStart > 0)
                                {
                                    indexStart += 9;
                                    int indexEnd = message.IndexOf('-', indexStart);
                                    if (indexEnd > 0) indexEnd -= 1;
                                    else indexEnd = message.Length - 1;
                                    message = message.Remove(indexStart, indexEnd - indexStart + 1).Insert(indexStart, newComment);
                                }
                                else message += " -COMMENT " + newComment;
                                SAMMessage newSam = new SAMMessage();
                                newSam.Message = message;
                                newSam.CTOT_id = msg.Id;
                                newSam.ISDistributed = false;
                                newSam.DistributeTime = DateTime.UtcNow;
                                newSam.Addresses = mailingList.AftnAddress;
                                samMessage.Add(newSam);
                            }
                            catch (Exception ex) { }
                        }
                    }
                }
                var samMessageAdjust = RemoveSamDuplicate(samMessage);
                _context.SAMMessage.AddRange(samMessageAdjust);
                // _context.SAMMessage.AddRange(samMessage);
                await _context.SaveChangesAsync();
            }
            catch (Exception e)
            { //CTOTLog.LogError(e); }
            }
        }

        private List<SAMMessage> RemoveSamDuplicate(List<SAMMessage> samMessage)
        {
            //List<SAMMessage> result = new List<SAMMessage>();
            return samMessage.GroupBy(s => s.CTOT_id).Select(s => new SAMMessage
            {
                Message = s.Select(ss => ss.Message).First(),
                CTOT_id = s.Key,
                ISDistributed = false,
                DistributeTime = DateTime.UtcNow,
                Addresses = removeDuplicateString(string.Join(",", s.Select(ss => ss.Addresses))),
            }).ToList();
        }
        private string removeDuplicateString(string strs)
        {
            strs.Replace(" ", "");
            var arr = strs.Split(',').Distinct();
            return string.Join(",", arr);
        }

        private async Task SendEmail(List<string> emails, string title, CTOTForwardMail mail)
        {
            string templateName = "ForwardEmailTemplate";
            // Create builder
            var builder = new BodyBuilder();
            using (StreamReader SourceReader = System.IO.File.OpenText("Views/CTOTMail/" + templateName + ".cshtml"))
            {
                builder.HtmlBody = SourceReader.ReadToEnd();
            }
            string messageBody = await _viewrenderService.RenderToStringAsync("CTOTMail/" + templateName, mail);

            MailRequest request = new MailRequest();
            request.ToEmails = emails;
            request.Subject = title;
            request.Body = messageBody;


            var result = _mailService.SendEmailAsync(request);
            if (result.Exception != null)
                ActionLog("Send CTOT Email Error :" + result.Exception.ToString());
        }


        /*  private static string SendNotifyEmail(List<string> emails, string title, CtotForwardNotifyMail mail)
          {
              string templateName = "NotifyEmailTemplate";
              string body = "";
              if (RazorEngine.Engine.Razor.IsTemplateCached(templateName, typeof(CtotForwardNotifyMail)))
                  body = RazorEngine.Engine.Razor.Run(templateName, typeof(CtotForwardNotifyMail), mail);
              else
              {
                  string template = System.IO.File.ReadAllText(System.Web.HttpContext.Current.Server.MapPath("~/Views/CTOTMail/" + templateName + ".cshtml"));
                  body = RazorEngine.Engine.Razor.RunCompile(template, templateName, typeof(CtotForwardNotifyMail), mail);
              }
              return AtfmMail.SendEmail(Properties.Settings.Default.SmtpServer, Properties.Settings.Default.SmtpPort, Properties.Settings.Default.SmtpSsl, Properties.Settings.Default.SmtpUsername, Properties.Settings.Default.SmtpPassword, Properties.Settings.Default.SmtpSenderEmail,
                  emails, title, body);
          }*/

        private void ActionLog(string source)
        {
            ActLog.ActionLog log = new ActLog.ActionLog();
            log.UserName = User.FindFirstValue(ClaimTypes.NameIdentifier);
            log.UserMachineIP = HttpContext.Connection.RemoteIpAddress.ToString();
            log.ActionMessage = "Test on log at " + DateTime.Today.ToString("dd-MM-yyyy HH:mm:ss");
            log.Source = source;
            Logger.LogMessage(log);
        }

        public class CTOTForwardingModel
        {
            public CTOTForwarding Forwarding { get; set; }
            public Pointofcontact Poc { get; set; }
            public AppUser UserDetails { get; set; }
            public TimeSpan STT { get; set; }

            public CTOTForwardingModel(List<AppUser> userList, List<Pointofcontact> pocList, CTOTForwarding forward)
            {
                Forwarding = forward;
                Poc = new Pointofcontact();
                UserDetails = new AppUser();
                if (pocList.Count > 0)
                {
                    var airlineCode = forward.AircraftId.Substring(0, 3);
                    var poc = pocList.Where(p => p.AirlineCode.Contains(airlineCode)).FirstOrDefault();
                    Poc = (poc == null) ? Poc : poc;
                }
                if (userList.Count > 0)
                {
                    var usr = userList.Where(u => u.UserName.Equals(forward.Username)).FirstOrDefault();
                    UserDetails = (usr == null) ? UserDetails : usr;
                }
                STT = _STT;
            }
        }


    }


    /****************************** Helper Classes ******************************/



    public class ForwardSlotMessageList
    {
        public ForwardSlotMessageReview[] messages;
        public ForwardSlotMessageTime[] messageTimes;
        //public ForwardSlotMessageIsCtot[] messageCtot;

        public ForwardSlotMessageList(ForwardSlotMessageReview[] messages, Flight[] flights)
        {
            this.messages = messages;
            messageTimes = new ForwardSlotMessageTime[messages.Length];
            for (int i = 0; i < messageTimes.Length; i++) messageTimes[i] = new ForwardSlotMessageTime(messages[i], flights);
        }
    }
    public class ForwardSlotMessageIsCtot
    {
        public bool IsFound { get; set; }
        public DateTime? CTOTFlight { get; set; }
    }

    public class ForwardSlotMessageTime
    {
        public string EOBT;
        public string CTOT;
        public string NEWCTOT;
        public bool IsCtotFound;
        public DateTime? CTOTFlight;

        public ForwardSlotMessageTime(ForwardSlotMessageReview f, Flight[] flights)
        {
            EOBT = f.EOBT != null ? ((DateTime)f.EOBT).ToString("dd / HH:mm") : "-";
            CTOT = f.CTOT != null ? ((DateTime)f.CTOT).ToString("dd / HH:mm") : "-";
            NEWCTOT = f.NEWCTOT != null ? ((DateTime)f.NEWCTOT).ToString("dd / HH:mm") : "-";
            if (f.MessageType == "SAM")
            {
                var flight = flights.Where(x => x.Callsign.Equals(f.AircraftId) && x.AirportDeparture.Equals(f.Departure) && x.AirportArrival.Equals(f.Arrival) && x.EOBT.Equals(f.EOBT)).FirstOrDefault();
                if (flight != null)
                {
                    IsCtotFound = (flight.CTOT == null) ? false : true;
                    CTOTFlight = flight.CTOT;
                }
            }
        }
       
    }

}
