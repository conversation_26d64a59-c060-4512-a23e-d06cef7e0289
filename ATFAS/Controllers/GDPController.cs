﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using ATFAS.Data;
using ATFAS.Models;
using ATFAS.ViewModels;
using FeatureAuthorize.PolicyCode;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using PermissionParts;
using Newtonsoft.Json;
using NetTopologySuite.Geometries;
using Microsoft.Extensions.Configuration;
using System.IO;

namespace ATFAS.Controllers
{
  
    public class GDPController : Controller
    {
        private readonly ATFASContext _context;

        static IConfiguration conf = (new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json").Build());
        public static string ATFMOptimizerIP = conf["ATFMOptimizerIP"].ToString();
        public static int HEC = Int32.Parse(conf["HECPeriod"].ToString());
        public static double doubleHCEdivided = HEC / 60.0;
        public static float alpha = float.Parse(conf["Alpha"].ToString());
        public static int fastMaxDelayIndex = Int32.Parse(conf["FastMaxDelayIndex"].ToString());
        public static int windowStep = Int32.Parse(conf["WindowStep"].ToString());
        public static int timeLimit = Int32.Parse(conf["TimeLimit"].ToString());

        private HttpClient client = new HttpClient { BaseAddress = new Uri(ATFMOptimizerIP) };

        public GDPController(ATFASContext context)
        {
            _context = context;
            client.DefaultRequestHeaders.Accept.Clear();
            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        }

        [HasPermission(Permissions.CtotChange)]
        public IActionResult Index()
        {
            ViewData["TrafficAreaId"] = new SelectList(_context.Set<TrafficArea>(), "Id", "Type");
            return View();
        }

        [HttpPost]
        public async Task<GDPChart> ChangeState(int gdpId, int stateId, string regul, string regcause, string comment)
        {
            try
            {
                if (gdpId < 0 || stateId < 0 || stateId > 2) return new GDPChart { Title = "gdpId or stateId is not valid." };
                GDP gdp = await _context.GDP.Include(g => g.GDPFlights).Include(g => g.TrafficArea).Where(g => g.Id == gdpId).FirstOrDefaultAsync();
                if (gdp == null) return new GDPChart { Title = "The GDP does not exist." };
                if (gdp.IsCancelled) return new GDPChart { Title = "The GDP has already been cancelled." };
                if ((!gdp.IsExecuted && stateId == 1) || (gdp.IsExecuted && stateId != 1)) return new GDPChart { Title = "The GDP state is not up-to-date." };
                if (stateId == 2 && (regul == null || regul.Length < 1 || regcause == null || regcause.Length < 1)) return new GDPChart { Title = "Regul and Regcause are required." };
                if (stateId == 0)
                {
                    gdp.IsCancelled = true;
                    gdp.TimeCancelled = DateTime.UtcNow;
                }
                else if (stateId == 1)
                {
                    gdp.IsExecuted = false;
                    gdp.IsActive = false;
                    gdp.TimeUpdated = DateTime.UtcNow;
                    gdp.GDPFlights.Clear();
                }
                else
                {
                    gdp.IsExecuted = true;
                    gdp.IsActive = true;
                    gdp.TimeExecuted = DateTime.UtcNow;
                    gdp.Regul = regul;
                    gdp.Regcause = regcause;
                    if (comment != null && comment.Length > 0) gdp.Comment = comment;
                    GDPChart gdpChart = await RequestChart(gdp);
                    gdp.GDPFlights = new List<GDPFlight>();
                    foreach (List<FlightData> flightDataList in gdpChart.FlightLists)
                    {
                        foreach (FlightData flightData in flightDataList)
                        {
                            GDPFlight gdpFlight = new GDPFlight
                            {
                                FlightId = flightData.Flight.Id,
                                COBT = flightData.Flight.COBT,
                                CTOT = flightData.Flight.CTOT,
                                CLDT = flightData.Flight.CLDT,
                                CIBT = flightData.Flight.CIBT,
                                COBTSaved = flightData.Flight.COBT,
                                CTOTSaved = flightData.Flight.CTOT,
                                CLDTSaved = flightData.Flight.CLDT,
                                CIBTSaved = flightData.Flight.CIBT,
                                TimeSaved = DateTime.UtcNow
                            };
                            await _context.AddAsync(gdpFlight);
                            gdp.GDPFlights.Add(gdpFlight);
                        }
                    }
                    _context.Update(gdp);
                    await _context.SaveChangesAsync();
                    return gdpChart;
                }
                _context.Update(gdp);
                await _context.SaveChangesAsync();
                return new GDPChart { GDP = gdp };
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return new GDPChart { Title = "Server Problem: GDP/ChangeState" };
            }
        }

        [HttpPost]
        public async Task<GDPInfo> RequestGDPInfo()
        {
            GDPInfo info = new GDPInfo
            {
                GDPSaved = await _context.GDP.Include(g => g.TrafficArea).Where(g => !g.IsCancelled && !g.IsExecuted).OrderByDescending(g => g.TimeUpdated).ThenByDescending(g => g.TimeSaved).Take(10).ToListAsync(),
                GDPExecuted = await _context.GDP.Include(g => g.TrafficArea).Where(g => !g.IsCancelled && g.IsExecuted && g.IsActive).OrderByDescending(g => g.TimeExecuted).ToListAsync(),
                Capacities = await _context.Capacity.Where(c => !c.IsDep && !c.IsOverall && !c.IsOccupancy).ToListAsync(),
                CapacityEvents = await _context.CapacityEvent./*Where(c => c.EndTime > DateTime.UtcNow && !c.Capacity.IsDep && !c.Capacity.IsOverall && !c.Capacity.IsOccupancy).*/OrderBy(c => c.StartTime).ToListAsync(),
                GDPCharts = new List<GDPChart>()
            };
            foreach (GDP gdp in info.GDPExecuted)
            {
                info.GDPCharts.Add(await RequestChart(gdp));
            }
            /*
            foreach (GDP gdp in info.GDPSaved)
            {
                info.GDPCharts.Add(await RequestChart(gdp));
            }
            */
            return info;
        }

        [HttpPost]
        public async Task<GDPChart> RequestChart(GDP gdp)
        {
            try
            {
                if (!ModelState.IsValid) return new GDPChart { Title = string.Join("\n", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)) };
                if (!gdp.IsValid()) return new GDPChart { Title = gdp.GetErrorMsg() };
                gdp.MakeValid();
                if (gdp.Id >= 0) gdp = await _context.GDP.FindAsync(gdp.Id);
                if (gdp == null) return new GDPChart { Title = "The GDP does not exist." };
                CTOTMeasure[] ctotMeasures = new CTOTMeasure[1];
                ctotMeasures[0] = new CTOTMeasure
                {
                    MeasureId = gdp.Id,
                    StartTime = JsonConvert.SerializeObject(gdp.StartTime).Trim('"'),
                    EndTime = JsonConvert.SerializeObject(gdp.EndTime).Trim('"'),
                    EndRecovery = JsonConvert.SerializeObject(gdp.EndRecoveryTime).Trim('"'),
                    Capacity = (int)Math.Round(gdp.CapacityPerHr * doubleHCEdivided), //use HEC from appSettings
                    CapacityRecovery = (int)Math.Round(gdp.CapacityRecoveryPerHr * doubleHCEdivided), //use HEC from appSettings
                    TrafficAreaType = "ArrivalAirport",
                    TrafficAreaName = gdp.Point
                };
                CTOTAlgorithm ctotAlgorithm = new CTOTAlgorithm
                {
                    AlgorithmName = "Network-based", //"Fast-network-based",
                    Parameters = new CTOTParameters
                    {
                        StartTime = JsonConvert.SerializeObject(gdp.StartTime.AddHours(-1)).Trim('"'),
                        EndTime = JsonConvert.SerializeObject(gdp.EndRecoveryTime.AddHours(1)).Trim('"'),
                        HaveAirborneDelay = false,
                        HECPeriod = HEC,
                        Alpha = alpha,
                        FastMaxDelayIndex = fastMaxDelayIndex,
                        MaxDelayIndex = fastMaxDelayIndex,
                        TimeLimit = timeLimit,
                        WindowStep = windowStep
                    }
                };
                List<Flight> flights = new List<Flight>();
                List<int> flightIds = new List<int>();
                List<FlightData> flightDatas = new List<FlightData>();
                if (gdp.IsExecuted)
                {
                    List<GDPFlight> gdpFlights = (await _context.GDP.Include(g => g.GDPFlights).ThenInclude(g => g.Flight).Where(g => g.Id == gdp.Id && !gdp.IsCancelled).FirstOrDefaultAsync()).GDPFlights;
                    foreach (GDPFlight gdpFlight in gdpFlights)
                    {
                        if (!gdpFlight.IsCancelled)
                        {
                            Flight flight = gdpFlight.Flight;
                            flight.COBT = gdpFlight.COBT;
                            flight.CTOT = gdpFlight.CTOT;
                            flight.CLDT = gdpFlight.CLDT;
                            flight.CIBT = gdpFlight.CIBT;
                            flights.Add(flight);
                            flightIds.Add(flight.Id);
                        }
                    }
                }
                string formatLabel = FlightData.FORMAT_TIME;
                string title = "Regulated Demand <" + gdp.Point + "> (" + gdp.StartTime.ToString(FlightData.FORMAT_DATETIME_LONG) + " - " + gdp.EndTime.ToString(FlightData.FORMAT_TIME) + " / " + gdp.EndRecoveryTime.ToString(FlightData.FORMAT_TIME) + ")";
                /*
                if (gdp.StartTime.Date.Equals(gdp.EndRecoveryTime.Date)) title += gdp.EndRecoveryTime.ToString(FlightData.FORMAT_TIME) + ")";
                else title += gdp.EndRecoveryTime.ToString(FlightData.FORMAT_DATETIME_LONG) + ")";
                */
                if (!gdp.StartTime.Date.Equals(gdp.EndRecoveryTime.Date)) formatLabel = FlightData.FORMAT_DATETIME_SHORT;
                int numInterval = (int)Math.Ceiling((gdp.EndRecoveryTime.Subtract(gdp.StartTime).TotalMinutes) / (double)gdp.IntervalMin);
                gdp.TrafficArea = await _context.TrafficArea.FindAsync(gdp.TrafficAreaId);
                TrafficDemand trafficDemand = new TrafficDemand
                {
                    Id = gdp.Id,
                    TrafficAreaId = gdp.TrafficAreaId,
                    TrafficArea = gdp.TrafficArea,
                    Designator = gdp.Designator,
                    Point = gdp.Point,
                    IsArr = true,
                    StartTime = gdp.StartTime,
                    EndTime = gdp.EndRecoveryTime,
                    IntervalMin = gdp.IntervalMin,
                    IsIFR = gdp.IsIFR,
                    IsVFR = gdp.IsVFR,
                    IsFPL = true,
                    IsATFM = true,
                    IsATSMSG = true,
                    IsSUR = true,
                    IsPassed = true,
                    TimeSaved = gdp.TimeSaved
                };
                GDPChart gdpChart = new GDPChart
                {
                    GDP = gdp,
                    TrafficDemand = trafficDemand,
                    Title = title,
                    Labels = new string[numInterval],
                    Titles = new string[numInterval],
                    DataFPL = new int[numInterval],
                    DataATFM = new int[numInterval],
                    DataATSMSG = new int[numInterval],
                    DataSUR = new int[numInterval],
                    DataPassed = new int[numInterval],
                    DataKickoffDelay = new int[numInterval],
                    FlightLists = new List<FlightData>[numInterval],
                    Capacities = new int[numInterval + 1]
                };
                CTOTFlightInput[] ctotFlightInputs = null;
                Dictionary<int, FlightData> idFlightData = new Dictionary<int, FlightData>();
                HashSet<string> onlyAdepSet = gdp.OnlyADEP != null ? gdp.OnlyADEP.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                HashSet<string> onlyAdesSet = gdp.OnlyADES != null ? gdp.OnlyADES.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                HashSet<string> onlyAirlineSet = gdp.OnlyAirlines != null ? gdp.OnlyAirlines.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();

                if (gdp.TrafficAreaId == 1)
                {
                    Airport airport = await _context.Airport.FirstOrDefaultAsync(a => a.DESIGNATOR == gdp.Point && !a.ROWDELETE);
                    if (airport == null) return new GDPChart { Title = "Airport <" + gdp.Point + "> is not found." };
                    if (!gdp.IsExecuted)
                    {
                        flights = await _context.Flight.Where(f => !f.IsCancelled && f.AirportArrival == gdp.Point &&
                        ((f.ALDT != null && f.ALDT >= gdp.StartTime && f.ALDT < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS != null && f.ELDTByTMCS >= gdp.StartTime && f.ELDTByTMCS < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep != null && f.ELDTByDep >= gdp.StartTime && f.ELDTByDep < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT != null && f.CLDT >= gdp.StartTime && f.CLDT < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT == null && f.ELDT != null && f.ELDT >= gdp.StartTime && f.ELDT < gdp.EndRecoveryTime))).ToListAsync();
                        if (gdp.OnlyADEP != null) flights = flights.Where(f => onlyAdepSet.Any(s => f.AirportDeparture.StartsWith(s))).ToList();
                        if (gdp.OnlyAirlines != null) flights = flights.Where(f => onlyAirlineSet.Any(s => f.Callsign.StartsWith(s))).ToList();
                        if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                        {
                            if (trafficDemand.IsIFR) flights = flights.Where(f => f.FlightRule == null || f.FlightRule.Equals("I") || f.FlightRule.Equals("Y")).ToList();
                            else flights = flights.Where(f => f.FlightRule == null || f.FlightRule.Equals("V") || f.FlightRule.Equals("Z")).ToList();
                        }
                        ctotFlightInputs = new CTOTFlightInput[flights.Count];
                        for (int i = 0; i < ctotFlightInputs.Length; i++)
                        {
                            ctotFlightInputs[i] = new CTOTFlightInput
                            {
                                SectorTrajectory = Array.Empty<SectorTrajectory>(),
                                WaypointTrajectory = Array.Empty<WaypointTrajectory>()
                            };
                        }
                        ctotMeasures[0].TrafficAreaType = "ArrivalAirport";
                    }
                }
                else if (gdp.TrafficAreaId == 2)
                {
                    Fix fix = await _context.Fix.FirstOrDefaultAsync(a => a.DESIGNATOR == gdp.Point && !a.ROWDELETE);
                    if (fix == null) return new GDPChart { Title = "Fix <" + gdp.Point + "> is not found." };
                    int radiusNm = gdp.RadiusNm != null ? gdp.RadiusNm.Value : fix.RadiusNm;
                    double radius = radiusNm / 0.0006213712;
                    Geometry circle = await _context.Fix.Where(f => f.ID == fix.ID).Select(f => f.GEOPOINT.Buffer(radius)).FirstOrDefaultAsync();
                    int minLevel = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : fix.MinLevelFt;
                    int maxLevel = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : fix.MaxLevelFt;
                    List<FlightTrajectory> trajectoryList = new List<FlightTrajectory>();
                    if (gdp.IsExecuted) trajectoryList = await _context.FlightTrajectory.Where(t => flightIds.Contains(t.FlightId) && t.IsActive).Include(f => f.Flight).ToListAsync();
                    else
                    {
                        trajectoryList = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                        if (gdp.OnlyADEP != null) trajectoryList = trajectoryList.Where(t => onlyAdepSet.Any(s => t.Flight.AirportDeparture.StartsWith(s))).ToList();
                        if (gdp.OnlyADES != null) trajectoryList = trajectoryList.Where(t => onlyAdesSet.Any(s => t.Flight.AirportArrival.StartsWith(s))).ToList();
                        if (gdp.OnlyAirlines != null) trajectoryList = trajectoryList.Where(t => onlyAirlineSet.Any(s => t.Flight.Callsign.StartsWith(s))).ToList();
                        if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                        {
                            if (trafficDemand.IsIFR) trajectoryList = trajectoryList.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("I") || t.Flight.FlightRule.Equals("Y")).ToList();
                            else trajectoryList = trajectoryList.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("V") || t.Flight.FlightRule.Equals("Z")).ToList();
                        }
                    }
                    TDChart tdChart = new TDChart
                    {
                        TrafficDemand = trafficDemand,
                        Title = title,
                        Labels = new string[numInterval],
                        Titles = new string[numInterval],
                        DataFPL = new int[numInterval],
                        DataATFM = new int[numInterval],
                        DataATSMSG = new int[numInterval],
                        DataSUR = new int[numInterval],
                        DataPassed = new int[numInterval],
                        FlightLists = new List<FlightData>[numInterval],
                        Capacities = new int[numInterval + 1]
                    };
                    TrafficDemandController.PrepareFlightLists(trajectoryList, new Geometry[] { circle }, tdChart, gdp.StartTime, gdp.EndRecoveryTime, new int[] { minLevel }, new int[] { maxLevel }, formatLabel);
                    for (int i = 0; i < numInterval; i++)
                    {
                        for (int j = 0; j < tdChart.FlightLists[i].Count; j++)
                        {
                            FlightData flightData = tdChart.FlightLists[i][j];
                            if (!gdp.IsExecuted) flights.Add(flightData.Flight);
                            flightDatas.Add(flightData);
                            idFlightData.Add(flightData.Flight.Id, flightData);
                        }
                    }
                    if (!gdp.IsExecuted)
                    {
                        ctotFlightInputs = new CTOTFlightInput[flightDatas.Count];
                        for (int i = 0; i < ctotFlightInputs.Length; i++)
                        {
                            ctotFlightInputs[i] = new CTOTFlightInput
                            {
                                SectorTrajectory = Array.Empty<SectorTrajectory>(),
                                WaypointTrajectory = new WaypointTrajectory[]
                                {
                                    new WaypointTrajectory
                                    {
                                        WaypointName = gdp.Point,
                                        ETO = JsonConvert.SerializeObject(flightDatas[i].TO.Value).Trim('"')
                                    }
                                }
                            };
                        }
                        ctotMeasures[0].TrafficAreaType = "Waypoint";
                    }
                }
                else
                {
                    List<FlightTrajectory> trajectoryList = new List<FlightTrajectory>();
                    Geometry[] geometries = null;
                    int[] minLevels = null, maxLevels = null;
                    if (gdp.TrafficAreaId == 3)
                    {
                        StaticAirspace[] sectors = await _context.StaticAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();
                        if (sectors == null || sectors.Length == 0) return new GDPChart { Title = "Sector <" + gdp.Point + "> is not found." };
                        minLevels = new int[sectors.Length];
                        maxLevels = new int[sectors.Length];
                        geometries = new Geometry[sectors.Length];
                        for (int i = 0; i < sectors.Length; i++)
                        {
                            minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : sectors[i].LowerLimitFt;
                            maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : sectors[i].UpperLimitFt;
                            geometries[i] = sectors[i].Geography;
                            List<FlightTrajectory> trajectoryListTemp = new List<FlightTrajectory>();
                            if (gdp.IsExecuted) trajectoryListTemp = await _context.FlightTrajectory.Where(t => flightIds.Contains(t.FlightId) && t.IsActive).Include(f => f.Flight).ToListAsync();
                            else
                            {
                                trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                                if (gdp.OnlyADEP != null) trajectoryListTemp = trajectoryListTemp.Where(t => onlyAdepSet.Any(s => t.Flight.AirportDeparture.StartsWith(s))).ToList();
                                if (gdp.OnlyADES != null) trajectoryListTemp = trajectoryListTemp.Where(t => onlyAdesSet.Any(s => t.Flight.AirportArrival.StartsWith(s))).ToList();
                                if (gdp.OnlyAirlines != null) trajectoryListTemp = trajectoryListTemp.Where(t => onlyAirlineSet.Any(s => t.Flight.Callsign.StartsWith(s))).ToList();
                                if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                                {
                                    if (trafficDemand.IsIFR) trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("I") || t.Flight.FlightRule.Equals("Y")).ToList();
                                    else trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("V") || t.Flight.FlightRule.Equals("Z")).ToList();
                                }
                            }
                            trajectoryList.AddRange(trajectoryListTemp);
                        }
                    }
                    else if (gdp.TrafficAreaId == 4)
                    {
                        UserDefinedAirspace[] airspaces = await _context.UserDefinedAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();
                        if (airspaces == null || airspaces.Length == 0) return new GDPChart { Title = "Airspace <" + gdp.Point + "> is not found." };
                        minLevels = new int[airspaces.Length];
                        maxLevels = new int[airspaces.Length];
                        geometries = new Geometry[airspaces.Length];
                        for (int i = 0; i < airspaces.Length; i++)
                        {
                            minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : airspaces[i].LowerLimitFt;
                            maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : airspaces[i].UpperLimitFt;
                            geometries[i] = airspaces[i].Geography;
                            List<FlightTrajectory> trajectoryListTemp = new List<FlightTrajectory>();
                            if (gdp.IsExecuted) trajectoryListTemp = await _context.FlightTrajectory.Where(t => flightIds.Contains(t.FlightId) && t.IsActive).Include(f => f.Flight).ToListAsync();
                            else
                            {
                                trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                                if (gdp.OnlyADEP != null) trajectoryListTemp = trajectoryListTemp.Where(t => onlyAdepSet.Any(s => t.Flight.AirportDeparture.StartsWith(s))).ToList();
                                if (gdp.OnlyADES != null) trajectoryListTemp = trajectoryListTemp.Where(t => onlyAdesSet.Any(s => t.Flight.AirportArrival.StartsWith(s))).ToList();
                                if (gdp.OnlyAirlines != null) trajectoryListTemp = trajectoryListTemp.Where(t => onlyAirlineSet.Any(s => t.Flight.Callsign.StartsWith(s))).ToList();
                                if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                                {
                                    if (trafficDemand.IsIFR) trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("I") || t.Flight.FlightRule.Equals("Y")).ToList();
                                    else trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("V") || t.Flight.FlightRule.Equals("Z")).ToList();
                                }
                            }
                            trajectoryList.AddRange(trajectoryListTemp);
                        }
                    }
                    TDChart tdChart = new TDChart
                    {
                        TrafficDemand = trafficDemand,
                        Title = title,
                        Labels = new string[numInterval],
                        Titles = new string[numInterval],
                        DataFPL = new int[numInterval],
                        DataATFM = new int[numInterval],
                        DataATSMSG = new int[numInterval],
                        DataSUR = new int[numInterval],
                        DataPassed = new int[numInterval],
                        FlightLists = new List<FlightData>[numInterval],
                        Capacities = new int[numInterval + 1]
                    };
                    TrafficDemandController.PrepareFlightLists(trajectoryList, geometries, tdChart, gdp.StartTime, gdp.EndRecoveryTime, minLevels, maxLevels, formatLabel);
                    for (int i = 0; i < numInterval; i++)
                    {
                        for (int j = 0; j < tdChart.FlightLists[i].Count; j++)
                        {
                            FlightData flightData = tdChart.FlightLists[i][j];
                            if (!gdp.IsExecuted) flights.Add(flightData.Flight);
                            flightDatas.Add(flightData);
                            idFlightData.Add(flightData.Flight.Id, flightData);
                        }
                    }
                    if (!gdp.IsExecuted)
                    {
                        ctotFlightInputs = new CTOTFlightInput[flights.Count];
                        for (int i = 0; i < ctotFlightInputs.Length; i++)
                        {
                            ctotFlightInputs[i] = new CTOTFlightInput
                            {
                                SectorTrajectory = new SectorTrajectory[]
                                {
                                    new SectorTrajectory
                                    {
                                        SectorName = gdp.Point,
                                        InboundTime = JsonConvert.SerializeObject(flightDatas[i].INB.Value).Trim('"')
                                    }
                                },
                                WaypointTrajectory = Array.Empty<WaypointTrajectory>()
                            };
                        }
                        ctotMeasures[0].TrafficAreaType = "Sector";
                    }
                }
                if (!gdp.IsExecuted)
                {
                    HashSet<string> exemptAdepSet = gdp.ExemptADEP != null ? gdp.ExemptADEP.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                    HashSet<string> exemptAdesSet = gdp.ExemptADES != null ? gdp.ExemptADES.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                    HashSet<string> exemptAirlineSet = gdp.ExemptAirlines != null ? gdp.ExemptAirlines.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                    for (int i = 0; i < ctotFlightInputs.Length; i++)
                    {
                        Flight flight = flights[i];
                        ctotFlightInputs[i].FlightId = flight.Id;
                        ctotFlightInputs[i].IsExcluded = false;
                        ctotFlightInputs[i].IsExempted = flight.ATOT.HasValue || flight.ALDT.HasValue || exemptAdepSet.Any(s => flight.AirportDeparture.StartsWith(s)) || exemptAdesSet.Any(s => flight.AirportArrival.StartsWith(s)) || exemptAirlineSet.Any(s => flight.Callsign.StartsWith(s));
                        ctotFlightInputs[i].IsManaged = false;
                        ctotFlightInputs[i].ArrivalAirport = flight.AirportArrival;
                        ctotFlightInputs[i].DepartureAirport = flight.AirportDeparture;
                        ctotFlightInputs[i].ETOT = JsonConvert.SerializeObject(flight.ATOT ?? (flight.CTOT ?? flight.ETOT.Value)).Trim('"');
                        ctotFlightInputs[i].ELDT = JsonConvert.SerializeObject(flight.ALDT ?? (flight.ELDTByTMCS ?? (flight.ELDTByDep ?? (flight.CLDT ?? flight.ELDT.Value)))).Trim('"');
                    }
                    CTOTOptimizeInput input = new CTOTOptimizeInput
                    {
                        Measures = ctotMeasures,
                        Algorithm = ctotAlgorithm,
                        Flights = ctotFlightInputs
                    };
                    CTOTOptimizeOutput ctotOptimizeOutput =  await GetCtotFlightsAsync(input);
                    if (ctotOptimizeOutput == null) return new GDPChart { Title = "Optimizer Cannot Resolve CTOT. (Output = null)" };
                    if (ctotOptimizeOutput.LogMessage != null && ctotOptimizeOutput.LogMessage.Length > 0 && ctotOptimizeOutput.LogMessage[0].StartsWith("[ERROR]")) return new GDPChart { Title = ctotOptimizeOutput.LogMessage[0] };
                    if (ctotOptimizeOutput.GroundDelayedFlights == null) return new GDPChart { Title = "Optimizer Cannot Resolve CTOT. (GroundDelayedFlights = null)" };
                    for (int i = 0; i < ctotOptimizeOutput.GroundDelayedFlights.Length; i++)
                    {
                        CTOTGroundDelayedFlight ctotFlight = ctotOptimizeOutput.GroundDelayedFlights[i];
                        Flight flight = flights.Where(f => f.Id == ctotFlight.FlightId).FirstOrDefault();
                        if (!(flight.ATOT.HasValue || flight.ALDT.HasValue))
                        {
                            flight.COBT = flight.EOBT.Value.AddMinutes(ctotFlight.GroundDelay);
                            flight.CTOT = flight.ETOT.Value.AddMinutes(ctotFlight.GroundDelay);
                            flight.CLDT = flight.ELDT.Value.AddMinutes(ctotFlight.GroundDelay);
                            flight.CIBT = flight.EIBT.Value.AddMinutes(ctotFlight.GroundDelay);
                            if (gdp.TrafficAreaId != 1)
                            {
                                FlightData flightData = idFlightData.GetValueOrDefault(ctotFlight.FlightId);
                                flightData.Flight = flight;
                                FlightData.AddGroundDelayFlightData(flightData, ctotFlight.GroundDelay);
                            }
                        }
                    }
                }
                DateTime startTime = gdp.StartTime, endTime;
                string formatFlightData = FlightData.FORMAT_TIME;
                for (int i = 0; i < numInterval; i++)
                {
                    gdpChart.Labels[i] = startTime.ToString(formatLabel);
                    endTime = startTime.AddMinutes(gdp.IntervalMin);
                    if (endTime > gdp.EndRecoveryTime) endTime = gdp.EndRecoveryTime;
                    gdpChart.Titles[i] = "Regulated Demand <" + gdp.Point + "> (" + startTime.ToString(FlightData.FORMAT_DATETIME_LONG) + " - ";
                    if (startTime.Date.Equals(endTime.Date)) gdpChart.Titles[i] += endTime.ToString(FlightData.FORMAT_TIME) + ")";
                    else
                    {
                        formatFlightData = FlightData.FORMAT_DATETIME_SHORT;
                        gdpChart.Titles[i] += endTime.ToString(FlightData.FORMAT_DATETIME_LONG) + ")";
                    }
                    gdpChart.FlightLists[i] = new List<FlightData>();
                    if (gdp.TrafficAreaId == 1)
                    {
                        List<Flight> flightList = flights.Where(f => f.ALDT != null && f.ALDT >= startTime && f.ALDT < endTime).ToList();
                        gdpChart.DataPassed[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            gdpChart.FlightLists[i].Add(FlightData.GetFlightData(flight, 7, null, formatFlightData));
                        }
                        flightList = flights.Where(f => f.ALDT == null && f.ELDTByTMCS != null && f.ELDTByTMCS >= startTime && f.ELDTByTMCS < endTime).ToList();
                        gdpChart.DataSUR[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            gdpChart.FlightLists[i].Add(FlightData.GetFlightData(flight, 6, null, formatFlightData));
                        }
                        flightList = flights.Where(f => f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep != null && f.ELDTByDep >= startTime && f.ELDTByDep < endTime).ToList();
                        gdpChart.DataATSMSG[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            gdpChart.FlightLists[i].Add(FlightData.GetFlightData(flight, 4, null, formatFlightData));
                        }
                        flightList = flights.Where(f => f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT != null && f.CLDT >= startTime && f.CLDT < endTime).ToList();
                        gdpChart.DataATFM[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            gdpChart.FlightLists[i].Add(FlightData.GetFlightData(flight, 3, null, formatFlightData));
                        }
                        flightList = flights.Where(f => f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT == null && f.ELDT != null && f.ELDT >= startTime && f.ELDT < endTime).ToList();
                        gdpChart.DataFPL[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            gdpChart.FlightLists[i].Add(FlightData.GetFlightData(flight, 2, null, formatFlightData));
                        }
                        gdpChart.FlightLists[i].Sort((x, y) => x.LDT.Value.CompareTo(y.LDT.Value));
                    }
                    else if (gdp.TrafficAreaId == 2)
                    {
                        List<FlightData> flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 7 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataPassed[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 6 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataSUR[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 4 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataATSMSG[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 3 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataATFM[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 2 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataFPL[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        gdpChart.FlightLists[i].Sort((x, y) => x.TO.Value.CompareTo(y.TO.Value));
                    }
                    else
                    {
                        List<FlightData> flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 7 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataPassed[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 6 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataSUR[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 4 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataATSMSG[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 3 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataATFM[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 2 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataFPL[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        gdpChart.FlightLists[i].Sort((x, y) => x.INB.Value.CompareTo(y.INB.Value));
                    }
                    double capacity = (startTime < gdp.EndTime) ? gdp.CapacityPerHr : gdp.CapacityRecoveryPerHr;
                    if (startTime < gdp.EndTime && endTime > gdp.EndTime)
                    {
                        capacity = ((gdp.CapacityPerHr * (gdp.EndTime - startTime).TotalHours) + (gdp.CapacityRecoveryPerHr * (endTime - gdp.EndTime).TotalHours)) / (endTime - startTime).TotalHours;
                    }
                    gdpChart.Capacities[i] = (int)Math.Round(capacity * ((double)gdp.IntervalMin / 60.0));
                    startTime = endTime;
                }
                gdpChart.Capacities[numInterval] = gdpChart.Capacities[numInterval - 1];

                // Kick-off Delayed Flights (beyond recovery period)
                if (gdp.TrafficAreaId == 1)
                {
                    List<Flight> flightList = flights.Where(f => f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT != null && f.CLDT >= startTime).ToList();
                    gdpChart.DataKickoffDelay[numInterval - 1] = flightList.Count;
                    foreach (var flight in flightList)
                    {
                        FlightData flightData = FlightData.GetFlightData(flight, 3, null, formatFlightData);
                        flightData.IsKickoffDelay = true;
                        gdpChart.FlightLists[numInterval - 1].Add(flightData);
                    }
                }
                else if (gdp.TrafficAreaId == 2)
                {
                    List<FlightData> flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 3 && f.TO >= startTime).ToList();
                    gdpChart.DataKickoffDelay[numInterval - 1] = flightDataList.Count;
                    foreach (FlightData flightData in flightDataList) flightData.IsKickoffDelay = true;
                    gdpChart.FlightLists[numInterval - 1].AddRange(flightDataList);
                }
                else
                {
                    List<FlightData> flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 3 && f.INB >= startTime).ToList();
                    gdpChart.DataKickoffDelay[numInterval - 1] = flightDataList.Count;
                    foreach (FlightData flightData in flightDataList) flightData.IsKickoffDelay = true;
                    gdpChart.FlightLists[numInterval - 1].AddRange(flightDataList);
                }

                gdpChart.Time = DateTime.UtcNow.ToString("HH:mm:ss");
                return gdpChart;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return new GDPChart { Title = "Server Problem: GDP/RequestChart" };
            }
        }

        [HttpPost]
        public async Task<GDPChart> Create([Bind("Designator,TrafficAreaId,TrafficArea,Point,LowerFlightLevel,UpperFlightLevel,RadiusNm,CapacityPerHr,CapacityRecoveryPerHr,StartTime,EndTime,EndRecoveryTime,OnlyADEP,OnlyADES,OnlyAirlines,ExemptADEP,ExemptADES,ExemptAirlines,IntervalMin,IsIFR,IsVFR")] GDP gdp)
        {
            try
            {
                if (ModelState.IsValid && gdp.IsValid())
                {
                    gdp.MakeValid();
                    gdp.TimeSaved = DateTime.UtcNow;
                    TrafficArea trafficArea = gdp.TrafficArea;
                    gdp.TrafficArea = null;
                    _context.Add(gdp);
                    await _context.SaveChangesAsync();
                    gdp.TrafficArea = trafficArea;
                    GDPChart gdpChart = new GDPChart
                    {
                        GDP = gdp,
                        Time = DateTime.UtcNow.ToString("HH:mm:ss")
                    };
                    return gdpChart;
                }
                return new GDPChart();
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return new GDPChart { Title = "Server Problem: GDP/Create" + e.ToString() };
            }
        }

        /********************* Helper Methods *********************/

        private static async Task<CTOTOptimizeOutput> GetCtotFlightsAsync(CTOTOptimizeInput input)
        {
            try
            {
                // check airport db
                /*CTOTAirports ctotAirports = null;
                HttpResponseMessage response = await client.GetAsync("airports");
                string test = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode) ctotAirports = await response.Content.ReadAsAsync<CTOTAirports>();
                if (ctotAirports != null && (ctotAirports.Airports == null || ctotAirports.Airports.Length == 0))
                {
                }
                return null;*/

                // upsert airport db
                /*Airport[] airports = await _context.Airport.Where(a => !a.ROWDELETE).ToArrayAsync();
                List<CTOTAirport> ctotAirportList = new List<CTOTAirport>();
                for (int i = 0; i < airports.Length; i++)
                {
                    CTOTAirport ctotAirport = new CTOTAirport();
                    ctotAirport.AirportId = airports[i].ID;
                    ctotAirport.AirportName = airports[i].DESIGNATOR;
                    Capacity capacity = await _context.Capacity.Where(c => c.TrafficAreaId == 1 && c.Point == airports[i].DESIGNATOR && c.IsArr).FirstOrDefaultAsync();
                    if (capacity == null) continue;
                    ctotAirport.DefaultArrivalCapacity = capacity.CapacityPerHr;
                    capacity = await _context.Capacity.Where(c => c.TrafficAreaId == 1 && c.Point == airports[i].DESIGNATOR && c.IsDep).FirstOrDefaultAsync();
                    if (capacity == null) continue;
                    ctotAirport.DefaultDepartureCapacity = capacity.CapacityPerHr;
                    ctotAirportList.Add(ctotAirport);
                }
                CTOTAirports ctotAirports = new CTOTAirports { Airports = ctotAirportList.ToArray() };
                HttpResponseMessage response = await client.PostAsJsonAsync("airports", ctotAirports);
                string test = await response.Content.ReadAsStringAsync();
                if (response.IsSuccessStatusCode) return null;
                return null;*/
                HttpClient client = new HttpClient { BaseAddress = new Uri(ATFMOptimizerIP) };
                for (int i = 0; i < input.Measures.Length; i++)
                {
                    if (input.Measures[i].MeasureId == -1) input.Measures[i].MeasureId = 0;
                }
                CTOTOptimizeOutput output = null;
                HttpResponseMessage response = await client.PostAsJsonAsync("optimizer", input);
                if (response.IsSuccessStatusCode) 
                    output = await response.Content.ReadAsAsync<CTOTOptimizeOutput>();
                return output;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return null;
            }
        }
    }
}
