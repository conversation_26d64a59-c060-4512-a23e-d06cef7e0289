﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using ATFAS.Areas.Identity.Data;
using ATFAS.Data;
using ATFAS.ViewModels;
using DataLayer.EfCode;
using DataLayer.ExtraAuthClasses;
using FeatureAuthorize.PolicyCode;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using PermissionParts;
using ServiceLayer.UserServices;
using StatusGeneric;
using ATFAS.Controllers;
using CsvHelper;
using System.IO;
using CsvHelper.Configuration;
using CsvHelper.Configuration.Attributes;
using System.Globalization;
using Microsoft.AspNetCore.Authorization;
using ATFAS.Models;

namespace ATFAS.Controllers
{
    public class UserController : Controller
    {
        private readonly ATFASContext _atfasContext;
        private readonly ExtraAuthorizeDbContext _extraContext;
        private readonly UserManager<AppUser> _userManager;
        private readonly IConfiguration _configuration;

        public UserController(ATFASContext atfasContext, ExtraAuthorizeDbContext extraContext,
            UserManager<AppUser> userManager, IConfiguration configuration)
        {
            _atfasContext = atfasContext;
            _extraContext = extraContext;
            _userManager = userManager;
            _configuration = configuration;
        }

        // GET: UserController1
        [HasPermission(Permissions.UserRead)]
        public ActionResult Index()
        {
            List<UserRoleViewModel> userRoleViewModels = new List<UserRoleViewModel>();
            foreach (AppUser user in _userManager.Users
                .Where(x => x.EmailConfirmed && x.UserName != "superadmin"))
            {
                UserRoleViewModel userRoleViewModel = new UserRoleViewModel
                {
                    User = user,
                    UserToRoles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(user.Id)).Select(x => x.RoleName).ToList()
                };
                userRoleViewModels.Add(userRoleViewModel);
            }
            return View(userRoleViewModels);
        }

        // GET: UserController1/Details/5
        [HasPermission(Permissions.UserRead)]
        public ActionResult Details(string id)
        {
            UserRoleViewModel userRoleViewModel = new UserRoleViewModel
            {
                User = _userManager.Users.Where(x => x.Id.Equals(id)).SingleOrDefault(),
                UserToRoles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(id)).Select(x => x.RoleName).ToList()
            };
            return View(userRoleViewModel);
        }

        // GET: UserController1/Create
        [HasPermission(Permissions.UserChange)]
        public ActionResult Create()
        {
            List<string> allRoles = _extraContext.RolesToPermissions.OrderBy(x => x.RoleName).Select(x => x.RoleName).ToList();
            UserRoleViewModel userRoleViewModel = new UserRoleViewModel(new AppUser(), new List<string>(), allRoles);
            return View(userRoleViewModel);
        }

        // POST: UserController1/Create
        [HttpPost]
        [ValidateAntiForgeryToken]
        [HasPermission(Permissions.UserChange)]
        public async Task<ActionResult> Create(UserRoleViewModel data)
        {

            try
            {
                if (ModelState.IsValid)
                {
                    AppUser newUser = new AppUser
                    {
                        UserName = data.User.UserName,
                        FirstName = data.User.FirstName,
                        LastName = data.User.LastName,
                        Email = data.User.Email,
                        EmailConfirmed = true,
                        PhoneNumber = data.User.PhoneNumber
                    };
                    string defaultPassword = _configuration.GetSection("NewUserDefaultPassword").Value;
                    IdentityResult result = await _userManager.CreateAsync(newUser, defaultPassword);
                    if (result.Succeeded)
                    {
                        UpdateUserRole(newUser.Id, data.UserToRolesCheckBox, _extraContext);
                        _extraContext.SaveChanges();

                        return RedirectToAction(nameof(Index));
                    }
                    foreach (var error in result.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                }
                data.RegenerateCheckBoxUserToRoles();
                return View(data);
            }
            catch (Exception e)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, e);
            }
        }

        [HasPermission(Permissions.UserChange)]
        public ActionResult PendingUser()
        {
            List<UserRoleViewModel> userRoleViewModels = new List<UserRoleViewModel>();
            foreach (AppUser user in _userManager.Users.Where(x => !x.EmailConfirmed))
            {
                UserRoleViewModel userRoleViewModel = new UserRoleViewModel
                {
                    User = user,
                    UserToRoles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(user.Id)).Select(x => x.RoleName).ToList()
                };
                userRoleViewModels.Add(userRoleViewModel);
            }
            return View(userRoleViewModels);
        }

        [HasPermission(Permissions.UserChange)]
        public ActionResult ApproveUser(string id)
        {
            AppUser user = _userManager.Users.Where(x => x.Id.Equals(id)).SingleOrDefault();
            List<string> userToRoles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(id)).Select(x => x.RoleName).ToList();
            List<string> allRoles = _extraContext.RolesToPermissions.OrderBy(x => x.RoleName).Select(x => x.RoleName).ToList();
            UserRoleViewModel userRoleViewModel = new UserRoleViewModel(user, userToRoles, allRoles);
            return View("Edit", userRoleViewModel);
        }

        // GET: UserController1/Edit/5
        [HasPermission(Permissions.UserChange)]
        public ActionResult Edit(string id)
        {
            AppUser user = _userManager.Users.Where(x => x.Id.Equals(id)).SingleOrDefault();
            if (user == null || user.UserName.Equals("superadmin"))
            {
                return NotFound();
            }
            List<string> userToRoles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(id)).Select(x => x.RoleName).ToList();
            List<string> allRoles = _extraContext.RolesToPermissions.OrderBy(x => x.RoleName).Select(x => x.RoleName).ToList();
            UserRoleViewModel userRoleViewModel = new UserRoleViewModel(user, userToRoles, allRoles);
            return View(userRoleViewModel);
        }

        // POST: UserController1/Edit/5
        // User for both Edit user and approve user
        [HttpPost]
        [ValidateAntiForgeryToken]
        [HasPermission(Permissions.UserChange)]
        public async Task<ActionResult> Edit(string id, UserRoleViewModel data)
        {
            try
            {
                ClaimsIdentity claim = new ClaimsIdentity(new[]
                {
                    new Claim(ClaimTypes.Name, data.User.UserName),
                    new Claim(ClaimTypes.NameIdentifier, data.User.Id.ToString())
                });
                AppUser user = await _userManager.GetUserAsync(new ClaimsPrincipal(claim));

                if (user == null || user.UserName.Equals("superadmin"))
                {
                    return NotFound();
                }

                if (ModelState.IsValid)
                {
                    // If user not confirm email, confirm
                    string returnView = nameof(Index);
                    if (!user.EmailConfirmed)
                    {
                        user.EmailConfirmed = true;
                        returnView = nameof(PendingUser);
                    }

                    user.FirstName = data.User.FirstName;
                    user.LastName = data.User.LastName;
                    user.Email = data.User.Email;
                    user.PhoneNumber = data.User.PhoneNumber;

                    IdentityResult result = await _userManager.UpdateAsync(user);
                    if (result.Succeeded)
                    {
                        UpdateUserRole(user.Id, data.UserToRolesCheckBox, _extraContext);
                        _extraContext.SaveChanges();

                        /*Add ADP Subscription in User Profiles
                        AddUsersubscription(user);*/

                        return RedirectToAction(returnView);
                    }
                    foreach (var error in result.Errors)
                    {
                        ModelState.AddModelError(string.Empty, error.Description);
                    }
                }
                data.RegenerateCheckBoxUserToRoles();
                data.User.EmailConfirmed = user.EmailConfirmed;
                return View(data);
            }
            catch (Exception e)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, e);
            }
        }
        private void AddUsersubscription(AppUser user)
        {
            //Add ADP Subscription in User Profiles
            UserProfileController userprofile = new UserProfileController(_userManager, _atfasContext, _extraContext);
            userprofile.CreateOrUpdateProfile("Subscribe ADP", "Subscribe to ADP E-mails", user.Id, true);
            userprofile.CreateOrUpdateProfile("Subscribe CTOT AFTN", "Subscribe to CTOT AFTN Messages", user.Id, false);
            userprofile.CreateOrUpdateProfile("Subscribe CTOT Email", "Subscribe to CTOT E-mails", user.Id, false);
            userprofile.CreateOrUpdateProfile("User AFTN Addresses", "", user.Id, false);
        }

        private void UpdateUserRole(string userId, List<CheckBoxListItem> checkBoxListItem,
            ExtraAuthorizeDbContext context)
        {
            foreach (CheckBoxListItem role in checkBoxListItem)
            {
                if (role.IsChecked)
                {
                    IStatusGeneric<UserToRole> status =
                        UserToRole.AddRoleToUser(userId, role.Value, _extraContext);
                    if (status.IsValid)
                    {
                        context.Add(status.Result);
                    }
                }
                else
                {
                    IStatusGeneric<UserToRole> status =
                        UserToRole.RemoveRoleToUser(userId, role.Value, _extraContext);
                    if (status.IsValid)
                    {
                        context.Remove(status.Result);
                    }
                }
            }
        }

        // GET: UserController1/Delete/5
        [HasPermission(Permissions.UserChange)]
        public ActionResult Delete(string id, string returnAction = null)
        {
            UserRoleViewModel userRoleViewModel = new UserRoleViewModel
            {
                User = _userManager.Users.Where(x => x.Id.Equals(id)).SingleOrDefault(),
                UserToRoles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(id)).Select(x => x.RoleName).ToList()
            };
            if (userRoleViewModel.User == null || userRoleViewModel.User.UserName.Equals("superadmin"))
            {
                return NotFound();
            }
            ViewData["returnAction"] = returnAction;
            return View(userRoleViewModel);
        }

        // POST: UserController1/Delete/5
        [HttpPost]
        [ValidateAntiForgeryToken]
        [HasPermission(Permissions.UserChange)]
        public async Task<ActionResult> Delete(string id, IFormCollection collection)
        {
            try
            {
                string returnView;
                AppUser user = _userManager.Users.Where(x => x.Id.Equals(id)).SingleOrDefault();
                if (user == null || user.UserName.Equals("superadmin"))
                {
                    return NotFound();
                }
                if (!user.EmailConfirmed)
                {
                    returnView = nameof(PendingUser);
                }
                else
                {
                    returnView = nameof(Index);
                }
                IdentityResult result = await _userManager.DeleteAsync(user);
                if (result.Succeeded)
                {
                    foreach (UserToRole userToRole in _extraContext.UserToRoles.Where(x => x.UserId.Equals(id)))
                    {
                        IStatusGeneric<UserToRole> status = UserToRole
                            .RemoveRoleToUser(id, userToRole.RoleName, _extraContext);
                        if (status.IsValid)
                        {
                            _extraContext.Remove(status.Result);
                        }
                    }
                    _extraContext.SaveChanges();
                    /*auto unsubscribe all*/
                    foreach (UserProfile profile in _atfasContext.UserProfile.Where(s => s.UserId.Equals(id)))
                    {
                        profile.IsActive = false;
                        _atfasContext.Update(profile);
                    }
                }
                return RedirectToAction(returnView);
            }
            catch (Exception e)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, e);
            }
        }

        [HasPermission(Permissions.UserChange)]
        public ActionResult ResetPassword(string id)
        {
            UserRoleViewModel userRoleViewModel = new UserRoleViewModel
            {
                User = _userManager.Users.Where(x => x.Id.Equals(id)).SingleOrDefault(),
                UserToRoles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(id)).Select(x => x.RoleName).ToList()
            };
            if (userRoleViewModel.User == null || userRoleViewModel.User.UserName.Equals("superadmin")
                || userRoleViewModel.User.PasswordHash == null)
            {
                return NotFound();
            }
            return View(userRoleViewModel);
        }

        [HttpPost]
        [ValidateAntiForgeryToken]
        [HasPermission(Permissions.UserChange)]
        public async Task<ActionResult> ResetPassword(string id, IFormCollection collection)
        {
            try
            {
                AppUser user = _userManager.Users.Where(x => x.Id.Equals(id)).SingleOrDefault();
                if (user == null || user.UserName.Equals("superadmin"))
                {
                    return NotFound();
                }
                string defaultPassword = _configuration.GetSection("NewUserDefaultPassword").Value;
                string token = await _userManager.GeneratePasswordResetTokenAsync(user);
                IdentityResult result = await _userManager.ResetPasswordAsync(user, token, defaultPassword);
                return RedirectToAction(nameof(Index));
            }
            catch (Exception e)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, e);
            }
        }

        [HttpGet]
        [HasPermission(Permissions.UserChange)]
        public ActionResult ExportCSV()
        {
            List<ReportCSVModel> reportCsvModels = new List<ReportCSVModel>();
            var userProfiles = _atfasContext.UserProfile.ToList();
            foreach (AppUser user in _userManager.Users
                .Where(x => x.EmailConfirmed && x.UserName != "superadmin"))
            {
                var airportCode = userProfiles.Where(u => u.UserId.Equals(user.Id) && u.ProfileName.Contains("User Airline Codes")).Select(u=>u.ProfileValue).FirstOrDefault();
                var airlineCode = userProfiles.Where(u => u.UserId.Equals(user.Id) && u.ProfileName.Contains("User Airport Codes")).Select(u => u.ProfileValue).FirstOrDefault();
                var aftnAddress = userProfiles.Where(u => u.UserId.Equals(user.Id) && u.ProfileName.Contains("User AFTN Addresses")).Select(u => u.ProfileValue).FirstOrDefault();
                ReportCSVModel reportCsvModel = new ReportCSVModel
                {
                    Username = user.UserName,
                    Firstname = user.FirstName,
                    Lastname = user.LastName,
                    Email = user.Email,
                    Phone= user.PhoneNumber,
                    Roles = String.Join(",",_extraContext.UserToRoles.Where(x => x.UserId.Equals(user.Id)).Select(x => x.RoleName).ToList()),
                    AirlineCode = (airlineCode == null) ? "" : airlineCode,
                    AirportCode = (airportCode == null) ? "": airportCode,
                    AFTNAddress = (aftnAddress == null) ? "": aftnAddress               
                };
                reportCsvModels.Add(reportCsvModel);
            }

            byte[] result;
            var stream = new MemoryStream();
            using (var writeFile = new StreamWriter(stream, leaveOpen: true))
            {
                var csv = new CsvWriter(writeFile, CultureInfo.InvariantCulture, true);
                csv.Context.RegisterClassMap<ReportCSVMap>();
                csv.WriteRecords(reportCsvModels);
                writeFile.Flush();
                result = stream.ToArray();
            }
            return new FileStreamResult(new MemoryStream(result), "text/csv") { FileDownloadName = "ATFASUsers.csv" };
        }

        [AllowAnonymous]
        public ActionResult Register()
        {
            ViewBag.PortalUrl = _configuration.GetValue<string>("OpenIdConfig:RegisterPageLink");
            //return View();
            return Redirect(_configuration.GetValue<string>("OpenIdConfig:RegisterPageLink"));
        }
    }
    public class ReportCSVModel
    {
        public string Username { get; set; }
        public string Firstname { get; set; }
        public string Lastname { get; set; }
        public string Email { get; set; }
        //[Name("Phone Number")]
        public string Phone { get; set; }
        public string Roles { get; set; }
        // [Name("Airline Code")]
        public string AirlineCode { get; set; }
        // [Name("Airport Code")]
        public string AirportCode { get; set; }
        //[Name("AFTN Address")]
        public string AFTNAddress { get; set; }

    }
    public class ReportCSVMap : ClassMap<ReportCSVModel>
    {
        public ReportCSVMap()
        {
            Map(m => m.Username).Name("Username");
            Map(m => m.Firstname).Name("First Name");
            Map(m => m.Lastname).Name("Last Name");
            Map(m => m.Email).Name("E-Mail");
            Map(m => m.Phone).Name("Phone Number");
            Map(m => m.Roles).Name("Roles");
            Map(m => m.AirlineCode).Name("Airline Code");
            Map(m => m.AirportCode).Name("Airport Code");
            Map(m => m.AFTNAddress).Name("AFTN Address");
        }

    }
}
