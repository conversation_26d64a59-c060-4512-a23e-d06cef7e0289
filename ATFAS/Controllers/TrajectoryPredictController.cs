﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ATFAS.ViewModels;
using ImportFlightschedLib;
using Microsoft.Extensions.Configuration;
using ATFAS.Data;
using Microsoft.AspNetCore.Http;
using System.IO;
using System.Security.Claims;
using Microsoft.ML;
using Microsoft.ML.Data;
using Microsoft.Data.SqlClient;
using Microsoft.AspNetCore.Hosting;
using ATFAS.LongRunningTask;
using ATFAS.Models;

namespace ATFAS.Controllers
{
    public class TrajectoryPredict : Controller
    {
        private readonly IWebHostEnvironment _hostingEnvironment;
        //private static string _appPath => Path.GetDirectoryName(Environment.GetCommandLineArgs()[0]);
        private readonly IConfiguration _configuration;
        private readonly ATFASContext _context;
        private static MLContext _mlContext;
        private static PredictionEngine<TrajectoryPredictModel, TrajectoryPredictionResult> _predEngine;
        private static ITransformer _trainedModel;
        static IDataView _trainDataView;
       // private static string _modelPath => Path.Combine(_appPath, "..", "..", "..", "Models", "model.zip");

        public TrajectoryPredict(IConfiguration configuration, ATFASContext ctx, IWebHostEnvironment hostingEnvironment)
        {
            _configuration = configuration;
            _context = ctx;
            _hostingEnvironment = hostingEnvironment;
        
    }

       public ActionResult Index()
        {
            var latest = "";
            using (var db = _context)
            {
                var log = db.ActionLog.Where(l => l.EventType == "TrainTrajectoryData").OrderByDescending(l => l.TimeStamp).FirstOrDefault();
                if (log != null)
                    latest = log.TimeStamp.Value.ToString("MM/dd/yyyy HH:mm:ss");

            }
            //ViewBag.SuccessDate = "";
            //ViewBag.Error = "";

            //if (TempData.ContainsKey("success")) ViewBag.SuccessDate = TempData["success"];
            //if (TempData.ContainsKey("error")) ViewBag.Error = TempData["error"];

            ViewBag.latestDate = latest;
            return View();
            /*  _mlContext = new MLContext();
              DatabaseLoader loader = _mlContext.Data.CreateDatabaseLoader<TrajectoryPredictModel>();

              string connectionString = _configuration.GetConnectionString("ATFASContext");
              string sqlCommand = @"SELECT    F.Callsign
                                              ,F.AirportDeparture
                                              ,F.AirportArrival
                                              ,T.AltitudeFt
                                              , CONVERT(varchar(7), DATEPART(dw,StartTime)) AS DayOfaWeek
                                              ,T.Heading
                                              ,T.SpeedKn
                                              ,CONCAT(PositionLine.ToString(),'|',AltitudeFt,'|',Heading,'|',SpeedKn,'|', DATEDIFF(minute, T.StartTime, T.EndTime)) AS PositionLong                                           
                                  FROM FlightTrajectory AS T
                                  INNER JOIN Flight AS F ON T.FlightId = F.Id 
                                  WHERE T.FlightSourceId = 2 ";
              DatabaseSource dbSource = new DatabaseSource(SqlClientFactory.Instance, connectionString, sqlCommand);

              IDataView data = loader.Load(dbSource);
              var split = _mlContext.Data.TrainTestSplit(data, testFraction: 0.1);
              _trainDataView = split.TrainSet;




              var pipeline = ProcessData();
              var trainingPipeline = BuildAndTrainModel(_trainDataView, pipeline);
              Evaluate(_trainDataView.Schema, split.TestSet,_hostingEnvironment);

              return View();*/
        }

        [HttpPost]
        public IActionResult Index(string e)
        {
            string ipaddress = HttpContext.Connection.RemoteIpAddress.ToString();
            var username = User.FindFirstValue(ClaimTypes.Name);
            var status = new ProgressStatus();
            status.Status = "Start Training Trajectory Prediction Data, Please do not close this page !";
            Guid requestId = Guid.NewGuid();
            ProgressTracker.add(requestId, status);

            //Call Long running task
            TrajectoryMLTask myTask = new TrajectoryMLTask(_configuration, _context, _hostingEnvironment, ipaddress, username);
            myTask.RunTask(requestId);

            return RedirectToAction("TaskProgress", new { requestId = requestId.ToString() });
        }


        public IActionResult TaskProgress(string requestId)
        {
            var statusMessage = new ProgressStatus();
          //  if (statusMessage.Status == "done")
              

            if (!string.IsNullOrWhiteSpace(requestId))
            {
                statusMessage = ProgressTracker.getValue(Guid.Parse(requestId));

                //The processing  has not yet finished 
                //Add a refresh header, to refresh the page in 10 seconds.
                if (statusMessage.Status == "done")  
                        createLog();
                else
                    Response.Headers.Add("Refresh", "5");
                return View("TaskProgress", statusMessage);
            }

            statusMessage.Status = "Error: Something went wrong with process";
            return View("TaskProgress", statusMessage);
        }

        public IActionResult Error()
        {
            return View();
        }


        private void createLog()
        {
            // Get the IP  
            string ipaddress = HttpContext.Connection.RemoteIpAddress.ToString();
            var username = User.FindFirstValue(ClaimTypes.Name);

            using (var db = _context)
            {
                db.ActionLog.Add(new ActionLog
                {
                    TimeStamp = DateTime.UtcNow,
                    Username = username,
                    EventType = "TrainTrajectoryData",
                    EventDetails = "Train Data Complete",
                    EventSource = "TrajctoryPredict",
                    Ip = ipaddress
                });

                db.SaveChanges();
            }

        }


      
    }
}
