﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ATFAS.Models;
using ATFAS.Data;

using Microsoft.AspNetCore.Http;
using System.Net.Http.Headers;
using System.IO;
using ATFAS.ViewModels;
using System.Net.Mime;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.AspNetCore.Hosting;
using System.Globalization;
using Microsoft.AspNetCore.Authorization;
using System.Net.Http;
using ATFAS.ActLog;
using System.Security.Claims;
using Microsoft.AspNetCore.Identity;
using ATFAS.Areas.Identity.Data;
using DataLayer.EfCode;
using ATFAS.Controllers;
using NetTopologySuite.Geometries;
using STJ = System.Text.Json.JsonSerializer;


namespace ATFAS.Controllers
{
    [AllowAnonymous]
    public class HomeController : Controller
    {
        private readonly ILogger<HomeController> _logger;
        private readonly ATFASContext _context;
        private IMemoryCache _cache;
        private readonly IWebHostEnvironment _env;
        private readonly ExtraAuthorizeDbContext _extraContext;

        public HomeController(ILogger<HomeController> logger, ATFASContext ctx, ExtraAuthorizeDbContext extraContext, IMemoryCache memoryCache, IWebHostEnvironment env)
        {
            _logger = logger;
            _context = ctx;
            _cache = memoryCache;
            _env = env;
            _extraContext = extraContext;
        }

        public IActionResult Index()
        {
            //ReceiveMail();
            ActionLog("Home - Visit ATFAS Home Page");
            return View();
        }
        private List<AirportTDModel> AirportDefault()
        {
            var airportdefault = _context.GeneralConfiguration.Select(g => g.DefaultSWAirportList).FirstOrDefault();
            return JsonConvert.DeserializeObject<List<AirportTDModel>>(airportdefault);
        }

        [Route("Home/GetTrafficDemandAirportMap")]
        [HttpGet]
        public List<DisplayTDModel> GetTrafficDemandAirportMap()
        {
            List<DisplayTDModel> TDList = new List<DisplayTDModel>();
            var apdefault = AirportDefault();
            // var tdAll = _context.TDSituationAwareness.ToList();
            var tdAll = CacheSituationAwareness();     
            var dtNow = DateTime.UtcNow;
            var hour = dtNow.TimeOfDay.Hours;
            if (tdAll != null && tdAll.Count > 0)
            {
                foreach (var itm in apdefault)
                {
                    DisplayTDModel td = new DisplayTDModel();
                    var index = 0;
                    var tdCustom = tdAll.Where(t => t.Designator == itm.name).FirstOrDefault();
                    if (tdCustom != null)
                    {
                        var times = tdCustom.TimeInterval.Split(',');
                        index = Array.FindIndex(times, t => t == hour.ToString());
                    }
                    td.Designator = itm.name;
                    td.Arrival = (tdCustom == null || tdCustom.Arrival == null || index < 0) ? "0" : tdCustom.Arrival.Split(',')[index];
                    td.Departure = (tdCustom == null || tdCustom.Departure == null || index < 0) ? "0" : tdCustom.Departure.Split(',')[index];
                    td.Capacity = (tdCustom == null || tdCustom.Capacity == null || index < 0) ? "0" : tdCustom.Capacity.Split(',')[index];
                    td.LatDegree = itm.lat;
                    td.LonDegree = itm.lon;
                    td.TrafficAreaId = 1;
                    TDList.Add(td);
                }
            }

            return TDList;
        }
        [Route("Home/GetTrafficDemandSectorMap")]
        [HttpGet]
        public List<DisplayTDSectorModel> GetTrafficDemandSectortMap()
        {
            List<DisplayTDSectorModel> TDList = new List<DisplayTDSectorModel>();
            var sectordefault = _context.StaticAirspace.Where(s => s.AirspaceType == AirspaceType.SECTOR).ToList();
            //var tdAll = _context.TDSituationAwareness.ToList();
            var tdAll = CacheSituationAwareness();
            var dtNow = DateTime.UtcNow;
            var hour = dtNow.TimeOfDay.Hours;

            if (tdAll != null && tdAll.Count > 0)
            {
                foreach (var itm in sectordefault)
                {
                    DisplayTDSectorModel td = new DisplayTDSectorModel();
                    var index = 0;
                    var tdCustom = tdAll.Where(t => t.Designator == itm.Name).FirstOrDefault();
                    if (tdCustom != null)
                    {
                        string[] times = tdCustom.TimeInterval.Split(',');
                        index = Array.FindIndex(times, t => t == hour.ToString());
                    }
                    td.Designator = itm.Name;
                    td.Name = itm.Name;
                    td.GeographyString = itm.Geography.AsText();
                    td.TrafficAreaId = 3;
                    td.Arrival = (tdCustom == null || tdCustom.Arrival == null || index < 0) ? "0" : tdCustom.Arrival.Split(',')[index];
                    td.Capacity = (tdCustom == null || tdCustom.Capacity == null || index < 0) ? "0" : tdCustom.Capacity.Split(',')[index];

                    TDList.Add(td);
                }
                TDList.OrderBy(t => t.TrafficAreaId);
            }
            return TDList;
        }
        [Route("Home/GetAllTrafficDemandList")]
        [HttpGet]
        public List<TDSituationAwareness> GetAllTrafficDemandList()
        {

            List<TDSituationAwareness> TDList = new List<TDSituationAwareness>();
            var apdefault = AirportDefault();
            var sectordefault = _context.StaticAirspace.Where(s => s.AirspaceType == AirspaceType.SECTOR).ToList();
            //var tdsw = _context.TDSituationAwareness.ToList();
            var tdsw = CacheSituationAwareness();

            foreach (var itm in apdefault)
            {
                var td = tdsw.Where(t => t.Designator.Equals(itm.name)).FirstOrDefault();
                TDList.Add(td);
            }
            foreach (var itm in sectordefault)
            {
                var td = tdsw.Where(t => t.Designator.Equals(itm.Name)).FirstOrDefault();
                TDList.Add(td);
            }
            TDList.OrderBy(t => t.TrafficAreaId);
            // ActionLog("Home - View Traffic Demand");

            return TDList;
        }
        // get Active ATFM
        [HttpGet]
        public List<GDP> GetActiveGdp() {
            return CacheActiveGdp();
        }

        //  GET  /Home/GetResourcesList
        [HttpGet, Route("Home/GetResourcesList")]
        public IActionResult GetResourcesList()
        {
            var idxPath = Path.Combine(_env.WebRootPath, "files", "resources", "resources.index.json");
            if (!System.IO.File.Exists(idxPath))
                return Ok(Array.Empty<object>());

            var opts = new System.Text.Json.JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };
            var metas = STJ.Deserialize<List<ResourceMeta>>(System.IO.File.ReadAllText(idxPath), opts)
                       ?? new List<ResourceMeta>();

            /* keep rows that point to EITHER a file OR a link */
            var rows = metas
                .Where(m =>
                       (m.Type == "file" && !string.IsNullOrEmpty(m.FileRelPath)) ||
                       (m.Type == "link" && !string.IsNullOrEmpty(m.LinkUrl)))
                .OrderBy(m => m.Serial)
                .Select(m => new
                {
                    displayName = string.IsNullOrWhiteSpace(m.DisplayName) ? "(no title)" : m.DisplayName,
                    fileRelPath = m.Type == "file" ? "/" + m.FileRelPath!.Replace("\\", "/") : null,
                    url = m.Type == "link" ? m.LinkUrl : null
                });

            return Ok(rows);
        }

        /* local projection of the structure in resources.index.json */
        private record ResourceMeta
        {
            public int Serial { get; init; }
            public string? DisplayName { get; init; }
            public string Type { get; init; } = "file";     // "file" | "link"
            public string? FileRelPath { get; init; }               // file-mode
            public string? LinkUrl { get; init; }               // link-mode
        }

            public IActionResult Privacy()
        {
            return View();
        }

        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }



        [HttpGet]
        public JsonResult getTotalStats()
        {
            JsonResult cacheEntry;
            // Look for cache key.
            if (!_cache.TryGetValue("TotalStat", out cacheEntry))
            {
                // Key not in cache, so get data.
                cacheEntry = CacheFlightStat();

                // Save data in cache and set the relative expiration time to one day
                _cache.Set("TotalStat", cacheEntry, TimeSpan.FromMinutes(3)); //cache for 3 min
            }

            return _cache.Get<JsonResult>("TotalStat");
            /* // Look for cache key.
             if (!_cache.TryGetValue(CacheKeys.Entry, out cacheEntry))
             {
                 // Key not in cache, so get data.
                 cacheEntry = CacheFlightStat();

                 // Save data in cache and set the relative expiration time to one day
                 _cache.Set(CacheKeys.Entry, cacheEntry, TimeSpan.FromMinutes(3)); //cache for 3 min
             }

             return _cache.Get<JsonResult>(CacheKeys.Entry);*/
        }
        private JsonResult CacheFlightStat()
        {
            var current = DateTime.UtcNow;
            List<Flight> flist = new List<Flight>();
            using (var db = _context)
            {
                flist = db.Flight.Where(f => f.EOBT.Value.Date == current.Date && f.ATOT != null).ToList();
            }
            var total = flist.Count();
            var landed = flist.Where(l => l.ALDT != null).Count();
            var fly = flist.Where(f => f.ATOT != null && f.ALDT == null).Count();

            /*var delays = flist.Where(l => l.ALDT != null && l.ALDT > l.ELDT).ToList();*/
            var delays = flist.Where(l => l.CTOT != null && l.CTOT > l.ETOT).ToList();
            //var delaysDep = delays.Where(l => l.ALDT != null).ToList();
            Double ttimes = 0;
            //Double dep

            foreach (var itm in delays)
            {
                /*ttimes = ttimes + (itm.ALDT - itm.ELDT).Value.TotalMinutes;*/
                ttimes = ttimes + (itm.CTOT - itm.ETOT).Value.TotalMinutes;
            }

            var result = new
            {
                FlightTotal = total,
                FlightLanded = landed,
                FlightAirborned = fly,
                FlightDelayTTL = ttimes,
                FlightDelayCount = delays.Count(),
                FlightDelayCM = delays.Count() > 0 ? ttimes / delays.Count() : 0

            };
            return Json(result);
        }
       /* private List<TDSituationAwareness> CacheSituationAwareness()
        {
            List<TDSituationAwareness> cacheEntry;
            // Look for cache key.
            if (!_cache.TryGetValue("SituationAwareness", out cacheEntry))
            {
                // Key not in cache, so get data.
                cacheEntry = _context.TDSituationAwareness.ToList();

                // Save data in cache and set the relative expiration time to one day
                _cache.Set("SituationAwareness", cacheEntry, TimeSpan.FromMinutes(1)); //cache for 1 min
            }

            return _cache.Get<List<TDSituationAwareness>>("SituationAwareness");   
        }*/
        /*Modify to update only has value*/
        private List<TDSituationAwareness> CacheSituationAwareness()
        {
            // Attempt to get data from cache, including expired data
            var cacheEntry = _cache.Get<List<TDSituationAwareness>>("SituationAwareness");

            if (cacheEntry == null || !_cache.TryGetValue("SituationAwareness", out _))
            {
                // Fetch data from the database
                var dbData = _context.TDSituationAwareness.ToList();

                // Cache new data if available, otherwise retain the old data
                if (dbData.Any())
                {
                    cacheEntry = dbData;
                    _cache.Set("SituationAwareness", cacheEntry, TimeSpan.FromMinutes(1)); // Cache for 1 min
                }
            }

            return cacheEntry ?? new List<TDSituationAwareness>();
        }

        private List<GDP> CacheActiveGdp()
        {
            List<GDP> cacheEntry;
            // Look for cache key.
            if (!_cache.TryGetValue("ActiveGdp", out cacheEntry))
            {
                // Key not in cache, so get data.
                cacheEntry = _context.GDP.Where(g=> g.IsActive && !g.IsCancelled).ToList();
            
                // Save data in cache and set the relative expiration time to one day
                _cache.Set("ActiveGdp", cacheEntry, TimeSpan.FromMinutes(3)); //cache for 3 min
            }

            return _cache.Get<List<GDP>>("ActiveGdp");
        }
        /*  [HttpGet]
          [Route("home/Flightlist")]
          public async Task<ActionResult<string>> GetFlightlist()
          {
              string url = "https://172.16.21.112/api/radarservice"; // sample url
              using (HttpClient client = new HttpClient())
              {
                  return await client.GetStringAsync(url);
              }
          }*/


        [HttpGet]
        [Route("Home/GetAnnouncement")]
        public List<Announcement> getAnnouncement()
        {
            var userid = User.FindFirstValue(ClaimTypes.NameIdentifier);
            var username = User.FindFirstValue(ClaimTypes.Name);
            var userroles = _extraContext.UserToRoles.Where(r => r.UserId.Equals(userid)).ToList();
            var current = DateTime.UtcNow;
            List<Announcement> result = new List<Announcement>();
            var announces = _context.Announcement.Where(a=> (a.StartDate == null && a.EndDate== null) || (current >= a.StartDate && current <= a.EndDate) ).OrderByDescending(a => a.TimeStamp).ToList(); // get only active news
            foreach (var itm in announces)
            {
                if (itm.UserAllowed != null && username != "superadmin") //&& !userroles.Select(r=>r.RoleName).Equals("Admin")
                {
                    var roles = itm.UserAllowed.Split(',');
                    foreach (var user in userroles)
                    {
                        if (roles.Contains(user.RoleName))
                            result.Add(itm);
                    }
                }
                else
                    result.Add(itm);
            }
            return result;
        }



        /* code for dl file ?!!!!!!!!!!!!!!!!!!!! */
        [HttpGet]
        [Route("Home/Download/{id}")]
        public FileResult DownloadFile(int id)
        {
            //if()
            var file = _context.Fileupload.Where(f => f.Id == id).FirstOrDefault();
            //fil
            var filepath = Path.Combine(_env.WebRootPath, file.Filepath.Replace("~/", ""));
            byte[] fileBytes = System.IO.File.ReadAllBytes(filepath);

            ActionLog("Home - Download File " + file.Filename);

            return this.File(fileBytes, MediaTypeNames.Application.Octet, file.Filename);

        }
        [HttpGet]
        [Route("Home/DownloadAdp/{id}")]
        public ActionResult DownloadAdp(int id)
        {
            //if()
            var file = _context.Adp.Where(f => f.Id == id).FirstOrDefault();
            //fil

            var filepath = Path.Combine(_env.WebRootPath, file.AdpContent.Replace("~/", ""));
            try
            {
                byte[] fileBytes = System.IO.File.ReadAllBytes(filepath);
                string mimeType = "application/pdf";
                //Headers.Add("content-disposition",
                Response.Headers.Add("Content-Disposition", "inline; filename=" + file.AdpHeader);

                ActionLog("Home - Download ADP " + file.AdpHeader);

                return File(fileBytes, mimeType);
            }
            catch (Exception e)
            {
                return new NotFoundResult();
            }
        }

        [HttpGet]
        [Route("Home/GetAllAdp")]
        public List<AdpModel> GetAllAdp()
        {
            List<AdpModel> AdpList = new List<AdpModel>();
            //var date = DateTime.UtcNow.AddDays(-1).Date;
            var adps = _context.Adp.Where(a => a.AdpContent != "").ToList();
            var atfmus = _context.Atfmu.ToList();
            foreach (var itm in adps)
            {
                var adpArr = itm.AdpHeader.Split('_');
                var revision = 1;
                if (adpArr.Length > 3)
                    revision = Int32.Parse(adpArr[3]);
                var adp = new AdpModel()
                {
                    Id = itm.Id,
                    Uid = itm.Uid,
                    AdpHeader = itm.AdpHeader,
                    AdpContent = itm.AdpContent,
                    TimeStamp = itm.TimeStamp,
                    AdpDate = DateTime.ParseExact(adpArr[2], "yyyyMMdd", CultureInfo.InvariantCulture),
                    AdpFirsString = SortFirs(adpArr[1]),
                    AdpRevision = revision,
                    isLocal = isLocal(adpArr[1]),
                    Atfmu = GetAtfmu(atfmus, adpArr[1])
                };
                AdpList.Add(adp);
            }
            var order = AdpList.OrderByDescending(a => a.AdpDate).ThenBy(a => a.AdpFirsString).ThenByDescending(a => a.AdpRevision).ToList();
            ActionLog("Home - View All Adp");

            return order;
        }

        [HttpGet]
        [Route("Home/GetAdp")]
        public List<AdpModel> GetAdp()
        {
            List<AdpModel> AdpList = new List<AdpModel>();
            var date = DateTime.UtcNow.AddDays(-15).Date;
            var yesterday = DateTime.UtcNow.AddDays(-1).Date;
            var tomorrow2 = DateTime.UtcNow.AddDays(2).Date;
            var adps = _context.Adp.Where(a => a.TimeStamp >= date && a.AdpContent != "").ToList();
            var atfmus = _context.Atfmu.ToList();
            foreach (var itm in adps)
            {
                var adpArr = itm.AdpHeader.Split('_');
                if (adpArr.Length > 3)
                {
                    var adp = new AdpModel()
                    {
                        Id = itm.Id,
                        Uid = itm.Uid,
                        AdpHeader = itm.AdpHeader,
                        AdpContent = itm.AdpContent,
                        TimeStamp = itm.TimeStamp,
                        AdpDate = DateTime.ParseExact(adpArr[2], "yyyyMMdd", CultureInfo.InvariantCulture),
                        AdpFirsString = SortFirs(adpArr[1]),
                        AdpRevision = Int32.Parse(adpArr[3]),
                        isLocal = isLocal(adpArr[1]),
                        Atfmu = GetAtfmu(atfmus, adpArr[1])
                    };
                    if (adp.AdpDate >= yesterday && adp.AdpDate < tomorrow2)
                        AdpList.Add(adp);
                }
                else Debug.WriteLine("{0} error", itm.AdpHeader);

            }

            var order = AdpList.Where(a => a.AdpDate >= date).OrderBy(a => a.AdpFirsString).ThenByDescending(a => a.AdpDate).ThenByDescending(a => a.AdpRevision).ToList();

            return order.GroupBy(a => new { a.AdpDate, a.AdpFirsString }).Select(a => a.First()).ToList();
        }
        private Atfmu GetAtfmu(List<Atfmu> atfmus, string firs)
        {
            Atfmu result = new Atfmu();
            result.Name = "(" + firs + ")"; // default
            //result.A
            foreach (var itm in atfmus)
            {
                if (firs.Contains(itm.Location))
                {
                    result = itm;
                    break;
                }
            }
            return result;
        }


        private bool isLocal(string firs)
        {
            return firs.Contains("VTBB");
        }
        private string SortFirs(string firs)
        {
            var count = firs.Length / 4;
            string[] result = new string[count];
            for (var i = 0; i < count; i++)
            {
                result[i] = firs.Substring((4 * i), 4);
            }
            Array.Sort(result);

            return String.Join(',', result);
        }
        private void ActionLog(string source)
        {
            ActLog.ActionLog log = new ActLog.ActionLog();
            log.UserName = User.FindFirstValue(ClaimTypes.NameIdentifier);
            log.UserMachineIP = HttpContext.Connection.RemoteIpAddress.ToString();
            log.ActionMessage = "Test on log at " + DateTime.Today.ToString("dd-MM-yyyy HH:mm:ss");
            log.Source = source;
            Logger.LogMessage(log);
        }

    }

    public class DisplayTDModel
    {
        public string Designator { get; set; }
        public string Departure { get; set; }
        public string Arrival { get; set; }
        //public int[] DepAndArr { get; set; }
        public string Capacity { get; set; }
        public string LatDegree { get; set; }
        public string LonDegree { get; set; }
        public int TrafficAreaId { get; set; }
    }
    public class DisplayTDSectorModel
    {
        public string Arrival { get; set; }
        public string Capacity { get; set; }
        public int Id { get; set; }
        public string GeographyString { get; set; }
        public string Name { get; set; }
        public string Designator { get; set; }
        public int TrafficAreaId { get; set; }
    }
    public class AirportTDModel
    {
        public string name { get; set; }
        public string lat { get; set; }
        public string lon { get; set; }
    }
   /* public static class CacheKeys
    {
        public static string Entry { get { return "Entry"; } }
        public static string CallbackEntry { get { return "_Callback"; } }
        public static string CallbackMessage { get { return "_CallbackMessage"; } }
        public static string Parent { get { return "_Parent"; } }
        public static string Child { get { return "_Child"; } }
        public static string DependentMessage { get { return "_DependentMessage"; } }
        public static string DependentCTS { get { return "_DependentCTS"; } }
        public static string Ticks { get { return "_Ticks"; } }
        public static string CancelMsg { get { return "_CancelMsg"; } }
        public static string CancelTokenSource { get { return "_CancelTokenSource"; } }
    }*/
}
