﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Security.Claims;
using System.Threading.Tasks;
using ATFAS.ActLog;
using ATFAS.Data;
using ATFAS.Models;
using ATFAS.ViewModels;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Rendering;
using Microsoft.EntityFrameworkCore;
using NetTopologySuite.Geometries;
using ATFAS.Utils;
using PermissionParts;
using FeatureAuthorize.PolicyCode;
using Newtonsoft.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using System.IO;
using ATFAS.Controllers.CTOTHelpers;
using ATFAS.Services;
using DataLayer.EfCode;
using Microsoft.AspNetCore.Identity;
using ATFAS.Areas.Identity.Data;
using System.Diagnostics;
using Elastic.Clients.Elasticsearch.Mapping;
using System.Text;
using Microsoft.AspNetCore.Rewrite;
using PdfSharp.Charting;
using PdfSharp.Pdf.IO;
using Elastic.Clients.Elasticsearch;

namespace ATFAS.Controllers
{
    public class GDPManageController : Controller
    {
        private readonly ATFASContext _context;
        static IConfiguration conf = (new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json").Build());
        public static string ATFMOptimizerIP = conf["ATFMOptimizerManagementIP"].ToString();
        public static int HEC = Int32.Parse(conf["HECPeriod"].ToString());
        public static double doubleHCEdivided = HEC / 60.0;
        public static float alpha = float.Parse(conf["Alpha"].ToString());
        public static int fastMaxDelayIndex = Int32.Parse(conf["FastMaxDelayIndex"].ToString());
        public static int windowStep = Int32.Parse(conf["WindowStep"].ToString());
        public static int timeLimit = Int32.Parse(conf["TimeLimit"].ToString());

        public static string IdepRegional = conf["idepRegionalIP"].ToString();
        public static bool EnableCTOTtoIdep = bool.Parse(conf["EnableCTOTtoIdep"].ToString());
        private HttpClient client = new HttpClient { BaseAddress = new Uri(ATFMOptimizerIP) };
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMailService _mailService;
        private readonly IViewRenderService _viewrenderService;
        private UserManager<AppUser> _userManager;
        private readonly ExtraAuthorizeDbContext _extraContext;


        public GDPManageController(ATFASContext context, IHttpContextAccessor httpContextAccessor, UserManager<AppUser> userManager, ExtraAuthorizeDbContext extraContext, IMailService mailService, IViewRenderService viewrenderService)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _userManager = userManager;
            _extraContext = extraContext;
            _mailService = mailService;
            _viewrenderService = viewrenderService;

        }
        [HasPermission(Permissions.CtotChange)]
        public IActionResult Index()
        {
            ViewData["TrafficAreaId"] = new SelectList(_context.Set<TrafficArea>(), "Id", "Type");

            return View();
        }


        [HttpPost]
        public async Task<GDPInfo> GetGdpInfo(GDP gdp)
        {

            GDPInfo info = new GDPInfo();

            info.GDPSaved = await _context.GDP.Include(g => g.TrafficArea).Where(g => !g.IsCancelled && !g.IsExecuted).OrderByDescending(g => g.TimeUpdated).ThenByDescending(g => g.TimeSaved).ToListAsync();
            info.GDPExecuted = await _context.GDP.Include(g => g.TrafficArea).Where(g => !g.IsCancelled && g.IsExecuted && g.IsActive).OrderByDescending(g => g.TimeExecuted).Take(10).ToListAsync();
            info.Capacities = await _context.Capacity.Where(c => !c.IsDep && !c.IsOverall && !c.IsOccupancy).ToListAsync();
            info.CapacityEvents = await _context.CapacityEvent.OrderBy(c => c.StartTime).ToListAsync();
            if (gdp.TrafficAreaId > 0)
            {
                info.GDPSaved = info.GDPSaved.Where(g => g.TrafficAreaId.Equals(gdp.TrafficAreaId)).ToList();
                info.GDPExecuted = info.GDPExecuted.Where(g => g.TrafficAreaId.Equals(gdp.TrafficAreaId)).ToList();
            }
            if (gdp.Point != null)
            {
                info.GDPSaved = info.GDPSaved.Where(g => g.Point.Equals(gdp.Point)).ToList();
                info.GDPExecuted = info.GDPExecuted.Where(g => g.Point.Equals(gdp.Point)).ToList();
            }
            if (gdp.StartTime > DateTime.MinValue)
            {
                info.GDPSaved = info.GDPSaved.Where(g => g.StartTime >= gdp.StartTime || gdp.StartTime <= g.EndRecoveryTime).ToList();
                info.GDPExecuted = info.GDPExecuted.Where(g => g.StartTime >= gdp.StartTime || gdp.StartTime <= g.EndRecoveryTime).ToList();
            }
            if (gdp.EndTime > DateTime.MinValue)
            {
                info.GDPSaved = info.GDPSaved.Where(g => g.EndRecoveryTime <= gdp.EndTime || gdp.EndTime >= g.StartTime).ToList();
                info.GDPExecuted = info.GDPExecuted.Where(g => g.EndRecoveryTime <= gdp.EndTime || gdp.EndTime >= g.StartTime).ToList();
            }

            return info;
        }


        [HttpPost]
        public async Task<GDPChart> RequestChart(GDP gdpin)
        {
            try
            {
                var gdp = _context.GDP.Include(g => g.GDPFlights).ThenInclude(g => g.Flight).Where(g => g.Id == gdpin.Id && !g.IsCancelled).FirstOrDefault();
                if (gdp == null) return new GDPChart { Title = "gdp not found" };
                else gdp.IntervalMin = gdpin.IntervalMin;

                string formatLabel = FlightData.FORMAT_TIME;
                string title = "Regulated Demand <" + gdp.Point + "> (" + gdp.StartTime.ToString(FlightData.FORMAT_DATETIME_LONG) + " - ";
                if (gdp.StartTime.Date.Equals(gdp.EndRecoveryTime.Date)) title += gdp.EndRecoveryTime.ToString(FlightData.FORMAT_TIME) + ")";
                else
                {
                    title += gdp.EndRecoveryTime.ToString(FlightData.FORMAT_DATETIME_LONG) + ")";
                    formatLabel = FlightData.FORMAT_DATETIME_SHORT;
                }
                int numInterval = (int)Math.Ceiling((gdp.EndRecoveryTime.Subtract(gdp.StartTime).TotalMinutes) / (double)gdp.IntervalMin);
                gdp.TrafficArea = _context.TrafficArea.FindAsync(gdp.TrafficAreaId).Result;
                List<int> flightIds = new List<int>();
                List<FlightData> flightDatas = new List<FlightData>();
                CTOTMeasure[] ctotMeasures = new CTOTMeasure[1];
                ctotMeasures[0] = new CTOTMeasure
                {
                    MeasureId = gdp.Id,
                    StartTime = JsonConvert.SerializeObject(gdp.StartTime).Trim('"'),
                    EndTime = JsonConvert.SerializeObject(gdp.EndTime).Trim('"'),
                    EndRecovery = JsonConvert.SerializeObject(gdp.EndRecoveryTime).Trim('"'),
                    Capacity = (int)Math.Round(gdp.CapacityPerHr * doubleHCEdivided), //use HEC from appSettings
                    CapacityRecovery = (int)Math.Round(gdp.CapacityRecoveryPerHr * doubleHCEdivided), //use HEC from appSettings
                    TrafficAreaType = "ArrivalAirport",
                    TrafficAreaName = gdp.Point
                };
                CTOTAlgorithm ctotAlgorithm = new CTOTAlgorithm
                {
                    AlgorithmName = "Network-based", //"Fast-network-based",
                    Parameters = new CTOTParameters
                    {
                        StartTime = JsonConvert.SerializeObject(gdp.StartTime.AddHours(-1)).Trim('"'),
                        EndTime = JsonConvert.SerializeObject(gdp.EndRecoveryTime.AddHours(1)).Trim('"'),
                        HaveAirborneDelay = false,
                        HECPeriod = HEC,// use HEC from AppSettings
                        Alpha = alpha,
                        FastMaxDelayIndex = fastMaxDelayIndex,
                        MaxDelayIndex = fastMaxDelayIndex,
                        TimeLimit = timeLimit,
                        WindowStep = windowStep
                    }
                };
                //get flighs     
                List<GDPFlight> gdpflights = new List<GDPFlight>();
                Dictionary<int, GDPFlight> idGDPFlight = new Dictionary<int, GDPFlight>();

                if (gdp.IsExecuted)
                {
                    gdpflights = gdp.GDPFlights.Where(g => !g.IsCancelled).ToList();
                    /* Display only active Flight    || (g.IsCancelled && g.Flight.IsExempt)*/
                    foreach (GDPFlight gfpl in gdpflights)
                    {
                        Flight flight = gfpl.Flight;
                        gfpl.FlightId = gfpl.Flight.Id;
                        flightIds.Add(flight.Id);
                        if (idGDPFlight.ContainsKey(gfpl.Flight.Id))
                            idGDPFlight[gfpl.Flight.Id] = gfpl;
                        else
                            idGDPFlight.Add(gfpl.Flight.Id, gfpl);
                    }
                }
                TrafficDemand trafficDemand = new TrafficDemand
                {
                    Id = gdp.Id,
                    TrafficAreaId = gdp.TrafficAreaId,
                    TrafficArea = gdp.TrafficArea,
                    Designator = gdp.Designator,
                    Point = gdp.Point,
                    IsArr = true,
                    StartTime = gdp.StartTime,
                    EndTime = gdp.EndRecoveryTime,
                    IntervalMin = gdp.IntervalMin,
                    IsIFR = gdp.IsIFR,
                    IsVFR = gdp.IsVFR,
                    IsFPL = true,
                    IsATFM = true,
                    IsATSMSG = true,
                    IsSUR = true,
                    IsPassed = true,
                    TimeSaved = gdp.TimeSaved
                };
                GDPChart gdpChart = new GDPChart
                {
                    GDP = gdp,
                    TrafficDemand = trafficDemand,
                    Title = title,
                    Labels = new string[numInterval],
                    Titles = new string[numInterval],
                    DataFPL = new int[numInterval],
                    DataATFM = new int[numInterval],
                    DataATSMSG = new int[numInterval],
                    DataSUR = new int[numInterval],
                    DataPassed = new int[numInterval],
                    FlightLists = new List<FlightData>[numInterval],
                    DataKickoffDelay = new int[numInterval],
                    Capacities = new int[numInterval + 1]
                };

                Dictionary<int, FlightData> idFlightData = new Dictionary<int, FlightData>();
                CTOTFlightInput[] ctotFlightInputs = null;

                if (gdp.TrafficAreaId == 1)
                {
                    Airport airport = await _context.Airport.FirstOrDefaultAsync(a => a.DESIGNATOR == gdp.Point && !a.ROWDELETE);
                    if (airport == null) return new GDPChart { Title = "Airport <" + gdp.Point + "> is not found." };
                    if (!gdp.IsExecuted)
                    {
                        var flights = await _context.Flight.Where(f => !f.IsCancelled && f.AirportArrival == gdp.Point &&
                        ((f.ALDT != null && f.ALDT >= gdp.StartTime && f.ALDT < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS != null && f.ELDTByTMCS >= gdp.StartTime && f.ELDTByTMCS < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep != null && f.ELDTByDep >= gdp.StartTime && f.ELDTByDep < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT != null && f.CLDT >= gdp.StartTime && f.CLDT < gdp.EndRecoveryTime) ||
                        (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT == null && f.ELDT != null && f.ELDT >= gdp.StartTime && f.ELDT < gdp.EndRecoveryTime))).ToListAsync();
                        /*Add VFR Flight*/
                        if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                        {
                            if (trafficDemand.IsIFR) flights = flights.Where(f => f.FlightRule == null || f.FlightRule.Equals("I") || f.FlightRule.Equals("Y")).ToList();
                            else flights = flights.Where(f => f.FlightRule == null || f.FlightRule.Equals("V") || f.FlightRule.Equals("Z")).ToList();
                        }
                        for (var i = 0; i < flights.Count(); i++)
                        {
                            gdpflights.Add(new GDPFlight { Flight = flights[i] });

                        }

                        ctotFlightInputs = new CTOTFlightInput[flights.Count];
                        for (int i = 0; i < ctotFlightInputs.Length; i++)
                        {
                            ctotFlightInputs[i] = new CTOTFlightInput
                            {
                                SectorTrajectory = Array.Empty<SectorTrajectory>(),
                                WaypointTrajectory = Array.Empty<WaypointTrajectory>()
                            };
                        }
                        ctotMeasures[0].TrafficAreaType = "ArrivalAirport";
                    }
                }
                else if (gdp.TrafficAreaId == 2)
                {
                    Fix fix = await _context.Fix.FirstOrDefaultAsync(a => a.DESIGNATOR == gdp.Point && !a.ROWDELETE);
                    if (fix == null) return new GDPChart { Title = "Fix <" + gdp.Point + "> is not found." };
                    int radiusNm = gdp.RadiusNm != null ? gdp.RadiusNm.Value : fix.RadiusNm;
                    double radius = radiusNm / 0.0006213712;
                    Geometry circle = await _context.Fix.Where(f => f.ID == fix.ID).Select(f => f.GEOPOINT.Buffer(radius)).FirstOrDefaultAsync();
                    int minLevel = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : fix.MinLevelFt;
                    int maxLevel = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : fix.MaxLevelFt;
                    List<FlightTrajectory> trajectoryListTemp = new List<FlightTrajectory>();
                    List<FlightTrajectory> trajectoryList = new List<FlightTrajectory>();
                    if (gdp.IsExecuted) trajectoryListTemp = await _context.FlightTrajectory.Where(t => flightIds.Contains(t.FlightId) && t.IsActive).Include(f => f.Flight).ToListAsync();
                    else /**Add VFR Flight **/
                    {
                        trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                        if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                        {
                            if (trafficDemand.IsIFR) trajectoryListTemp = trajectoryList.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("I") || t.Flight.FlightRule.Equals("Y")).ToList();
                            else trajectoryListTemp = trajectoryList.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("V") || t.Flight.FlightRule.Equals("Z")).ToList();
                        }
                    }
                    //else trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();

                    trajectoryList = UpdateExemptTrajectory(trajectoryListTemp);

                    TDChart tdChart = new TDChart
                    {
                        TrafficDemand = trafficDemand,
                        Title = title,
                        Labels = new string[numInterval],
                        Titles = new string[numInterval],
                        DataFPL = new int[numInterval],
                        DataATFM = new int[numInterval],
                        DataATSMSG = new int[numInterval],
                        DataSUR = new int[numInterval],
                        DataPassed = new int[numInterval],
                        FlightLists = new List<FlightData>[numInterval],
                        Capacities = new int[numInterval + 1]
                    };
                    TrafficDemandController.PrepareFlightLists(trajectoryList, new Geometry[] { circle }, tdChart, gdp.StartTime, gdp.EndRecoveryTime, new int[] { minLevel }, new int[] { maxLevel }, formatLabel);
                    int count = 0;
                    for (int i = 0; i < numInterval; i++)
                    {
                        for (int j = 0; j < tdChart.FlightLists[i].Count; j++)
                        {
                            FlightData flightData = tdChart.FlightLists[i][j];
                            if (!gdp.IsExecuted)
                            {
                                gdpflights.Add(new GDPFlight { FlightId = flightData.Flight.Id, Flight = flightData.Flight });
                            }
                            flightDatas.Add(flightData);
                            idFlightData.Add(flightData.Flight.Id, flightData);
                            count += 1;
                        }
                    }
                    if (gdp.IsExecuted)
                    {
                        foreach (var fda in flightDatas)
                        {
                            fda.IsSent = idGDPFlight[fda.Flight.Id].IsSent;
                            fda.Comment = idGDPFlight[fda.Flight.Id].Comment;
                            fda.Username = idGDPFlight[fda.Flight.Id].Username;
                            fda.NewEobt = idGDPFlight[fda.Flight.Id].NewEOBT;
                            fda.IsGdpExempt = idGDPFlight[fda.Flight.Id].IsGdpExempt;
                            fda.IsFlightExempt = idGDPFlight[fda.Flight.Id].Flight.IsExempt;
                        }
                    }
                    if (!gdp.IsExecuted)
                    {
                        ctotFlightInputs = new CTOTFlightInput[flightDatas.Count];
                        for (int i = 0; i < ctotFlightInputs.Length; i++)
                        {
                            ctotFlightInputs[i] = new CTOTFlightInput
                            {
                                SectorTrajectory = Array.Empty<SectorTrajectory>(),
                                WaypointTrajectory = new WaypointTrajectory[]
                                {
                                    new WaypointTrajectory
                                    {
                                        WaypointName = gdp.Point,
                                        ETO = JsonConvert.SerializeObject(flightDatas[i].TO.Value).Trim('"')
                                    }
                                }
                            };
                        }
                        ctotMeasures[0].TrafficAreaType = "Waypoint";
                    }
                }
                else
                {
                    List<FlightTrajectory> trajectoryList = new List<FlightTrajectory>();
                    Geometry[] geometries = null;
                    int[] minLevels = null, maxLevels = null;
                    if (gdp.TrafficAreaId == 3)
                    {
                        StaticAirspace[] sectors = await _context.StaticAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();
                        if (sectors == null || sectors.Length == 0) return new GDPChart { Title = "Sector <" + gdp.Point + "> is not found." };
                        minLevels = new int[sectors.Length];
                        maxLevels = new int[sectors.Length];
                        geometries = new Geometry[sectors.Length];
                        for (int i = 0; i < sectors.Length; i++)
                        {
                            minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : sectors[i].LowerLimitFt;
                            maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : sectors[i].UpperLimitFt;
                            geometries[i] = sectors[i].Geography;
                            List<FlightTrajectory> trajectoryListTemp = new List<FlightTrajectory>();
                            if (gdp.IsExecuted) trajectoryListTemp = await _context.FlightTrajectory.Where(t => flightIds.Contains(t.FlightId) && t.IsActive).Include(f => f.Flight).ToListAsync();
                            else /* Add VFR Flight */
                            {
                                trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                                if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                                {
                                    if (trafficDemand.IsIFR) trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("I") || t.Flight.FlightRule.Equals("Y")).ToList();
                                    else trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("V") || t.Flight.FlightRule.Equals("Z")).ToList();
                                }
                            }
                            // else trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                            trajectoryList.AddRange(trajectoryListTemp);
                        }
                        trajectoryList = UpdateExemptTrajectory(trajectoryList);
                    }
                    else if (gdp.TrafficAreaId == 4)
                    {
                        UserDefinedAirspace[] airspaces = await _context.UserDefinedAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();
                        if (airspaces == null || airspaces.Length == 0) return new GDPChart { Title = "Airspace <" + gdp.Point + "> is not found." };
                        minLevels = new int[airspaces.Length];
                        maxLevels = new int[airspaces.Length];
                        geometries = new Geometry[airspaces.Length];
                        for (int i = 0; i < airspaces.Length; i++)
                        {
                            minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : airspaces[i].LowerLimitFt;
                            maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : airspaces[i].UpperLimitFt;
                            geometries[i] = airspaces[i].Geography;
                            List<FlightTrajectory> trajectoryListTemp = new List<FlightTrajectory>();
                            if (gdp.IsExecuted) trajectoryListTemp = await _context.FlightTrajectory.Where(t => flightIds.Contains(t.FlightId) && t.IsActive).Include(f => f.Flight).ToListAsync();
                            else /* Add VFR Flight */
                            {
                                trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                                if (!(trafficDemand.IsIFR && trafficDemand.IsVFR))
                                {
                                    if (trafficDemand.IsIFR) trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("I") || t.Flight.FlightRule.Equals("Y")).ToList();
                                    else trajectoryListTemp = trajectoryListTemp.Where(t => t.Flight.FlightRule == null || t.Flight.FlightRule.Equals("V") || t.Flight.FlightRule.Equals("Z")).ToList();
                                }
                            }
                            //else trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();
                            trajectoryList.AddRange(trajectoryListTemp);
                        }
                        trajectoryList = UpdateExemptTrajectory(trajectoryList);
                    }
                    TDChart tdChart = new TDChart
                    {
                        TrafficDemand = trafficDemand,
                        Title = title,
                        Labels = new string[numInterval],
                        Titles = new string[numInterval],
                        DataFPL = new int[numInterval],
                        DataATFM = new int[numInterval],
                        DataATSMSG = new int[numInterval],
                        DataSUR = new int[numInterval],
                        DataPassed = new int[numInterval],
                        FlightLists = new List<FlightData>[numInterval],
                        Capacities = new int[numInterval + 1]
                    };

                    TrafficDemandController.PrepareFlightLists(trajectoryList, geometries, tdChart, gdp.StartTime, gdp.EndRecoveryTime, minLevels, maxLevels, formatLabel);
                    int count = 0;
                    for (int i = 0; i < numInterval; i++)
                    {
                        for (int j = 0; j < tdChart.FlightLists[i].Count; j++)
                        {
                            FlightData flightData = tdChart.FlightLists[i][j];
                            if (!gdp.IsExecuted)
                            {
                                gdpflights.Add(new GDPFlight { FlightId = flightData.Flight.Id, Flight = flightData.Flight });
                                //gdpflights[count].FlightId = flightData.Flight.Id; 
                                //gdpflights[count].Flight = flightData.Flight;
                            }
                            flightDatas.Add(flightData);
                            idFlightData.Add(flightData.Flight.Id, flightData);
                            count = count + 1;
                        }
                    }
                    if (gdp.IsExecuted)
                    {
                        foreach (var fda in flightDatas)
                        {
                            fda.IsSent = idGDPFlight[fda.Flight.Id].IsSent;
                            fda.Comment = idGDPFlight[fda.Flight.Id].Comment;
                            fda.Username = idGDPFlight[fda.Flight.Id].Username;
                            fda.NewEobt = idGDPFlight[fda.Flight.Id].NewEOBT;
                            fda.IsGdpExempt = idGDPFlight[fda.Flight.Id].IsGdpExempt;
                            fda.IsFlightExempt = idGDPFlight[fda.Flight.Id].Flight.IsExempt;
                        }
                    }
                    if (!gdp.IsExecuted)
                    {
                        ctotFlightInputs = new CTOTFlightInput[gdpflights.Count];
                        for (int i = 0; i < ctotFlightInputs.Length; i++)
                        {
                            ctotFlightInputs[i] = new CTOTFlightInput
                            {
                                SectorTrajectory = new SectorTrajectory[]
                                {
                                    new SectorTrajectory
                                    {
                                        SectorName = gdp.Point,
                                        InboundTime = JsonConvert.SerializeObject(flightDatas[i].INB.Value).Trim('"')
                                    }
                                },
                                WaypointTrajectory = Array.Empty<WaypointTrajectory>()
                            };
                        }
                        ctotMeasures[0].TrafficAreaType = "Sector";
                    }
                }
                if (!gdp.IsExecuted)
                {
                    var gdpflightArray = gdpflights.ToArray();
                    HashSet<string> exemptAdepSet = gdp.ExemptADEP != null ? gdp.ExemptADEP.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                    HashSet<string> exemptAdesSet = gdp.ExemptADES != null ? gdp.ExemptADES.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                    HashSet<string> exemptAirlineSet = gdp.ExemptAirlines != null ? gdp.ExemptAirlines.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                    for (int i = 0; i < ctotFlightInputs.Length; i++)
                    {
                        Flight flight = gdpflightArray[i].Flight;
                        ctotFlightInputs[i].FlightId = flight.Id;
                        ctotFlightInputs[i].IsExcluded = false;
                        ctotFlightInputs[i].IsExempted = flight.ATOT.HasValue || exemptAdepSet.Any(s => flight.AirportDeparture.StartsWith(s)) || exemptAdesSet.Any(s => flight.AirportArrival.StartsWith(s)) || exemptAirlineSet.Any(s => flight.Callsign.StartsWith(s));
                        ctotFlightInputs[i].IsManaged = false;
                        ctotFlightInputs[i].ArrivalAirport = flight.AirportArrival;
                        ctotFlightInputs[i].DepartureAirport = flight.AirportDeparture;
                        ctotFlightInputs[i].ETOT = JsonConvert.SerializeObject(flight.ATOT ?? (flight.CTOT ?? flight.ETOT.Value)).Trim('"');
                        ctotFlightInputs[i].ELDT = JsonConvert.SerializeObject(flight.ALDT ?? (flight.ELDTByTMCS ?? (flight.ELDTByDep ?? (flight.CLDT ?? flight.ELDT.Value)))).Trim('"');
                    }
                    CTOTOptimizeInput input = new CTOTOptimizeInput
                    {
                        Measures = ctotMeasures,
                        Algorithm = ctotAlgorithm,
                        Flights = ctotFlightInputs
                    };
                    CTOTOptimizeOutput ctotOptimizeOutput = await GetCtotFlightsAsync(input);
                    if (ctotOptimizeOutput != null && ctotOptimizeOutput.GroundDelayedFlights != null)
                    {
                        for (int i = 0; i < ctotOptimizeOutput.GroundDelayedFlights.Length; i++)
                        {
                            CTOTGroundDelayedFlight ctotFlight = ctotOptimizeOutput.GroundDelayedFlights[i];
                            Flight flight = gdpflights.Where(f => f.Flight.Id == ctotFlight.FlightId).Select(f => f.Flight).FirstOrDefault();
                            flight.COBT = flight.EOBT.Value.AddMinutes(ctotFlight.GroundDelay);
                            flight.CTOT = flight.ETOT.Value.AddMinutes(ctotFlight.GroundDelay);
                            flight.CLDT = flight.ELDT.Value.AddMinutes(ctotFlight.GroundDelay);
                            flight.CIBT = flight.EIBT.Value.AddMinutes(ctotFlight.GroundDelay);
                            if (gdp.TrafficAreaId != 1)
                            {
                                FlightData flightData = idFlightData.GetValueOrDefault(ctotFlight.FlightId);
                                flightData.Flight = flight;
                                FlightData.AddGroundDelayFlightData(flightData, ctotFlight.GroundDelay);
                            }
                        }
                    }
                }
                DateTime startTime = gdp.StartTime, endTime; 
                string formatFlightData = FlightData.FORMAT_TIME;

                for (int i = 0; i < numInterval; i++)
                {
                    gdpChart.Labels[i] = startTime.ToString(formatLabel);
                    endTime = startTime.AddMinutes(gdp.IntervalMin);
                    if (endTime > gdp.EndRecoveryTime) endTime = gdp.EndRecoveryTime;
                    gdpChart.Titles[i] = "Regulated Demand <" + gdp.Point + "> (" + startTime.ToString(FlightData.FORMAT_DATETIME_LONG) + " - ";
                  
                    if (startTime.Date.Equals(endTime.Date)) gdpChart.Titles[i] += endTime.ToString(FlightData.FORMAT_TIME) + ")";
                    else
                    {
                        formatFlightData = FlightData.FORMAT_DATETIME_SHORT;
                        gdpChart.Titles[i] += endTime.ToString(FlightData.FORMAT_DATETIME_LONG) + ")";
                    }
                    gdpChart.FlightLists[i] = new List<FlightData>();
                    if (gdp.TrafficAreaId == 1)
                    {
                        List<GDPFlight> flightList = gdpflights.Where(f => f.Flight.ALDT != null && f.Flight.ALDT >= startTime && f.Flight.ALDT < endTime).ToList();
                        gdpChart.DataPassed[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            var fda = FlightData.GetFlightData(flight.Flight, 7, null, formatFlightData);
                            fda.IsSent = flight.IsSent;
                            fda.Comment = flight.Comment;
                            fda.Username = flight.Username;
                            fda.NewEobt = flight.NewEOBT;
                            fda.IsGdpExempt = flight.IsGdpExempt;
                            fda.IsFlightExempt = flight.Flight.IsExempt;

                            gdpChart.FlightLists[i].Add(fda);
                        }
                        flightList = gdpflights.Where(f => f.Flight.ALDT == null && f.Flight.ELDTByTMCS != null && f.Flight.ELDTByTMCS >= startTime && f.Flight.ELDTByTMCS < endTime).ToList();
                        gdpChart.DataSUR[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            var fda = FlightData.GetFlightData(flight.Flight, 6, null, formatFlightData);
                            fda.IsSent = flight.IsSent;
                            fda.Comment = flight.Comment;
                            fda.Username = flight.Username;
                            fda.NewEobt = flight.NewEOBT;
                            fda.IsGdpExempt = flight.IsGdpExempt;
                            fda.IsFlightExempt = flight.Flight.IsExempt;

                            gdpChart.FlightLists[i].Add(fda); ;
                        }
                        flightList = gdpflights.Where(f => f.Flight.ALDT == null && f.Flight.ELDTByTMCS == null && f.Flight.ELDTByDep != null && f.Flight.ELDTByDep >= startTime && f.Flight.ELDTByDep < endTime).ToList();
                        gdpChart.DataATSMSG[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            var fda = FlightData.GetFlightData(flight.Flight, 4, null, formatFlightData);
                            fda.IsSent = flight.IsSent;
                            fda.Comment = flight.Comment;
                            fda.Username = flight.Username;
                            fda.NewEobt = flight.NewEOBT;
                            fda.IsGdpExempt = flight.IsGdpExempt;
                            fda.IsFlightExempt = flight.Flight.IsExempt;

                            gdpChart.FlightLists[i].Add(fda);
                        }
                        flightList = gdpflights.Where(f => f.Flight.ALDT == null && f.Flight.ELDTByTMCS == null && f.Flight.ELDTByDep == null && f.Flight.CLDT != null && f.Flight.CLDT >= startTime && f.Flight.CLDT < endTime).ToList();
                        gdpChart.DataATFM[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            var fda = FlightData.GetFlightData(flight.Flight, 3, null, formatFlightData);
                            fda.IsSent = flight.IsSent;
                            fda.Comment = flight.Comment;
                            fda.Username = flight.Username;
                            fda.NewEobt = flight.NewEOBT;
                            fda.IsGdpExempt = flight.IsGdpExempt;
                            fda.IsFlightExempt = flight.Flight.IsExempt;

                            gdpChart.FlightLists[i].Add(fda);
                        }
                        flightList = gdpflights.Where(f => f.Flight.ALDT == null && f.Flight.ELDTByTMCS == null && f.Flight.ELDTByDep == null && f.Flight.CLDT == null && f.Flight.ELDT != null && f.Flight.ELDT >= startTime && f.Flight.ELDT < endTime).ToList();
                        gdpChart.DataFPL[i] = flightList.Count;
                        foreach (var flight in flightList)
                        {
                            var fda = FlightData.GetFlightData(flight.Flight, 2, null, formatFlightData);
                            fda.IsSent = flight.IsSent;
                            fda.Comment = flight.Comment;
                            fda.Username = flight.Username;
                            fda.NewEobt = flight.NewEOBT;
                            fda.IsGdpExempt = flight.IsGdpExempt;
                            fda.IsFlightExempt = flight.Flight.IsExempt;

                            gdpChart.FlightLists[i].Add(fda);
                        }
                        gdpChart.FlightLists[i].Sort((x, y) => x.LDT.Value.CompareTo(y.LDT.Value));
                    }
                    else if (gdp.TrafficAreaId == 2)
                    {
                        List<FlightData> flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 7 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataPassed[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 6 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataSUR[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 4 && f.TO >= startTime && f.TO < endTime).ToList();
                        /*UpdateTOandINBExempt
                        flightDataList = UpdateTOandINBExempt(flightDataList, 2);*/
                        gdpChart.DataATSMSG[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 3 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataATFM[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 2 && f.TO >= startTime && f.TO < endTime).ToList();
                        gdpChart.DataFPL[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        gdpChart.FlightLists[i].Sort((x, y) => x.TO.Value.CompareTo(y.TO.Value));
                    }
                    else if (gdp.TrafficAreaId == 3)
                    {
                        List<FlightData> flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 7 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataPassed[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 6 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataSUR[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 4 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataATSMSG[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 3 && f.INB >= startTime && f.INB < endTime).ToList();
                        /*UpdateTOandINBExempt
                        flightDataList = UpdateTOandINBExempt(flightDataList, 3);*/
                        gdpChart.DataATFM[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 2 && f.INB >= startTime && f.INB < endTime).ToList();
                        gdpChart.DataFPL[i] = flightDataList.Count;
                        gdpChart.FlightLists[i].AddRange(flightDataList);

                        gdpChart.FlightLists[i].Sort((x, y) => x.INB.Value.CompareTo(y.INB.Value));
                    }
                    // gdpChart.Capacities[i] = (startTime < gdp.EndTime) ? gdp.CapacityPerHr : gdp.CapacityRecoveryPerHr; 
                    // startTime = endTime;
                    double capacity = (startTime < gdp.EndTime) ? gdp.CapacityPerHr : gdp.CapacityRecoveryPerHr;
                    if (startTime < gdp.EndTime && endTime > gdp.EndTime)
                    {
                        capacity = ((gdp.CapacityPerHr * (gdp.EndTime - startTime).TotalHours) + (gdp.CapacityRecoveryPerHr * (endTime - gdp.EndTime).TotalHours)) / (endTime - startTime).TotalHours;
                    }
                    gdpChart.Capacities[i] = (int)Math.Round(capacity * ((double)gdp.IntervalMin / 60.0));
                    startTime = endTime;

                }

                gdpChart.Capacities[numInterval] = gdpChart.Capacities[numInterval - 1];
                gdpChart.Time = DateTime.UtcNow.ToString("HH:mm:ss");

                // Kick-off Delayed Flights (beyond recovery period)
                if (gdp.TrafficAreaId == 1)
                {
                    List<GDPFlight> flightList = gdpflights.Where(f => f.Flight.ALDT == null && f.Flight.ELDTByTMCS == null && f.Flight.ELDTByDep == null && f.Flight.CLDT != null && f.Flight.CLDT >= startTime).ToList();
                    gdpChart.DataKickoffDelay[numInterval - 1] = flightList.Count;
                    foreach (var flight in flightList)
                    {
                        FlightData flightData = FlightData.GetFlightData(flight.Flight, 3, null, formatFlightData);
                        flightData.IsKickoffDelay = true;
                        gdpChart.FlightLists[numInterval - 1].Add(flightData);
                    }
                }
                else if (gdp.TrafficAreaId == 2)
                {
                    List<FlightData> flightDataList = flightDatas.Where(f => f.TO != null && f.FlightSourceId == 3 && f.TO >= startTime).ToList();
                    gdpChart.DataKickoffDelay[numInterval - 1] = flightDataList.Count;
                    foreach (FlightData flightData in flightDataList) flightData.IsKickoffDelay = true;
                    gdpChart.FlightLists[numInterval - 1].AddRange(flightDataList);
                }
                else
                {
                    List<FlightData> flightDataList = flightDatas.Where(f => f.INB != null && f.FlightSourceId == 3 && f.INB >= startTime).ToList();
                    gdpChart.DataKickoffDelay[numInterval - 1] = flightDataList.Count;
                    foreach (FlightData flightData in flightDataList) flightData.IsKickoffDelay = true;
                    gdpChart.FlightLists[numInterval - 1].AddRange(flightDataList);
                }

                return gdpChart;

            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return new GDPChart { Title = "Server Problem: GDP/RequestChart" };
            }
        }
        private List<FlightTrajectory> UpdateExemptTrajectory(List<FlightTrajectory> trajectories)
        {
            List<FlightTrajectory> result = new List<FlightTrajectory>();
            var tjexempts = trajectories.Where(t => t.Flight.IsExempt && t.FlightSourceId != 3).ToList();
            var tj = trajectories.Where(t => !t.Flight.IsExempt).ToList();
            result.AddRange(tjexempts);
            result.AddRange(tj);
            return result;

        }
        /* private List<FlightData> UpdateTOandINBExempt(List<FlightData> flightDatas, int point)
         {
             foreach (var itm in flightDatas)
             {
                 if (itm.Flight.IsExempt && point == 2 && itm.FlightSourceId < 4) // waypoint
                     itm.TOStr = itm.TOStr.Split(' ')[0].Trim() + " (E)";
                 if (itm.Flight.IsExempt && point > 2 && itm.FlightSourceId < 4) //sectors and other airspace
                     itm.INBStr = itm.INBStr.Split(' ')[0].Trim() + " (E)";
             }

             return flightDatas;
         }*/
        [HttpGet]
        [AllowAnonymous]
        public async Task<CTOTTrialModel> GetFlightTrial()
        {
            var config = await _context.GeneralConfiguration.FirstOrDefaultAsync();
            var ctotOptions = config.DefaultCtotOptions;
            var ctotTimes = config.DefaultCtotTime;
            var ctotBlock = config.CtotAirlineTime;
            var newCtotBuffer = config.NewCtotBufferTime;
            DateTime expired = DateTime.UtcNow - ctotTimes;

            var trials = await _context.GDPFlight.Where(g => g.IsTrial && !g.IsCancelled && g.TimeSaved >= expired).OrderBy(g => g.CTOT).ToListAsync();

            return new CTOTTrialModel { CtotFlights = trials, CtotOptions = ctotOptions, CtotTimeout = ctotTimes.TotalMilliseconds, CtotAirlineBlock = ctotBlock.TotalMilliseconds, NewCtotBuffer = newCtotBuffer.TotalMinutes };
        }

        [HttpPost]
        public async Task<GDPChart> Edit(GDP gdp)
        {
            try
            {
                var gdpexist = await GdpExists(gdp.Id);
                if (gdpexist == null) return new GDPChart { Title = "gdp does not exist" };
                if (!ModelState.IsValid) return new GDPChart { Title = string.Join("\n", ModelState.Values.SelectMany(v => v.Errors).Select(e => e.ErrorMessage)) };
                if (!gdp.IsValid()) return new GDPChart { Title = gdp.GetErrorMsg() };
                gdp.MakeValid();
                if (gdp.IsCancelled) //ยกเลิก GDP
                {
                    int count = 0;
                    GDPFlight[] mailAftnFlight = new GDPFlight[gdpexist.GDPFlights.Count];
                    GDP[] gdps = new GDP[1];
                    gdpexist.TimeCancelled = DateTime.UtcNow;
                    gdpexist.IsCancelled = gdp.IsCancelled;
                    gdpexist.IsExecuted = false;
                    gdpexist.IsActive = false;
                    /*gdpexist.GDPFlights.Clear();*/
                    /*not remove flight only cancelled it*/
                    if (gdpexist.GDPFlights.Count > 0)
                    {
                        foreach (var itm in gdpexist.GDPFlights)
                        {
                            if (itm.Flight.ATOT == null)
                            {

                                itm.IsCancelled = true;
                                itm.TimeCancelled = DateTime.UtcNow;
                                itm.Comment = null;
                                itm.Flight.CTOT = null;
                                itm.Flight.CLDT = null;
                                itm.Flight.COBT = null;
                                itm.Flight.CIBT = null;

                                if (itm.Flight.IsManaged) // ส่ง SLC เฉพาะไฟล์ทที่ออก CTOT ไปแล้ว
                                {
                                    mailAftnFlight[count] = itm;
                                    mailAftnFlight[count].Flight.CLDT = null;
                                    mailAftnFlight[count].Flight.CTOT = null;
                                    count = count + 1;

                                    /*update CTOT to IDEP    เอาคอมเม้นออกตอนเทสด้วย */
                                    if (EnableCTOTtoIdep) await cancelCTOTtoIdepAsync(itm.Flight, "C", IdepRegional);
                                }

                                itm.Flight.IsManaged = false; //ยกเลิกไฟล์ทที่โดน CTOT
                                gdps[0] = gdpexist;
                            }
                        }

                        /*Generate SLC and Send Email*/
                        await GenerateMailAftnAsync(gdps, mailAftnFlight.Where(m => m != null).ToArray(), MailType.Update, AFTNMsgType.SLC, null, gdps[0].Comment == null ? "" : gdps[0].Comment);
                    }
                }
                else //Edit GDP 
                {
                    List<GDPFlight> removeList = new List<GDPFlight>();
                    int count = 0;
                    GDPFlight[] mailAftnFlight = new GDPFlight[gdpexist.GDPFlights.Count];
                    GDP[] gdps = new GDP[1];

                    gdpexist.TimeUpdated = DateTime.UtcNow;
                    gdpexist.Designator = gdp.Designator;
                    gdpexist.TrafficAreaId = gdp.TrafficAreaId;
                    gdpexist.Point = gdp.Point;
                    gdpexist.LowerFlightLevel = gdp.LowerFlightLevel;
                    gdpexist.UpperFlightLevel = gdp.UpperFlightLevel;
                    gdpexist.RadiusNm = gdp.RadiusNm;
                    gdpexist.CapacityPerHr = gdp.CapacityPerHr;
                    gdpexist.StartTime = gdp.StartTime;
                    gdpexist.EndTime = gdp.EndTime;
                    gdpexist.EndRecoveryTime = gdp.EndRecoveryTime;
                    gdpexist.CapacityRecoveryPerHr = gdp.CapacityRecoveryPerHr;
                    gdpexist.ExemptADEP = gdp.ExemptADEP;
                    gdpexist.ExemptADES = gdp.ExemptADES;
                    gdpexist.ExemptAirlines = gdp.ExemptAirlines;
                    foreach (var itm in gdpexist.GDPFlights)
                    {
                        if (itm.Flight.CLDT < gdp.StartTime || itm.Flight.CLDT > gdp.EndRecoveryTime)
                        {
                            if (itm.Flight.IsManaged) //Issued CTOT ให้ยกเลิก 
                            {
                                itm.Flight.CTOT = null;
                                itm.Flight.CLDT = null;
                                itm.Flight.COBT = null;
                                itm.Flight.CIBT = null;
                                itm.Flight.IsManaged = false;

                                mailAftnFlight[count] = itm;
                                mailAftnFlight[count].Flight.CLDT = null;
                                mailAftnFlight[count].Flight.CTOT = null;
                                count = count + 1;

                                _context.Update(itm.Flight);
                                if (EnableCTOTtoIdep) await cancelCTOTtoIdepAsync(itm.Flight, "C", IdepRegional);
                            }

                            removeList.Add(itm);
                        }
                        //เอาออกจาก table GDPFLIGHTS ไปเลย

                        /* if (itm.Flight.CLDT < gdp.StartTime || itm.Flight.CLDT > gdp.EndRecoveryTime || !itm.Flight.IsManaged) // if current CLDT out of GDP Area or not managed
                         {
                             itm.IsCancelled = true;
                             itm.TimeCancelled = DateTime.UtcNow;
                             itm.Comment = null;
                             itm.Flight.CTOT = null;
                             itm.Flight.CLDT = null;
                             itm.Flight.COBT = null;
                             itm.Flight.CIBT = null;
                             if (itm.Flight.IsManaged)
                             {
                                 mailAftnFlight[count] = itm;
                                 mailAftnFlight[count].Flight.CLDT = null;
                                 mailAftnFlight[count].Flight.CTOT = null;
                                 count = count + 1;


                                 await cancelCTOTtoIdepAsync(itm.Flight, "C");
                             }
                             itm.Flight.IsManaged = false;
                         }*/
                        gdps[0] = gdpexist;
                    }
                    foreach (var gf in removeList) { gdpexist.GDPFlights.Remove(gf); }
                    /*Generate SLC and Send Email ส่ง SLC ในกรณีที่ไฟล์ท Managed แล้ว และออกนอกมาตรการ */
                    await GenerateMailAftnAsync(gdps, mailAftnFlight.Where(m => m != null).ToArray(), MailType.Update, AFTNMsgType.SLC, null, gdps[0].Comment == null ? "" : gdps[0].Comment);
                }

                _context.Update(gdpexist);
                if (await _context.SaveChangesAsync() > 0)
                    return await RequestChart(gdpexist);
                else
                    return new GDPChart { Title = "error update gdp" };
            }
            catch (Exception e)
            {
                throw e;
            }
        }

        /* For Cancel Ctot before Expired*/
        [HttpPost]
        public async Task<bool> CancelCtot(int fid)
        {
            try
            {
                var trials = await _context.GDPFlight.Where(g => g.FlightId == fid && g.IsTrial).ToListAsync();

                foreach (var t in trials)
                {
                    t.IsCancelled = true;
                }
                _context.UpdateRange(trials);

                await _context.SaveChangesAsync();

                return true;
            }
            catch (Exception e)
            {
                //throw e;
                return false;
            }
        }

        [HttpPost]
        public async Task<ApiResponse<string>> ConfirmCtot(int gid, int cid, string comment, string source)
        {
            try
            {
                var ipaddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString(); // HttpContext.Connection.RemoteIpAddress.ToString();
                var username = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Name);
                DateTime pCTOT = DateTime.Now;

                GDPFlight[] mailAftnFlight = new GDPFlight[1];
                // var gdps = await _context.GDP.Include(g => g.TrafficArea).Where(g => g.Id.Equals(gid)).ToArrayAsync();      
                var cflight = await _context.GDPFlight.Include(g => g.GDPs).Where(g => g.Id == cid).FirstOrDefaultAsync();

                if (cflight != null)
                {
                    var flight = await _context.Flight.Where(f => f.Id.Equals(cflight.FlightId)).FirstOrDefaultAsync();
                    var gflights = await _context.GDPFlight.Include(g => g.GDPs).Where(g => g.FlightId == cflight.FlightId && !g.IsCancelled).ToListAsync();
                    var gflight = gflights.Where(g => !g.IsTrial).FirstOrDefault();
                    var tflights = gflights.Where(t => t.IsTrial).ToList();
                    var gdps = gflight.GDPs.ToArray(); // update gdps

                    if(flight.ATOT != null || flight.ALDT !=  null)
                        return ApiResponse<string>.Fail("Flight already departed");

                    pCTOT = (flight.CTOT == null) ? flight.ETOT.Value : flight.CTOT.Value;

                    flight.CIBT = cflight.CIBT;
                    flight.COBT = cflight.COBT;
                    flight.CLDT = cflight.CLDT;
                    flight.CTOT = cflight.CTOT;
                    flight.EOBTAirline = cflight.NewEOBT;

                    gflight.CIBT = cflight.CIBT;
                    gflight.COBT = cflight.COBT;
                    gflight.CLDT = cflight.CLDT;
                    gflight.CTOT = cflight.CTOT;
                    gflight.Comment = comment; //+ "//Confirm Ctot";
                    gflight.Username = username;
                    gflight.Ip = ipaddress;
                    gflight.NewEOBT = cflight.NewEOBT;

                    foreach (var t in tflights)
                    {
                        t.IsCancelled = true;
                    }


                    _context.Update(gflight);
                    _context.UpdateRange(tflights);
                    _context.Update(flight);

                    mailAftnFlight[0] = gflight;
                    mailAftnFlight[0].Flight.CLDT = cflight.CLDT;
                    mailAftnFlight[0].Flight.CTOT = cflight.CTOT;
                    mailAftnFlight[0].Flight.EOBTAirline = cflight.NewEOBT;

                    /*update CTOT to IDEP    เอาคอมเม้นออกตอนเทสด้วย */
                    if (EnableCTOTtoIdep) await updateCTOTtoIdepAsync(flight, "U", IdepRegional);

                    /*Generate SRM and Send Email*/
                    await GenerateMailAftnAsync(gdps, mailAftnFlight, MailType.Update, AFTNMsgType.SRM, comment, gdps[0].Comment == null ? "" : gdps[0].Comment);


                    var log = "CTOT - User " + username + " Confirm CTOT Flight " + gflight.Flight.Callsign + " CTOT " + gflight.Flight.CTOT.Value;
                    ActionLog(log);
                    /* Add to CtotLog */
                    CtotLog clog = new CtotLog();
                    clog.FlightId = gflight.Flight.Id;
                    clog.Callsign = gflight.Flight.Callsign;
                    clog.NewEOBT = cflight.NewEOBT.Value;
                    clog.EOBT = flight.EOBT.Value;
                    clog.UserMachineIP = ipaddress;
                    clog.UserName = username;
                    clog.CTOT = pCTOT;
                    clog.NewCTOT = gflight.Flight.CTOT.Value;
                    clog.Source = source;
                    Logger.LogCtot(clog);
                }


                await _context.SaveChangesAsync();

                return ApiResponse<string>.Ok("Success");


            }
            catch (Exception e)
            {
                return ApiResponse<string>.Fail("An Error Occur, Please Try Again"); ;
            }
        }
        public class ApiResponse<T>
        {
            public bool Success { get; set; }
            public string Error { get; set; }
            public T Data { get; set; }

            public static ApiResponse<T> Ok(T data) =>
                new ApiResponse<T> { Success = true, Data = data };

            public static ApiResponse<T> Fail(string error) =>
                new ApiResponse<T> { Success = false, Error = error };
        }
        private async Task<string> GenerateMailAftnAsync(GDP[] gdps, GDPFlight[] mailAftnFlight, MailType mailType, AFTNMsgType msgType, String comment, String aftnComment)
        {
            string result = "";
            CTOTForwardReviewController ctotfwd = new CTOTForwardReviewController(_userManager, _context, _extraContext, _mailService, _viewrenderService);
            List<MailingList> mailingList = ctotfwd.GetMailingList();
            string url = Url.ActionLink("Index", "GDPManage");

            result = await MailAFTNGenerator.GenerateMailAsync(gdps, mailAftnFlight, mailingList, mailType, null, comment, url, _viewrenderService, _mailService);
            MailAFTNGenerator.GenerateSam(_context, mailAftnFlight, mailingList, msgType, gdps[0].Regul, gdps[0].Regcause, aftnComment); //get only first gdp's regul

            return result;
        }

        [HttpGet]
        [Route("GDPManage/UpdateChartbyGid/{id}")]
        public async Task<GDPChart> UpdateChartbyGid(int id)
        {
            var gdp = await GdpExists(id);
            if (gdp != null)
                return await RequestChart(gdp);
            else
                return new GDPChart { Title = "error update gdp chart" };
        }

        private async Task<GDP> GdpExists(int id)
        {
            return await _context.GDP.Include(g => g.GDPFlights).ThenInclude(g => g.Flight).Include(g => g.TrafficArea).Where(e => e.Id == id).FirstOrDefaultAsync();
        }

        /* Request from Qtft CtotOptimizer */
        [HttpPost]
        public async Task<CTOTTrialModel> RequestCtot(CtotInputModel ctot)
        {
            var result = new CTOTTrialModel();
            var config = await _context.GeneralConfiguration.FirstOrDefaultAsync();
            try
            {
                var ipaddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString(); // HttpContext.Connection.RemoteIpAddress.ToString();
                var username = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Name);
                var ctotOptions = config.DefaultCtotOptions;
                var ctotTimes = config.DefaultCtotTime;
                var current = DateTime.UtcNow;
                var expireTime = current - ctotTimes;
                var newEobt = CheckEobtRequest(ctot, config);

                var gdp = _context.GDP.Include(g => g.GDPFlights).Where(g => g.Id == ctot.Gdpid).FirstOrDefault();
                var diff = newEobt - ctot.Flight.EOBT;

                var gdpf = new GDPFlight();
                List<Flight> flights = new List<Flight>();
                List<FlightData> flightDatas = new List<FlightData>();
                var trials = await _context.GDPFlight.Include(g => g.Flight).Where(g => g.IsTrial && !g.IsCancelled && g.TimeSaved >= expireTime).ToListAsync();

                for (var i = 0; i < ctotOptions; i++)
                {
                    Flight nfpl = new Flight();
                    nfpl = newCtotFlight(ctot.Flight, "_TRIAL" + (i + 1), diff.Value, i + 1);
                    flights.Add(nfpl);
                }

                string formatLabel = FlightData.FORMAT_TIME;
                int numInterval = (int)Math.Ceiling((gdp.EndRecoveryTime.Subtract(gdp.StartTime).TotalMinutes) / (double)gdp.IntervalMin);
                gdp.TrafficArea = await _context.TrafficArea.FindAsync(gdp.TrafficAreaId);
                TrafficDemand trafficDemand = new TrafficDemand
                {
                    Id = gdp.Id,
                    TrafficAreaId = gdp.TrafficAreaId,
                    TrafficArea = gdp.TrafficArea,
                    Designator = gdp.Designator,
                    Point = gdp.Point,
                    IsArr = true,
                    StartTime = gdp.StartTime,
                    EndTime = gdp.EndRecoveryTime,
                    IntervalMin = gdp.IntervalMin,
                    IsIFR = gdp.IsIFR,
                    IsVFR = gdp.IsVFR,
                    IsFPL = true,
                    IsATFM = true,
                    IsATSMSG = true,
                    IsSUR = true,
                    IsPassed = true,
                    TimeSaved = gdp.TimeSaved
                };
                CTOTFlightInput[] ctotFlightInputs = null;
                CTOTMeasure[] ctotMeasures = new CTOTMeasure[1];
                ctotMeasures[0] = new CTOTMeasure
                {
                    MeasureId = gdp.Id,
                    StartTime = JsonConvert.SerializeObject(gdp.StartTime).Trim('"'),
                    EndTime = JsonConvert.SerializeObject(gdp.EndTime).Trim('"'),
                    EndRecovery = JsonConvert.SerializeObject(gdp.EndRecoveryTime).Trim('"'),
                    Capacity = (int)Math.Round(gdp.CapacityPerHr * doubleHCEdivided), //use HEC from appSettings
                    CapacityRecovery = (int)Math.Round(gdp.CapacityRecoveryPerHr * doubleHCEdivided), //use HEC from appSettings
                    TrafficAreaType = "ArrivalAirport",
                    TrafficAreaName = gdp.Point
                };
                CTOTAlgorithm ctotAlgorithm = new CTOTAlgorithm
                {
                    AlgorithmName = "Network-based", //"Fast-network-based",
                    Parameters = new CTOTParameters
                    {
                        StartTime = JsonConvert.SerializeObject(gdp.StartTime.AddHours(-1)).Trim('"'),
                        EndTime = JsonConvert.SerializeObject(gdp.EndRecoveryTime.AddHours(1)).Trim('"'),
                        HaveAirborneDelay = false,
                        HECPeriod = HEC,  // use HEC from AppSettings
                        Alpha = alpha,
                        FastMaxDelayIndex = fastMaxDelayIndex,
                        MaxDelayIndex = fastMaxDelayIndex,
                        TimeLimit = timeLimit,
                        WindowStep = windowStep
                    }
                };
                if (gdp.TrafficAreaId == 1)
                {
                    Airport airport = await _context.Airport.FirstOrDefaultAsync(a => a.DESIGNATOR == gdp.Point && !a.ROWDELETE);


                    flights.AddRange(await _context.Flight.Where(f => !f.IsCancelled && f.AirportArrival == gdp.Point &&
                    ((f.ALDT != null && f.ALDT >= gdp.StartTime && f.ALDT < gdp.EndRecoveryTime) ||
                    (f.ALDT == null && f.ELDTByTMCS != null && f.ELDTByTMCS >= gdp.StartTime && f.ELDTByTMCS < gdp.EndRecoveryTime) ||
                    (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep != null && f.ELDTByDep >= gdp.StartTime && f.ELDTByDep < gdp.EndRecoveryTime) ||
                    (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT != null && f.CLDT >= gdp.StartTime && f.CLDT < gdp.EndRecoveryTime) ||
                    (f.ALDT == null && f.ELDTByTMCS == null && f.ELDTByDep == null && f.CLDT == null && f.ELDT != null && f.ELDT >= gdp.StartTime && f.ELDT < gdp.EndRecoveryTime))).ToListAsync());

                    /* ADD GDP TRIALS */
                    for (var i = 0; i < trials.Count; i++)
                    {
                        if (flights.Any(g => g.Id.Equals(trials[i].FlightId)))
                        {
                            flights.Add(new Flight
                            {
                                Id = (trials[i].FlightId * 100) + i,
                                Callsign = trials[i].Flight.Callsign + "_0" + i,
                                COBT = trials[i].COBT,
                                CIBT = trials[i].CIBT,
                                CTOT = trials[i].CTOT,
                                CLDT = trials[i].CLDT,
                                AirportDeparture = trials[i].Flight.AirportDeparture,
                                AirportArrival = trials[i].Flight.AirportArrival,
                                IsExempt = false //เปลี่ยนเป็น False เพื่อให้ Optimizer  เอาไป solve
                            });
                        }
                    }
                    ctotFlightInputs = new CTOTFlightInput[flights.Count];
                    for (int i = 0; i < ctotFlightInputs.Length; i++)
                    {
                        ctotFlightInputs[i] = new CTOTFlightInput
                        {
                            SectorTrajectory = Array.Empty<SectorTrajectory>(),
                            WaypointTrajectory = Array.Empty<WaypointTrajectory>()
                        };
                    }
                    ctotMeasures[0].TrafficAreaType = "ArrivalAirport";

                }
                else if (gdp.TrafficAreaId == 2)
                {
                    Fix fix = await _context.Fix.FirstOrDefaultAsync(a => a.DESIGNATOR == gdp.Point && !a.ROWDELETE);
                    int radiusNm = gdp.RadiusNm != null ? gdp.RadiusNm.Value : fix.RadiusNm;
                    double radius = radiusNm / 0.0006213712;
                    Geometry circle = await _context.Fix.Where(f => f.ID == fix.ID).Select(f => f.GEOPOINT.Buffer(radius)).FirstOrDefaultAsync();
                    int minLevel = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : fix.MinLevelFt;
                    int maxLevel = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : fix.MaxLevelFt;
                    List<FlightTrajectory> trajectoryList = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();

                    TDChart tdChart = new TDChart
                    {
                        TrafficDemand = trafficDemand,
                        Labels = new string[numInterval],
                        Titles = new string[numInterval],
                        DataFPL = new int[numInterval],
                        DataATFM = new int[numInterval],
                        DataATSMSG = new int[numInterval],
                        DataSUR = new int[numInterval],
                        DataPassed = new int[numInterval],
                        FlightLists = new List<FlightData>[numInterval],
                        Capacities = new int[numInterval + 1]
                    };
                    TrafficDemandController.PrepareFlightLists(trajectoryList, new Geometry[] { circle }, tdChart, gdp.StartTime, gdp.EndRecoveryTime, new int[] { minLevel }, new int[] { maxLevel }, formatLabel);
                    for (int i = 0; i < numInterval; i++)
                    {
                        for (int j = 0; j < tdChart.FlightLists[i].Count; j++)
                        {
                            FlightData flightData = tdChart.FlightLists[i][j];
                            flights.Add(flightData.Flight);
                            flightDatas.Add(flightData);
                        }
                    }
                    /* ADD TRIAL FLIGHT */
                    var tcount = 0;
                    for (var i = 0; i < trials.Count; i++)
                    {
                        if (flights.Any(g => g.Id.Equals(trials[i].FlightId)))
                        {
                            flights.Add(new Flight
                            {
                                Id = (trials[i].FlightId * 100) + i,
                                Callsign = trials[i].Flight.Callsign + "_0" + i,
                                COBT = trials[i].COBT,
                                CIBT = trials[i].CIBT,
                                CTOT = trials[i].CTOT,
                                CLDT = trials[i].CLDT,
                                AirportDeparture = trials[i].Flight.AirportDeparture,
                                AirportArrival = trials[i].Flight.AirportArrival,
                                IsExempt = false //เปลี่ยนเป็น False เพื่อให้ Optimizer  เอาไป solve
                            });
                            tcount += tcount;  //count trials add in flights
                        }
                    }
                    ctotFlightInputs = new CTOTFlightInput[flightDatas.Count + tcount];
                    for (int i = 0; i < flightDatas.Count; i++)
                    {
                        ctotFlightInputs[i] = new CTOTFlightInput
                        {
                            SectorTrajectory = Array.Empty<SectorTrajectory>(),
                            WaypointTrajectory = new WaypointTrajectory[]
                            {
                                    new WaypointTrajectory
                                    {
                                        WaypointName = gdp.Point,
                                        ETO = JsonConvert.SerializeObject(flightDatas[i].TO.Value).Trim('"')
                                    }
                            }
                        };
                    }
                    /* ADD TRIAL IN ctotFlightInputs */
                    for (int j = 0; j < tcount; j++)
                    {
                        var k = j + flightDatas.Count;
                        ctotFlightInputs[k] = new CTOTFlightInput
                        {
                            SectorTrajectory = Array.Empty<SectorTrajectory>(),
                            WaypointTrajectory = Array.Empty<WaypointTrajectory>(),
                        };
                    }
                    ctotMeasures[0].TrafficAreaType = "Waypoint";

                }
                else
                {
                    List<FlightTrajectory> trajectoryList = new List<FlightTrajectory>();
                    Geometry[] geometries = null;
                    int[] minLevels = null, maxLevels = null;
                    if (gdp.TrafficAreaId == 3)
                    {
                        StaticAirspace[] sectors = await _context.StaticAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();

                        minLevels = new int[sectors.Length];
                        maxLevels = new int[sectors.Length];
                        geometries = new Geometry[sectors.Length];
                        for (int i = 0; i < sectors.Length; i++)
                        {
                            minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : sectors[i].LowerLimitFt;
                            maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : sectors[i].UpperLimitFt;
                            geometries[i] = sectors[i].Geography;
                            List<FlightTrajectory> trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();

                            trajectoryList.AddRange(trajectoryListTemp);
                        }

                    }
                    else if (gdp.TrafficAreaId == 4)
                    {
                        UserDefinedAirspace[] airspaces = await _context.UserDefinedAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();

                        minLevels = new int[airspaces.Length];
                        maxLevels = new int[airspaces.Length];
                        geometries = new Geometry[airspaces.Length];
                        for (int i = 0; i < airspaces.Length; i++)
                        {
                            minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : airspaces[i].LowerLimitFt;
                            maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : airspaces[i].UpperLimitFt;
                            geometries[i] = airspaces[i].Geography;
                            List<FlightTrajectory> trajectoryListTemp = await _context.FlightTrajectory.Where(t => t.StartTime < gdp.EndRecoveryTime && t.EndTime >= gdp.StartTime && t.IsActive).Include(f => f.Flight).ToListAsync();

                            trajectoryList.AddRange(trajectoryListTemp);
                        }

                    }

                    TDChart tdChart = new TDChart
                    {
                        TrafficDemand = trafficDemand,
                        Labels = new string[numInterval],
                        Titles = new string[numInterval],
                        DataFPL = new int[numInterval],
                        DataATFM = new int[numInterval],
                        DataATSMSG = new int[numInterval],
                        DataSUR = new int[numInterval],
                        DataPassed = new int[numInterval],
                        FlightLists = new List<FlightData>[numInterval],
                        Capacities = new int[numInterval + 1]
                    };
                    TrafficDemandController.PrepareFlightLists(trajectoryList, geometries, tdChart, gdp.StartTime, gdp.EndRecoveryTime, minLevels, maxLevels, formatLabel);
                    for (int i = 0; i < numInterval; i++)
                    {
                        for (int j = 0; j < tdChart.FlightLists[i].Count; j++)
                        {
                            FlightData flightData = tdChart.FlightLists[i][j];
                            flights.Add(flightData.Flight);
                            flightDatas.Add(flightData);
                        }
                    }

                    /* ADD TRIAL FLIGHT */
                    var tcount = 0;
                    for (var i = 0; i < trials.Count; i++)
                    {
                        if (flights.Any(g => g.Id.Equals(trials[i].FlightId)))
                        {
                            flights.Add(new Flight
                            {
                                Id = (trials[i].FlightId * 100) + i,
                                Callsign = trials[i].Flight.Callsign + "_0" + i,
                                COBT = trials[i].COBT,
                                CIBT = trials[i].CIBT,
                                CTOT = trials[i].CTOT,
                                CLDT = trials[i].CLDT,
                                AirportDeparture = trials[i].Flight.AirportDeparture,
                                AirportArrival = trials[i].Flight.AirportArrival,
                                IsExempt = false //เปลี่ยนเป็น False เพื่อให้ Optimizer  เอาไป solve
                            });
                            tcount += tcount;  //count trials add in flights
                        }
                    }

                    ctotFlightInputs = new CTOTFlightInput[flightDatas.Count + tcount];
                    for (int i = 0; i < flightDatas.Count; i++)
                    {
                        ctotFlightInputs[i] = new CTOTFlightInput
                        {
                            SectorTrajectory = new SectorTrajectory[]
                            {
                                    new SectorTrajectory
                                    {
                                        SectorName = gdp.Point,
                                        InboundTime = JsonConvert.SerializeObject(flightDatas[i].INB.Value).Trim('"')
                                    }
                            },
                            WaypointTrajectory = Array.Empty<WaypointTrajectory>()
                        };
                    }
                    /* ADD TRIAL IN ctotFlightInputs */
                    for (int j = 0; j < tcount; j++)
                    {
                        var k = j + flightDatas.Count;
                        ctotFlightInputs[k] = new CTOTFlightInput
                        {
                            SectorTrajectory = Array.Empty<SectorTrajectory>(),
                            WaypointTrajectory = Array.Empty<WaypointTrajectory>(),
                        };
                    }
                    ctotMeasures[0].TrafficAreaType = "Sector";

                }

                /* CREATE OPTIMIZER */
                HashSet<string> exemptAdepSet = gdp.ExemptADEP != null ? gdp.ExemptADEP.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                HashSet<string> exemptAdesSet = gdp.ExemptADES != null ? gdp.ExemptADES.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                HashSet<string> exemptAirlineSet = gdp.ExemptAirlines != null ? gdp.ExemptAirlines.Split(',', StringSplitOptions.TrimEntries).ToHashSet() : new HashSet<string>();
                flights = tempExcludeEobtReqFlight(flights);
                for (int i = 0; i < ctotFlightInputs.Length; i++)
                {
                    Flight flight = flights[i];
                    ctotFlightInputs[i].FlightId = flight.Id;
                    ctotFlightInputs[i].IsExcluded = flight.IsExclude;
                    ctotFlightInputs[i].IsExempted = flight.IsExempt || flight.ATOT.HasValue || exemptAdepSet.Any(s => flight.AirportDeparture.StartsWith(s)) || exemptAdesSet.Any(s => flight.AirportArrival.StartsWith(s)) || exemptAirlineSet.Any(s => flight.Callsign.StartsWith(s));
                    ctotFlightInputs[i].IsManaged = flight.IsManaged;
                    ctotFlightInputs[i].ArrivalAirport = flight.AirportArrival;
                    ctotFlightInputs[i].DepartureAirport = flight.AirportDeparture;
                    ctotFlightInputs[i].ETOT = JsonConvert.SerializeObject(flight.ATOT ?? (flight.CTOT ?? flight.ETOT.Value)).Trim('"');
                    ctotFlightInputs[i].ELDT = JsonConvert.SerializeObject(flight.ALDT ?? (flight.ELDTByTMCS ?? (flight.ELDTByDep ?? (flight.CLDT ?? flight.ELDT.Value)))).Trim('"');
                }
                CTOTOptimizeInput input = new CTOTOptimizeInput
                {
                    Measures = ctotMeasures,
                    Algorithm = ctotAlgorithm,
                    Flights = ctotFlightInputs
                };
                CTOTOptimizeOutput ctotOptimizeOutput = await GetCtotFlightsAsync(input);

                var isTrialfound = false;
                DateTime pCtot = DateTime.MinValue;
                result.IsGdpflight = false;
                if (ctotOptimizeOutput == null) result.ErrMsg = "Optimizer Cannot Resove CTOT";
                if (ctotOptimizeOutput != null && ctotOptimizeOutput.GroundDelayedFlights != null)
                {
                    for (int i = 0; i < ctotOptimizeOutput.GroundDelayedFlights.Length; i++)
                    {
                        CTOTGroundDelayedFlight ctotFlight = ctotOptimizeOutput.GroundDelayedFlights[i];
                        Flight flight = flights.Where(f => f.Id == ctotFlight.FlightId && f.Callsign.Contains("_TRIAL")).FirstOrDefault();
                        if (flight != null)
                        {
                            isTrialfound = true;
                            flight.CLDT = flight.ELDT.Value.AddMinutes(ctotFlight.GroundDelay);
                            if (IsGdpflight(flight) && pCtot != flight.ETOT.Value.AddMinutes(ctotFlight.GroundDelay))
                            {
                                result.IsGdpflight = true;
                                GDPFlight gdpFlight = new GDPFlight
                                {
                                    FlightId = flight.Id / 10,
                                    COBT = flight.EOBT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    CTOT = flight.ETOT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    CLDT = flight.ELDT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    CIBT = flight.EIBT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    COBTSaved = flight.EOBT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    CTOTSaved = flight.ETOT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    CLDTSaved = flight.ELDT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    CIBTSaved = flight.EIBT.Value.AddMinutes(ctotFlight.GroundDelay),
                                    TimeSaved = DateTime.UtcNow,
                                    IsTrial = true,
                                    NewEOBT = ctot.NewEobt,
                                    Comment = ctot.Comment
                                };
                                _context.Add(gdpFlight);
                            }
                            pCtot = flight.ETOT.Value.AddMinutes(ctotFlight.GroundDelay);
                        }
                        //else
                        //    result.ErrMsg = "no flight trial found";
                    }

                    if (isTrialfound && !result.IsGdpflight) //not in gdp
                    {
                        /* var gdpfpl = await _context.GDPFlight.Where(f => f.FlightId == ctot.Flight.Id && !f.IsTrial && !f.IsCancelled).FirstOrDefaultAsync();
                         var fpl = await _context.Flight.Where(f => f.Id == ctot.Flight.Id).FirstOrDefaultAsync();

                         if (gdpfpl != null)
                         {
                             gdpf = gdpfpl;
                             gdpfpl.IsCancelled = true;
                             gdpfpl.TimeCancelled = DateTime.UtcNow;
                             gdpfpl.Comment = ctot.Comment;
                             gdpfpl.Username = username;
                             gdpfpl.Ip = ipaddress;
                             gdpfpl.NewEOBT = ctot.NewEobt;

                             _context.Update(gdpfpl);
                             _context.Update(gdp);
                         }
                         if (fpl != null)
                         {
                             fpl.IsManaged = false;
                             fpl.IsExclude = false;
                             fpl.IsExempt = false;
                             fpl.CTOT = null;
                             fpl.CLDT = null;
                             fpl.COBT = null;
                             fpl.CIBT = null;
                             fpl.EOBTAirline = ctot.NewEobt;

                             _context.Update(fpl);
                         } */
                        result.ErrMsg = "Flight out of Ground Delay Program , Please contact ATFMU at +6622878024";
                    }
                    await _context.SaveChangesAsync();

                }
                else result.ErrMsg = "Optimizer Cannot Resove CTOT";
                DateTime expired = DateTime.UtcNow - ctotTimes;
                result.CtotFlights = new List<GDPFlight>();
                result.CtotFlights = await _context.GDPFlight.Where(g => g.IsTrial && !g.IsCancelled && g.TimeSaved >= expired).OrderByDescending(g => g.TimeSaved).OrderBy(c => c.CTOT).ToListAsync();
                result.CtotTimeout = ctotTimes.TotalMilliseconds;
                result.CtotOptions = ctotOptions;
                result.GdpId = gdp.Id;
                result.RequestedOBT = ctot.NewEobt;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
            }
            return result;

        }

         private List<Flight> tempExcludeEobtReqFlight(List<Flight> fpls)
        {
            var trial = fpls.Where(f => f.Callsign.Contains("_TRIAL")).FirstOrDefault();
            if (trial != null)
            {
                var id = trial.Id / 10;
                fpls = fpls.Select(x =>
                {
                    if (x.Id == id)
                    {
                        x.IsExclude = true;
                    }
                    return x;
                }).ToList();
            }
            return fpls;
        } 

        /* get flight data update by flightid*/
        [HttpGet]
        [Route("GDPManage/GetFlightDataUpdate/{flightid}/{gid}")]
        public async Task<FlightData> GetFlightDataUpdate(int flightid, int gid)
        {
            var gdp = _context.GDP.Where(g => g.Id == gid).FirstOrDefault();
            var tid = gdp.TrafficAreaId;
            FlightData fdta = new FlightData();
            string formatFlightData = FlightData.FORMAT_TIME;

            if (tid == 1) //airport
            {
                var flight = await _context.Flight.Where(f => f.Id == flightid).FirstOrDefaultAsync();
                fdta = FlightData.GetFlightData(flight, tid, null, formatFlightData);
            }
            if (tid == 2) //waypoint
            {

                Fix fix = await _context.Fix.FirstOrDefaultAsync(a => a.DESIGNATOR == gdp.Point && !a.ROWDELETE);
                //if (fix == null) return new GDPChart { Title = "Fix <" + gdp.Point + "> is not found." };
                int radiusNm = gdp.RadiusNm != null ? gdp.RadiusNm.Value : fix.RadiusNm;
                double radius = radiusNm / 0.0006213712;
                Geometry circle = await _context.Fix.Where(f => f.ID == fix.ID).Select(f => f.GEOPOINT.Buffer(radius)).FirstOrDefaultAsync();
                int minLevel = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : fix.MinLevelFt;
                int maxLevel = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : fix.MaxLevelFt;


                FlightTrajectory trajectoryFlight = _context.FlightTrajectory.Where(t => t.FlightId == flightid).OrderByDescending(t => t.FlightSourceId).FirstOrDefault();
                List<Trajectory> trajectoryList = new List<Trajectory>();
                List<Trajectory> trajectoryOutList = new List<Trajectory>();
                int[] altitudes = Array.ConvertAll(trajectoryFlight.AltitudeFt.Split(','), int.Parse);
                int[] states = Array.ConvertAll(trajectoryFlight.FlightState.Split(','), int.Parse);
                int[] headings = Array.ConvertAll(trajectoryFlight.Heading.Split(','), int.Parse);
                int[] speeds = Array.ConvertAll(trajectoryFlight.SpeedKn.Split(','), int.Parse);
                bool isIn = false, isOut = false;
                int[] minLevels = new int[] { minLevel };
                int[] maxLevels = new int[] { maxLevel };
                Geometry[] circles = new Geometry[] { circle };
                for (int i = 0; i < trajectoryFlight.PositionLine.Count; i++)
                {
                    Geometry positionLine = trajectoryFlight.PositionLine.Geometries[i];
                    for (int k = 0; k < circles.Length; k++)
                    {
                        if (altitudes[i] >= minLevels[k] && altitudes[i] <= maxLevels[k] && positionLine.Intersects(circles[k]))
                        {
                            if (!isIn)
                            {
                                trajectoryList.Add(ConvertToTrajectory(trajectoryFlight, states[i], altitudes[i], headings[i], speeds[i], i));
                                isIn = true;
                            }
                            //else
                            //{
                            //    trajectoryOutList.Add(ConvertToTrajectory(trajectoryListInput[i], states[j], altitudes[j], headings[j], speeds[j], j));
                            //    isOut = true;
                            //}
                            break;
                        }
                        else
                        {
                            if (isIn && !isOut)
                            {
                                trajectoryOutList.Add(ConvertToTrajectory(trajectoryFlight, states[i], altitudes[i], headings[i], speeds[i], i));
                                isOut = true;
                                break;
                            }
                        }
                    }
                    if (isIn && (isOut || tid == 2)) break;
                }
                if (isIn && !isOut && tid != 2)
                {
                    int index = trajectoryFlight.PositionLine.Count - 1;
                    trajectoryOutList.Add(ConvertToTrajectory(trajectoryFlight, states[index], altitudes[index], headings[index], speeds[index], index));
                }

                if (trajectoryOutList != null)
                {
                    foreach (Trajectory trajectory in trajectoryList)
                    {
                        Trajectory[] trajectoryInGroups = trajectoryList.Where(t => t.FlightId == trajectory.FlightId).ToArray();
                        Trajectory[] trajectoryOutGroups = trajectoryOutList.Where(t => t.FlightId == trajectory.FlightId).ToArray();
                        Trajectory trajectoryOut = trajectoryOutGroups.Where(t => t.FlightSourceId == trajectory.FlightSourceId).FirstOrDefault();
                        fdta = FlightData.GetFlightData(trajectory, trajectoryOut, trajectoryInGroups, trajectoryOutGroups, tid, null, formatFlightData);
                    }
                }
                else
                {
                    foreach (Trajectory trajectory in trajectoryList)
                    {
                        Trajectory[] trajectoryGroups = trajectoryList.Where(t => t.FlightId == trajectory.FlightId).ToArray();
                        fdta = FlightData.GetFlightData(trajectory, trajectoryGroups, tid, null, formatFlightData);
                    }
                }
            }
            if (tid > 2) //sector and user-defined airspace
            {
                List<Trajectory> trajectoryList = new List<Trajectory>();
                Geometry[] geometries = null;
                int[] minLevels = null, maxLevels = null;
                FlightTrajectory trajectoryFlight = _context.FlightTrajectory.Where(t => t.FlightId == flightid).OrderByDescending(t => t.FlightSourceId).FirstOrDefault();
                if (tid == 3)
                {
                    StaticAirspace[] sectors = await _context.StaticAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();
                    //if (sectors == null || sectors.Length == 0) return new GDPChart { Title = "Sector <" + gdp.Point + "> is not found." };
                    minLevels = new int[sectors.Length];
                    maxLevels = new int[sectors.Length];
                    geometries = new Geometry[sectors.Length];



                    for (int i = 0; i < sectors.Length; i++)
                    {
                        minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : sectors[i].LowerLimitFt;
                        maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : sectors[i].UpperLimitFt;
                        geometries[i] = sectors[i].Geography;
                    }
                }
                if (tid == 4)
                {
                    UserDefinedAirspace[] airspaces = await _context.UserDefinedAirspace.Where(f => f.Name == gdp.Point).ToArrayAsync();
                    //if (airspaces == null || airspaces.Length == 0) return new GDPChart { Title = "Airspace <" + gdp.Point + "> is not found." };
                    minLevels = new int[airspaces.Length];
                    maxLevels = new int[airspaces.Length];
                    geometries = new Geometry[airspaces.Length];

                    for (int i = 0; i < airspaces.Length; i++)
                    {
                        minLevels[i] = gdp.LowerFlightLevel != null ? gdp.LowerFlightLevel.Value * 100 : airspaces[i].LowerLimitFt;
                        maxLevels[i] = gdp.UpperFlightLevel != null ? gdp.UpperFlightLevel.Value * 100 : airspaces[i].UpperLimitFt;
                        geometries[i] = airspaces[i].Geography;

                    }
                }


                List<Trajectory> trajectoryOutList = new List<Trajectory>();
                int[] altitudes = Array.ConvertAll(trajectoryFlight.AltitudeFt.Split(','), int.Parse);
                int[] states = Array.ConvertAll(trajectoryFlight.FlightState.Split(','), int.Parse);
                int[] headings = Array.ConvertAll(trajectoryFlight.Heading.Split(','), int.Parse);
                int[] speeds = Array.ConvertAll(trajectoryFlight.SpeedKn.Split(','), int.Parse);
                bool isIn = false, isOut = false;
                //int[] minLevels = new int[] { minLevel };
                //int[] maxLevels = new int[] { maxLevel };
                // Geometry[] circles = new Geometry[] { circle };
                for (int i = 0; i < trajectoryFlight.PositionLine.Count; i++)
                {
                    Geometry positionLine = trajectoryFlight.PositionLine.Geometries[i];
                    for (int k = 0; k < geometries.Length; k++)
                    {
                        if (altitudes[i] >= minLevels[k] && altitudes[i] <= maxLevels[k] && positionLine.Intersects(geometries[k]))
                        {
                            if (!isIn)
                            {
                                trajectoryList.Add(ConvertToTrajectory(trajectoryFlight, states[i], altitudes[i], headings[i], speeds[i], i));
                                isIn = true;
                            }
                            //else
                            //{
                            //    trajectoryOutList.Add(ConvertToTrajectory(trajectoryListInput[i], states[j], altitudes[j], headings[j], speeds[j], j));
                            //    isOut = true;
                            //}
                            break;
                        }
                        else
                        {
                            if (isIn && !isOut)
                            {
                                trajectoryOutList.Add(ConvertToTrajectory(trajectoryFlight, states[i], altitudes[i], headings[i], speeds[i], i));
                                isOut = true;
                                break;
                            }
                        }
                    }
                    if (isIn && (isOut || tid == 2)) break;
                }
                if (isIn && !isOut && tid != 2)
                {
                    int index = trajectoryFlight.PositionLine.Count - 1;
                    trajectoryOutList.Add(ConvertToTrajectory(trajectoryFlight, states[index], altitudes[index], headings[index], speeds[index], index));
                }

                if (trajectoryOutList != null)
                {
                    foreach (Trajectory trajectory in trajectoryList)
                    {
                        Trajectory[] trajectoryInGroups = trajectoryList.Where(t => t.FlightId == trajectory.FlightId).ToArray();
                        Trajectory[] trajectoryOutGroups = trajectoryOutList.Where(t => t.FlightId == trajectory.FlightId).ToArray();
                        Trajectory trajectoryOut = trajectoryOutGroups.Where(t => t.FlightSourceId == trajectory.FlightSourceId).FirstOrDefault();
                        fdta = FlightData.GetFlightData(trajectory, trajectoryOut, trajectoryInGroups, trajectoryOutGroups, tid, null, formatFlightData);
                    }
                }
                else
                {
                    foreach (Trajectory trajectory in trajectoryList)
                    {
                        Trajectory[] trajectoryGroups = trajectoryList.Where(t => t.FlightId == trajectory.FlightId).ToArray();
                        fdta = FlightData.GetFlightData(trajectory, trajectoryGroups, tid, null, formatFlightData);
                    }
                }


            }
            return fdta;
        }
        private static Trajectory ConvertToTrajectory(FlightTrajectory flightTrajectory, int state, int altitude, int heading, int speed, int index)
        {
            Trajectory trajectory = new Trajectory
            {
                Id = flightTrajectory.Id,
                FlightId = flightTrajectory.FlightId,
                FlightSourceId = flightTrajectory.FlightSourceId,
                FlightStateId = state,
                AltitudeFt = altitude,
                Time = flightTrajectory.StartTime.AddSeconds(index * 30),
                Heading = heading,
                SpeedKn = speed,
                TimeGenerated = flightTrajectory.TimeGenerated,
                IsActive = flightTrajectory.IsActive,
                Flight = flightTrajectory.Flight
            };
            return trajectory;
        }
        private DateTime CheckEobtRequest(CtotInputModel ctot, GeneralConfiguration config)
        {
            var buffer = config.NewCtotBufferTime;
            var stt = config.DefaultTaxiTime;
            var airport = _context.Airport.Where(a => a.DESIGNATOR.Equals(ctot.Flight.AirportDeparture) && !a.ROWDELETE).FirstOrDefault();
            if (airport != null)
                stt = TimeSpan.FromMinutes(airport.TAXIOUT);
            var d = DateTime.UtcNow;
            var current = new DateTime(d.Year, d.Month, d.Day, d.Hour, d.Minute, d.Second); // get only HH:mm
            var newCtot = ctot.NewEobt + stt;
            if (newCtot > current + buffer)
                return ctot.NewEobt.Value;
            else
                return current + buffer - stt; // new EOBT
        }
        private bool IsGdpflight(Flight flight)
        {
           // var gdp = _context.GDP.Where(g => g.EndRecoveryTime >= flight.CLDT).FirstOrDefault();
           // if (gdp == null)
            //    return false;
           // else
                return true;
        }


        [HttpPost]
        public async Task<GDPChart> RequestRemoveCtot(int gdpid, int fid, string removeStr, string comment) //ขอ Exempt หรือ Exclude
        {
            if (await UpdateRemoveFlight(fid, removeStr, comment, false))
                return await RequestChart(await GdpExists(gdpid));
            else
                return new GDPChart { Title = "Server Problem: GDP/RequestChart" };

        }
        /* helpers */

        [HttpPost]
        public async Task<bool> UpdateRemoveFlight(int fid, string removeStr, string comment, bool isUndo) //ขอ Exempt หรือ Exclude
        {
            try
            {
                var ipaddress = _httpContextAccessor.HttpContext?.Connection?.RemoteIpAddress?.ToString(); // HttpContext.Connection.RemoteIpAddress.ToString();
                var username = _httpContextAccessor.HttpContext?.User?.FindFirstValue(ClaimTypes.Name);
                var gdpf = new GDPFlight();

                var gdpfpls = await _context.GDPFlight.Include(f => f.GDPs).Where(f => f.FlightId == fid).ToListAsync();  // Include Trials       
                var fpl = await _context.Flight.Where(f => f.Id == fid).FirstOrDefaultAsync();
                GDPFlight[] mailAftnFlight = new GDPFlight[1];


                if (gdpfpls.Count > 0)
                {
                    gdpf = gdpfpls.Where(g => !g.IsTrial).FirstOrDefault();
                    foreach (var gdpfpl in gdpfpls)
                    {
                        if (gdpfpl.IsTrial)
                        {
                            gdpfpl.IsCancelled = true;
                            gdpfpl.TimeCancelled = DateTime.UtcNow;
                        }
                        else
                        {
                            if (removeStr == "exempt")
                            {
                                gdpfpl.IsGdpExempt = true;
                                gdpfpl.IsCancelled = false;
                                gdpfpl.CTOT = null;
                                gdpfpl.CLDT = null;
                                gdpfpl.COBT = null;
                                gdpfpl.CIBT = null;
                            }
                            else
                            {
                                gdpfpl.IsCancelled = true;
                                gdpfpl.TimeCancelled = DateTime.UtcNow;
                            }
                        }

                        gdpfpl.Username = username;
                        gdpfpl.Ip = ipaddress;
                        gdpfpl.Comment = comment;

                        _context.Update(gdpfpl);


                    }
                }
                if (fpl != null)
                {
                    //fpl.IsManaged = false; Remove because Optimizer Included
                    fpl.CTOT = null;
                    fpl.CLDT = null;
                    fpl.COBT = null;
                    fpl.CIBT = null;
                    fpl.IsExempt = removeStr == "exempt";
                    fpl.IsExclude = removeStr == "exclude";
                    _context.Update(fpl);
                }

                await _context.SaveChangesAsync();

                if (!isUndo && gdpf.Flight.IsManaged) // ส่ง SLC เฉพาะ Flight ที่ Isuue CTOT ไปแล้ว
                {
                    var gdps = gdpf.GDPs.ToArray();
                    mailAftnFlight[0] = gdpf;
                    mailAftnFlight[0].Flight.CLDT = null;
                    mailAftnFlight[0].Flight.CTOT = null;

                    /* cancel CTOT to IDEP  เอาคอมเม้นออกตอนเทสด้วย*/
                    if (EnableCTOTtoIdep) await cancelCTOTtoIdepAsync(gdpf.Flight, "C", IdepRegional);

                    /*Generate SLC and Send Email*/
                    await GenerateMailAftnAsync(gdps, mailAftnFlight, MailType.Update, AFTNMsgType.SLC, comment, gdps[0].Comment == null ? "" : gdps[0].Comment);
                }

                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);
                return false;
            }
        }

        private Flight newCtotFlight(Flight cfl, string name, TimeSpan diff, int index)
        {
            Flight fl = new Flight();
            fl.Id = (cfl.Id * 10) + index;
            fl.Callsign = cfl.Callsign + name;
            fl.AirportDeparture = cfl.AirportDeparture;
            fl.AirportArrival = cfl.AirportArrival;
            fl.EOBT = cfl.EOBT.HasValue ? cfl.EOBT.Value.Add(diff) : null;
            fl.EIBT = cfl.EIBT.HasValue ? cfl.EIBT.Value.Add(diff) : null;
            fl.ETOT = cfl.ETOT.HasValue ? cfl.ETOT.Value.Add(diff) : null;
            fl.ELDT = cfl.ELDT.HasValue ? cfl.ELDT.Value.Add(diff) : null;
            fl.ELDTByDep = null;
            fl.ELDTByTMCS = null;
            fl.EIBTByDep = null;
            fl.EIBTByTMCS = null;
            fl.CTOT = null;
            fl.CLDT = null;
            fl.CIBT = null;
            fl.CIBT = null;
            fl.ALDT = null;
            fl.ATOT = null;
            fl.IsManaged = false;
            fl.IsExempt = false;

            return fl;
        }


        private async Task<CTOTOptimizeOutput> GetCtotFlightsAsync(CTOTOptimizeInput input)
        {
            /* CTOTFlight[] ctotFlights = null;
             HttpResponseMessage response = await client.PostAsJsonAsync("api/CtotOptimize", input);
             if (response.IsSuccessStatusCode)
                 ctotFlights = await response.Content.ReadAsAsync<CTOTFlight[]>();
             return ctotFlights;*/
            HttpClient client = new HttpClient { BaseAddress = new Uri(ATFMOptimizerIP) };

            try
            {
                for (int i = 0; i < input.Measures.Length; i++)
                {
                    if (input.Measures[i].MeasureId == -1) input.Measures[i].MeasureId = 0;
                }
                CTOTOptimizeOutput output = null;
                HttpResponseMessage response = await client.PostAsJsonAsync("optimizer", input);
                //Debug.WriteLine("response ="+ response.ToString());
                if (response.IsSuccessStatusCode)
                {
                    output = await response.Content.ReadAsAsync<CTOTOptimizeOutput>();
                    //return output;
                }
                return output;
            }
            catch (Exception e)
            {
                //Debug.WriteLine("error =" +e.InnerException.ToString());
                //Debug.WriteLine("error Msg =" + e.Message.ToString());
                Console.WriteLine(e.Message);
                return null;
            }
        }


        /* public async Task UpdateGdpAfterFlightExempt(GDPFlight gdpf)
         {
             var gdps = await _context.GDP.Include(g => g.GDPFlights.Where(f => f.Id == gdpf.Id)).ToListAsync();

             foreach (var gdp in gdps)
             {
                 gdp.GDPFlights.Remove(gdpf);
                 _context.Update(gdp);
             }

             await _context.SaveChangesAsync();
         } no remove, only cancelled*/


        /**Update CTOT to Idep ***/
        public async Task updateCTOTtoIdepAsync(Flight flight, string crudFlag, string IP)
        {
            CtotFlightModel cm = new CtotFlightModel();
            cm.FlightId = flight.Id;
            cm.Callsign = flight.Callsign;
            cm.Departure = flight.AirportDeparture;
            cm.Arrival = flight.AirportArrival;
            cm.EOBT = flight.EOBT;
            cm.CTOT = flight.CTOT;
            cm.CLDT = flight.CLDT;
            cm.ETOT = flight.ETOT;
            cm.ELDT = flight.ELDT;
            cm.CrudFlag = crudFlag;

            Ctot ctot = new Ctot();
            ctot.ctot = cm;

            HttpClient client = new HttpClient { BaseAddress = new Uri(IP) };
            try
            {
                HttpResponseMessage response = await client.PostAsJsonAsync("api/flightProcessingRegional/updateCTOTtoIdepGW", cm); //ต้องเขียนใหม่ในกรณี Regional
                if (response.IsSuccessStatusCode)
                {
                    // return response.Content.ReadAsStringAsync().Result;
                }
                else
                {
                    var log = "CTOT - Error Call updateCTOTtoIdepGW : " + response.Content.ReadAsStringAsync().Result;
                    ActionLog(log);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);

            }
        }

        public async Task cancelCTOTtoIdepAsync(Flight flight, string crudFlag, string IP)
        {
            CtotFlightModel cm = new CtotFlightModel();
            cm.FlightId = flight.Id;
            cm.Callsign = flight.Callsign;
            cm.Departure = flight.AirportDeparture;
            cm.Arrival = flight.AirportArrival;
            cm.EOBT = flight.EOBT;
            cm.CTOT = flight.CTOT;
            cm.CLDT = flight.CLDT;
            cm.ETOT = flight.ETOT;
            cm.ELDT = flight.ELDT;

            cm.CrudFlag = crudFlag;

            Ctot ctot = new Ctot();
            ctot.ctot = cm;

            HttpClient client = new HttpClient { BaseAddress = new Uri(IP) };


            // HttpClient client = new HttpClient { BaseAddress = new Uri(IdepBkkIP) };
            try
            {
                HttpResponseMessage response = await client.PostAsJsonAsync("api/flightProcessingRegional/cancelCTOTtoIdepGW", cm);
                if (response.IsSuccessStatusCode)
                {
                    //return ;
                }
                else
                {
                    var log = "CTOT - Error Call cancelCTOTtoIdepGW : " + response.Content.ReadAsStringAsync().Result;
                    ActionLog(log);
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e.Message);

            }
        }

        private void ActionLog(string source)
        {
            ActLog.ActionLog log = new ActLog.ActionLog();
            log.UserName = User.FindFirstValue(ClaimTypes.NameIdentifier);
            log.UserMachineIP = HttpContext.Connection.RemoteIpAddress.ToString();
            log.ActionMessage = "Test on log at " + DateTime.Today.ToString("dd-MM-yyyy HH:mm:ss");
            log.Source = source;
            Logger.LogMessage(log);
        }

        public class CtotInputModel
        {
            public int Gdpid { get; set; }
            public Flight Flight { get; set; }
            public DateTime? NewEobt { get; set; }
            public string Comment { get; set; }
        }
        public class CtotFlightModel
        {
            public int FlightId { get; set; }
            public string Callsign { get; set; }
            public string Departure { get; set; }
            public string Arrival { get; set; }
            public DateTime? EOBT { get; set; }
            public DateTime? ETOT { get; set; }
            public DateTime? ELDT { get; set; }
            public DateTime? EIBT { get; set; }
            public DateTime? CTOT { get; set; }
            public DateTime? CTO { get; set; }
            public DateTime? CLDT { get; set; }
            public string CrudFlag { get; set; }
        }
        public class Ctot { public CtotFlightModel ctot { get; set; } }



    }

}
