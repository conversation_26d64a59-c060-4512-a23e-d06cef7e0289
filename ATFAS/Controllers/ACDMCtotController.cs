﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using ATFAS.Areas.Identity.Data;
using ATFAS.Data;
using ATFAS.Models;
using ATFAS.Services;
using ATFAS.ViewModels;
using DataLayer.EfCode;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;

namespace ATFAS.Controllers
{

    public class ACDMCtotController : Controller
    {
        private readonly ATFASContext _context;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMailService _mailService;
        private readonly IViewRenderService _viewrenderService;
        private UserManager<AppUser> _userManager;
        private readonly ExtraAuthorizeDbContext _extraContext;
        public ACDMCtotController(ATFASContext context, IHttpContextAccessor httpContextAccessor, UserManager<AppUser> userManager, ExtraAuthorizeDbContext extraContext, IMailService mailService, IViewRenderService viewrenderService)
        {
            _context = context;
            _httpContextAccessor = httpContextAccessor;
            _userManager = userManager;
            _extraContext = extraContext;
            _mailService = mailService;
            _viewrenderService = viewrenderService;
        }
        [HttpGet]
        public IActionResult Index(string callsign, string dep, string arr, string eobt, string tobt)
        {
            if (!string.IsNullOrEmpty(callsign) && !string.IsNullOrEmpty(dep) && !string.IsNullOrEmpty(arr) && !string.IsNullOrEmpty(eobt) && !string.IsNullOrEmpty(tobt))
            {
                CtotreqModel request = new CtotreqModel()
                {
                    AircraftId = callsign.ToUpper(),
                    Departure = dep.ToUpper(),
                    Arrival = arr.ToUpper(),
                    EOBT = DateTime.Parse(eobt).ToUniversalTime(),
                    TOBT = DateTime.Parse(tobt).ToUniversalTime()
                };
               
                var res = RequestCtotTrialApi(request).Result;

                return View(res);
            }
            else
                return NotFound();

        }
        /* public IActionResult Index()
         {
             return NotFound();
         }*/
        /* [HttpPost]
         public IActionResult Index(CtotreqModel req)
         {

             var res = RequestCtotTrialApi(req).Result;
             //var res = RequestCtotTrialAirportApi(req).Result;
             return View(res);
         }*/
        [AllowAnonymous]
        public IActionResult Test()
        {
            CtotreqModel req = new CtotreqModel();
            //req.CtotTrials = RequestCtotTrial(req).Result;
            return View(req);
        }
        /* [HttpPost]
         [Route("ACDMCtot/Request")]
         public async Task<IActionResult> RequestCtotAsync(CtotreqModel req)
         {
             var result = new CtotresModel();
             var flight = _context.Flight.Where(f => f.Callsign.Equals(req.AircraftId) && f.AirportDeparture.Equals(req.Departure) && f.AirportArrival.Equals(req.Arrival) && f.EOBT.Equals(req.EOBT)).FirstOrDefault();
             if (flight != null)
             {
                 var gdpflights = await _context.GDPFlight.Include(g => g.GDPs).Where(g => g.FlightId.Equals(flight.Id)).FirstOrDefaultAsync();

                 GDPManageController manage = new GDPManageController(_context);
                 GDPManageController.CtotInputModel ctot = new GDPManageController.CtotInputModel();
                 ctot.Flight = flight;
                 ctot.NewEobt = req.TOBT;
                 ctot.Gdpid = gdpflights.GDPs.FirstOrDefault().Id;

                 result.Flight = flight;
                 result.NewEOBT = req.TOBT;
                 result.CtotTrial = await manage.RequestCtot(ctot);
             }
             return RedirectToAction("Index", result);
         }*/
        [HttpPost]
        public async Task<CtotresModel> RequestCtotTrialApi(CtotreqModel req)
        {
            var result = new CtotresModel();
            try
            {  
                var flight = await _context.Flight.Where(f => f.Callsign.Equals(req.AircraftId) && f.AirportDeparture.Equals(req.Departure) && f.AirportArrival.Equals(req.Arrival) && f.EOBT.Equals(req.EOBT) && !f.IsCancelled).FirstOrDefaultAsync();
                if (flight != null)
                {
                    result.Flight = flight;
                    result.NewEOBT = req.TOBT;
                    var gdpflights = await _context.GDPFlight.Include(g => g.GDPs).Where(g => g.FlightId.Equals(flight.Id)).ToListAsync();
                    /*remove previous trials*/
                    var gdpTrials = gdpflights.Where(g => g.IsTrial).ToList();
                    foreach (var itm in gdpTrials)
                    {
                        itm.IsCancelled = true;
                    }
                    _context.UpdateRange(gdpTrials);
                    await _context.SaveChangesAsync();
                    //if (NewEobtValid(req.TOBT.Value, req.EOBT, flight.ETOT.Value))
                    //{
                        var gdpflight = gdpflights.FirstOrDefault();
                        if (gdpflight != null)
                        {
                            GDPManageController manage = new GDPManageController(_context, _httpContextAccessor, _userManager, _extraContext, _mailService, _viewrenderService);
                            GDPManageController.CtotInputModel ctot = new GDPManageController.CtotInputModel();
                            ctot.Flight = flight;
                            ctot.NewEobt = req.TOBT;
                            ctot.Gdpid = gdpflight.GDPs.FirstOrDefault().Id;
                            result.CtotTrial = await manage.RequestCtot(ctot);
                        }
                        else
                            result.Message = "Flight Not In Ground Delay Program";
                    //}
                    //else
                    //    result.Message = "Invalid New OBT Value.";
                }
                else
                    result.Message = "Flight Not Found";
            }
            catch (Exception e)
            {
                result.Message = e.ToString();
            }

            return result;
        }

        [HttpGet]
        [Route("ACDMCtot/UpdateFlightAsync/{id}")]
        public async Task<Flight> UpdateFlightAsync(int id)
        {
            return await _context.Flight.Where(f => f.Id.Equals(id)).FirstOrDefaultAsync();
        }

        /* private bool NewEobtValid(DateTime newEobt, DateTime eobt, DateTime etot) {
            var config = _context.GeneralConfiguration.FirstOrDefault();
            var utcDate = DateTime.UtcNow;
            //var freezeUtc = utcDate.Add(config.NewCtotBlockTime);
            var freezeUtc = utcDate; 
            var duration = etot - eobt;
            if (newEobt > freezeUtc.Subtract(duration))
                return true;
            else
                return false;
        } */

        public class CtotreqModel
        {
            public string AircraftId { get; set; }
            public string Departure { get; set; }
            public string Arrival { get; set; }
            public DateTime EOBT { get; set; }
            public DateTime? TOBT { get; set; }


        }
        public class CtotresModel
        {
            public Flight Flight { get; set; }
            public DateTime? NewEOBT { get; set; }
            public CTOTTrialModel CtotTrial { get; set; }
            public string Message { get; set; }

        }
    }
}
