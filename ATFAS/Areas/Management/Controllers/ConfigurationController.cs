﻿using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using ATFAS.Models;
using ATFAS.Data;
using ATFAS.ActLog;
using System.Security.Claims;
using Microsoft.AspNetCore.Authorization;
using PermissionParts;
using FeatureAuthorize.PolicyCode;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.EntityFrameworkCore;
using System.IO;
using System.Threading;
using System.Text.Json;
using static Microsoft.ML.Data.DataDebuggerPreview;


namespace ATFAS.Areas.Management.Controllers
{
    [Area("Management")]
    //[Route("Management/Announcement")]
    public class ConfigurationController : Controller
    {
        private readonly ATFASContext _context;
        //private List<Flight> flist;
        private readonly IWebHostEnvironment _env;
        private const string _resourceDir = "files/resources";
        public ConfigurationController(ATFASContext ctx, IWebHostEnvironment env)
        {
            _context = ctx;
            _env = env;
        }
        public IActionResult Index()
        {
            ActionLog("Configuration - Visit Configuration Page");
            return View("Index");
        }
        public IActionResult Airport()
        {
            ActionLog("Configuration - Visit Airport Configuration Page");
            return View();
        }

        [HasPermission(Permissions.CtotConfig)]
        public IActionResult Ctot()
        {
            ActionLog("Configuration - Visit Ctot Configuration Page");
            return View();
        }
        [HasPermission(Permissions.ManageAtfmu)]
        public IActionResult Atfmu()
        {
            ActionLog("Configuration - Visit ATFMU Configuration Page");
            return View();
        }
        [HasPermission(Permissions.ManagePoc)]
        public IActionResult PointofContact()
        {
            ActionLog("Configuration - Visit Point Of Contact Configuration Page");
            return View();
        }
        [HasPermission(Permissions.ManageResources)]
        public IActionResult Resources()
        {
            ActionLog("Configuration - Visit Resources Configuration Page");
            return View();                              // looks for Areas/Management/Views/Configuration/Resources.cshtml
        }
        [HttpGet]
        [Route("Configuration/GetAtfmus")]
        public List<Atfmu> GetAtfmus()
        {

            return _context.Atfmu.ToList();
        }
        [HttpGet]
        [Route("Configuration/GetPocs")]
        public List<Pointofcontact> GetPocs()
        {
            return _context.Pointofcontact.ToList();
        }
        [HttpGet]
        [Route("Configuration/GetConfigList")]
        public GeneralConfiguration GetConfigList()
        {
            ActionLog("Home - Airport Configuration ");
            return _context.GeneralConfiguration.FirstOrDefault();
        }
        [HttpGet]
        [Route("Configuration/GetAllAirportList")]
        public List<Airport> GetAllAirportList()
        {
            var airports = _context.Airport.Where(a => a.ROWDELETE == false).OrderBy(a => a.FIR).ToList();
            foreach (var itm in airports)
            {
                if (itm.FIX > 0)
                {
                    var geo = _context.Fix.Where(f => f.RFDPID == itm.FIX).FirstOrDefault();
                   
                    itm.LAT_DEGREE = (geo != null) ? geo.LAT_DEGREE : null;
                    itm.LON_DEGREE = (geo != null) ? geo.LON_DEGREE : null;
                }
            }
            return airports;
        }
        [HttpPost]
        [Route("Configuration/SaveConfig")]
        public async Task<bool> SaveConfig(GeneralConfiguration data)
        {

            var config = new GeneralConfiguration
            {
                Id = 1,
                DefaultTaxiTime = data.DefaultTaxiTime,
                DefaultSWAirportList = data.DefaultSWAirportList,
                DefaultCtotOptions = data.DefaultCtotOptions,
                DefaultCtotTime = data.DefaultCtotTime,
                CtotAirlineTime = data.CtotAirlineTime,
                NewCtotBufferTime = data.NewCtotBufferTime

            };
            try
            {
                _context.Update(config);
                await _context.SaveChangesAsync();
                ActionLog("Home - Save Configuration ");
            }
            catch (Exception e)
            {
                throw e;
            }
            return true;
        }
        [HttpPost]
        [Route("Configuration/SaveAtfmu")]
        public async Task<JsonResult> SaveAtfmu(Atfmu data)
        {

            try
            {
                if (IsAtfmExist(data.Id)) // Update
                {
                    if (_context.Atfmu.Any(a => !(a.Id.Equals(data.Id)) && a.Location.Equals(data.Location)))
                        return Json(new { message = "location already exist!" });
                    else
                        _context.Update(data);
                }
                else
                {
                    if (_context.Atfmu.Any(a => a.Location.Equals(data.Location)))
                        return Json(new { message = "location already exist!" });
                    else
                        _context.Add(data);
                }
                await _context.SaveChangesAsync();
                return Json(new { message = "success" });

            }
            catch (Exception e)
            {

                return Json(new { message = e.ToString() });
            }
        }
        [HttpGet]
        [Route("Configuration/DeleteAtfmu/{id}")]
        public async Task<Boolean> DeleteAtfmu(int id)
        {
            try
            {
                if (IsAtfmExist(id))
                {
                    var atfmu = _context.Atfmu.Where(a => a.Id.Equals(id)).FirstOrDefault();
                    _context.Atfmu.Remove(atfmu);
                    await _context.SaveChangesAsync();
                    return true;
                }
                else
                    return false;
            }
            catch (Exception e)
            {

                return false;
            }
        }
        [HttpPost]
        [Route("Configuration/SavePoc")]
        public async Task<JsonResult> SavePoc(Pointofcontact data)
        {
            try
            {
                if (IsPocExist(data.Id)) // Update
                    _context.Update(data);
                else
                    _context.Add(data);
                await _context.SaveChangesAsync();
                return Json(new { message = "success" });
            }
            catch (Exception e)
            {
                return Json(new { message = e.ToString() });
            }
        }
        [HttpGet]
        [Route("Configuration/DeletePoc/{id}")]
        public async Task<Boolean> DeletePoc(int id)
        {
            try
            {
                if (IsPocExist(id))
                {
                    var poc = _context.Pointofcontact.Where(a => a.Id.Equals(id)).FirstOrDefault();
                    _context.Pointofcontact.Remove(poc);
                    await _context.SaveChangesAsync();
                    return true;
                }
                else
                    return false;
            }
            catch (Exception e)
            {

                return false;
            }
        }
        private bool IsAtfmExist(int id)
        {
            return _context.Atfmu.Any(a => a.Id.Equals(id));
        }
        private bool IsPocExist(int id)
        {
            return _context.Pointofcontact.Any(a => a.Id.Equals(id));
        }
        private void ActionLog(string source)
        {
            ActLog.ActionLog log = new ActLog.ActionLog();
            log.UserName = User.FindFirstValue(ClaimTypes.NameIdentifier);
            log.UserMachineIP = HttpContext.Connection.RemoteIpAddress.ToString();
            log.ActionMessage = "Test on log at " + DateTime.Today.ToString("dd-MM-yyyy HH:mm:ss");
            log.Source = source;
            Logger.LogMessage(log);
        }

        // Endpoint for Resources Configuration
        // ─────────────────────────────────────────────────────────────
        //  RESOURCES  — single-index version  (resources.index.json)
        // ─────────────────────────────────────────────────────────────
        private record ResourceMeta
        {
            public int Id { get; init; }
            public int Serial { get; set; }
            public string Type { get; set; } = "file";   // "file" | "link"
            public string? LinkUrl { get; set; } // for Type=="link"
            public string AddedBy { get; init; } = "";
            public DateTime AddedAt { get; init; }
            public DateTime UpdatedAt { get; set; }
            public string DisplayName { get; set; } = "";
            public string FileRelPath { get; set; } = ""; // for Type=="file"
        }

        private string IndexPath =>
            Path.Combine(_env.WebRootPath, _resourceDir, "resources.index.json");

        /* ---------- central load / save ---------------------------------- */
        private List<ResourceMeta> LoadIndex()
        {
            if (!System.IO.File.Exists(IndexPath)) return new();

            var json = System.IO.File.ReadAllText(IndexPath);
            return JsonSerializer.Deserialize<List<ResourceMeta>>(json)
                   ?? new List<ResourceMeta>();
        }
        /* ──────────────────────────────────────────────────────────────
        Helpers – never write the index until the folder exists
        ──────────────────────────────────────────────────────────── */
        private void SaveIndex(List<ResourceMeta> list)
        {
            var dir = Path.GetDirectoryName(IndexPath)!;   // …/wwwroot/files/resources
            if (!Directory.Exists(dir))
                Directory.CreateDirectory(dir);

            var json = JsonSerializer.Serialize(
                list,
                new JsonSerializerOptions { WriteIndented = true });

            System.IO.File.WriteAllText(IndexPath, json);
        }


        /* ---------- helpers ---------------------------------------------- */
        private int NextSerial(List<ResourceMeta> idx)
            => idx.Count == 0 ? 1 : idx.Max(m => m.Serial) + 1;

        private void RenumberSerials(List<ResourceMeta> idx)
        {
            var ordered = idx.OrderBy(m => m.Serial == 0 ? int.MaxValue : m.Serial)
                             .ThenBy(m => m.AddedAt)
                             .ToList();
            for (int i = 0; i < ordered.Count; i++)
                ordered[i].Serial = i + 1;

            SaveIndex(ordered);
        }

        /* ---------- GET --------------------------------------------------- */
        [HttpGet, Route("Management/Configuration/GetResources")]
        public IActionResult GetResources()
        {
            var user = User.Identity?.Name ?? "anonymous";
            var idx = LoadIndex().Where(m => m.AddedBy == user)
                                  .OrderBy(m => m.Serial);

            var rows = idx.Select(m => new
            {
                id = m.Id,
                serial = m.Serial,
                type = m.Type,
                displayName = string.IsNullOrWhiteSpace(m.DisplayName) ? "(no title)" : m.DisplayName,
                fileName = string.IsNullOrEmpty(m.FileRelPath) ? "" : Path.GetFileName(m.FileRelPath),
                linkUrl = m.LinkUrl,
                fileRelPath = m.FileRelPath,
                addedBy = m.AddedBy,
                updatedAt = m.UpdatedAt
            });

            return Ok(rows);
        }

        /* ---------- POST Create ------------------------------------------- */
        [HttpPost, Route("Management/Configuration/CreateResource")]
        public async Task<IActionResult> CreateResource(IFormFile? file,
                                                string displayName,
                                                string type,           
                                                string? linkUrl)  
        {
            // ── basic checks ──────────────────────────────────────────────
            if (type == "file" && (file == null || file.Length == 0))
                return BadRequest("Please choose a file.");

            if (type == "link" && string.IsNullOrWhiteSpace(linkUrl))
                return BadRequest("Please enter a URL.");

            var idx = LoadIndex();
            var serial = NextSerial(idx);

            // dummy DB row (keeps PK logic intact)
            var db = new Fileupload { IsActive = true, TimeStamp = DateTime.UtcNow };
            _context.Add(db);
            await _context.SaveChangesAsync();

            /* ---------- FILE branch ------------------------------------- */
            string relPath = "";
            if (type == "file")
            {
                var root = Path.Combine(_env.WebRootPath, _resourceDir);
                if (!Directory.Exists(root)) Directory.CreateDirectory(root);

                relPath = Path.Combine(_resourceDir, file!.FileName).Replace("\\", "/");
                var full = Path.Combine(_env.WebRootPath, relPath);
                if (System.IO.File.Exists(full))
                    return Conflict("A file with the same name already exists.");

                await using var fs = new FileStream(full, FileMode.Create);
                await file.CopyToAsync(fs);
            }

            /* ---------- write index ------------------------------------- */
            idx.Add(new ResourceMeta
            {
                Id = db.Id,
                Serial = serial,
                Type = type,              
                LinkUrl = linkUrl,        
                AddedBy = User.Identity?.Name ?? "anonymous",
                AddedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow,
                DisplayName = displayName ?? "(no title)",
                FileRelPath = relPath
            });
            SaveIndex(idx);

            return Ok();
        }

        /* ---------- POST Update display name ------------------------------ */
        [HttpPost, Route("Management/Configuration/UpdateResourceDisplayName")]
        public IActionResult UpdateResourceDisplayName(int pk, string value)
        {
            var idx = LoadIndex();
            var m = idx.FirstOrDefault(x => x.Id == pk);
            if (m == null) return NotFound();

            m.DisplayName = value;
            m.UpdatedAt = DateTime.UtcNow;                   
            SaveIndex(idx);
            return Ok();
        }

        /* ---------- POST upload file -------------------------------------- */
        [HttpPost, Route("Management/Configuration/UpdateResource")]
        [RequestSizeLimit(100_000_000)]
        public async Task<IActionResult> UpdateResource(
        int id,
        string displayName,
        string type,              // "file" | "link"   <-- NEW
        string? linkUrl,           // (only in link-mode)
        IFormFile? file)              // (only in file-mode)
        {
            var idx = LoadIndex();
            var m = idx.FirstOrDefault(x => x.Id == id);
            if (m == null) return NotFound();

            /* ───── display-name ───────────────────────────────────── */
            if (!string.IsNullOrWhiteSpace(displayName))
                m.DisplayName = displayName.Trim();

            /* ───── link-mode ──────────────────────────────────────── */
            if (type == "link")
            {
                if (string.IsNullOrWhiteSpace(linkUrl))
                    return BadRequest("URL is required.");

                /* delete old file if this resource USED TO be a file */
                if (!string.IsNullOrEmpty(m.FileRelPath))
                {
                    var oldPath = Path.Combine(_env.WebRootPath, m.FileRelPath);
                    if (System.IO.File.Exists(oldPath))
                        System.IO.File.Delete(oldPath);
                }

                m.Type = "link";
                m.LinkUrl = linkUrl.Trim();
                m.FileRelPath = "";          // nothing on disk
            }
            /* ───── file-mode ──────────────────────────────────────── */
            else if (type == "file")
            {
                m.Type = "file";
                m.LinkUrl = null;            // clear any previous URL

                if (file != null && file.Length > 0)
                {
                    var root = Path.Combine(_env.WebRootPath, _resourceDir);
                    var relPath = Path.Combine(_resourceDir, file.FileName).Replace("\\", "/");
                    var full = Path.Combine(_env.WebRootPath, relPath);

                    if (!Directory.Exists(root)) Directory.CreateDirectory(root);
                    if (System.IO.File.Exists(full))
                        return Conflict("A file with the same name already exists.");

                    /* delete old file (if any) */
                    if (!string.IsNullOrEmpty(m.FileRelPath))
                    {
                        var oldPath = Path.Combine(_env.WebRootPath, m.FileRelPath);
                        if (System.IO.File.Exists(oldPath))
                            System.IO.File.Delete(oldPath);
                    }

                    await using var fs = new FileStream(full, FileMode.Create);
                    await file.CopyToAsync(fs);

                    m.FileRelPath = relPath;
                }
                /* if no new file was posted we just keep the existing one */
            }
            else
                return BadRequest("Unknown resource type.");

            /* ───── save & finish ──────────────────────────────────── */
            m.UpdatedAt = DateTime.UtcNow;
            SaveIndex(idx);
            return Ok();
        }

        /* ---------- DELETE ------------------------------------------------ */
        [HttpDelete, Route("Management/Configuration/DeleteResource/{id}")]
        public async Task<IActionResult> DeleteResource(int id)
        {
            var idx = LoadIndex();
            var meta = idx.FirstOrDefault(x => x.Id == id);
            var user = User.Identity?.Name ?? "";

            if (meta == null) return NotFound();
            if (meta.AddedBy != user && !User.IsInRole("Admin")) return Forbid();

            if (!string.IsNullOrEmpty(meta.FileRelPath))
                System.IO.File.Delete(Path.Combine(_env.WebRootPath, meta.FileRelPath));

            idx.Remove(meta);
            RenumberSerials(idx);

            var f = await _context.Fileupload.FindAsync(id);
            if (f != null) { _context.Fileupload.Remove(f); await _context.SaveChangesAsync(); }

            return Ok();
        }

    }
}
