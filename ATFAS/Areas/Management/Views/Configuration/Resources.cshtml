﻿@model IEnumerable<ATFAS.Models.Fileupload>
@{
    Layout = "~/Views/Shared/_SidebarLayout.cshtml";
}

@* <div style="margin-bottom:2em;"> *@
@*     <span style="font-size: 1.2rem; font-weight: 500;">Resources Configuration</span> *@
@*     <a class="btn btn-dark btn-add-resource" style="float:right;" href="javascript:void(0)"> *@
@*         Add Resource *@
@*     </a> *@
@* </div> *@

<div class="row">
    <table id="resource-table"
           class="table"
           data-id-field="id"
           data-unique-id="id"
           data-search="true">    
        <thead class="thead-dark">
            <tr>
                <th data-field="serial" data-align="center" style="width:70px">
                    <button class="btn btn-sm btn-dark btn-add-resource float-right">
                        <i class="fas fa-plus"></i>
                    </button>
                </th>

                <th data-field="displayName" data-sortable="true">
                    Display&nbsp;Name
                </th>

                <th data-field="addedBy" data-sortable="true" style="width:140px">
                    Added&nbsp;By
                </th>

                
                <th data-field="updatedAt" data-sortable="true"
                    data-formatter="dateFormatter" style="width:160px">
                   Latest&nbsp;Updated(UTC)
                </th>

                <th data-field="fileName"
                    data-formatter="attachmentFormatter"
                    data-align="center" style="width:200px">
                    Reference
                </th>

                <th data-field="edit"
                    data-formatter="editFormatter"
                    data-events="editEvents"
                    data-align="center"
                    data-width="80">
                </th>

                <th data-field="delete"
                    data-formatter="delFormatter"
                    data-events="delEvents"
                    data-align="center"
                    data-width="80">
                </th>
            </tr>
        </thead>
    </table>
    <!-- ADD / EDIT modal -->
    <div class="modal fade" id="resourceModal" tabindex="-1">
        <div class="modal-dialog">
            <form id="resourceForm" class="modal-content" enctype="multipart/form-data">
                <div class="modal-header">
                    <h5 class="modal-title"></h5>
                    <button type="button" class="close" data-dismiss="modal">
                        <span>&times;</span>
                    </button>
                </div>

                <div class="modal-body">
                    <input type="hidden" name="id">

                    <!-- Display-name -->
                    <div class="form-group">
                        <label class="font-weight-bold">Display&nbsp;Name<span class="text-danger">*</span></label>
                        <input type="text" class="form-control" name="displayName" required>
                    </div>

                    <!-- Resource type -->
                    <div class="form-group ">
                        <label class="font-weight-bold">Type<span class="text-danger">*</span></label>
                        <select id="resourceType" name="type" class="form-control" required>
                            <option value="file" selected>File</option>
                            <option value="link">URL Link</option>
                        </select>
                    </div>

                    <!-- URL input (link-mode only) -->
                    <div class="form-group url-group d-none">
                        <label class="font-weight-bold">URL<span class="text-danger">*</span></label>
                        <input type="url" class="form-control" name="linkUrl">
                    </div>

                    <!-- File picker / drop-zone -->
                    <div class="form-group file-group">
                        <label class="font-weight-bold">Attachment<span class="text-danger">*</span></label>

                        <div class="drop-zone" id="dz">
                            <span class="dz-text">Drag &amp; drop file here <small class="text-muted">or click to browse</small></span>
                            <input type="file" name="file" required>
                        </div>

                        <small class="form-text text-muted current-file d-none">
                            current file:&nbsp;<span></span>
                        </small>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-primary">Save</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Stylesheets {
    <link rel="stylesheet" href="~/lib/jquery-ui/jquery-ui.css" />
    <link rel="stylesheet" href="~/lib/bootstrap-table/dist/bootstrap-table.css" />
    <link rel="stylesheet" href="~/lib/x-editable/css/bootstrap-editable.css" />
    <style>
        /* make grab-bars obvious */
        .drag-handle {
            cursor: move;
        }

            .drag-handle i {
                opacity: .55;
            }

        .table-editable {
            box-shadow: none;
        }
    </style>
    <style>
        /* drag-&-drop file box */
        .drop-zone {
            position: relative;
            border: 2px dashed #bbb;
            border-radius: .25rem;
            padding: 2.5rem 1rem;
            text-align: center;
            cursor: pointer;
            transition: background .2s;
        }

            .drop-zone.hover {
                background: #e2ffe2;
            }

            .drop-zone input[type=file] {
                position: absolute;
                inset: 0;
                opacity: 0;
                cursor: pointer;
            }
    </style>
}

@section Scripts {
    <script src="~/lib/jquery-ui/jquery-ui.js"></script>
    <script src="~/lib/bootstrap-table/dist/bootstrap-table.js"></script>
    <script src="~/lib/x-editable/js/bootstrap-editable.min.js"></script>
    <script src="~/lib/bootstrap-table/extensions/editable/bootstrap-table-editable.js"></script>
    <script src="~/lib/moment.js"></script>
    <script src="~/js/Management/Configuration/resources.js"></script>
}
