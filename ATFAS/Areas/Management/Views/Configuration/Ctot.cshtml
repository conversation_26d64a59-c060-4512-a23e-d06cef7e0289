﻿@{
    Layout = "~/Views/Shared/_SidebarLayout.cshtml";
}
@section Scripts {
    <link rel="stylesheet" href="~/lib/adminlte/plugins/bootstrap4-duallistbox/bootstrap-duallistbox.min.css" />
    <script src="~/lib/adminlte/plugins/bootstrap4-duallistbox/jquery.bootstrap-duallistbox.min.js"></script>
    <script src="~/js/Management/Configuration/ctot.js"></script>

}
<div class="card card-default" style="max-width: 800px;box-shadow:unset;">
    <div class="card-header">
        <h5 style="margin-bottom:1em;">CTOT Configuration</h5>
        </div>
    <!-- /.card-header -->
    <div class="card-body">
        <div class="row">
            <div class="col-12">
                <div class="form-group">
                    <div class="form-group col-auto">
                        <label class="control-label" for="CtotTime">New CTOT Reservation Time (Minutes)</label>
                        <small class="text-gray">Time limited for user to selected new CTOT</small>
                        <input id="CtotTime" class="form-control" type="number" min="1" max="10" />
                        <span class="text-danger time-error"></span>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-group col-auto">
                        <label class="control-label" for="CtotAirline">Airline Change Request prohibition Time (Minutes)</label>
                        <small class="text-gray">CTOT changes are not allowed if current CTOT earlier than the current time plus the prohibition time. (input 0 if you don't want a prohibition time.)</small>
                        <input id="CtotAirline" class="form-control" type="number" min="1" max="300" />
                        <span class="text-danger airline-error"></span>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-group col-auto">
                        <label class="control-label" for="CtotOption">New CTOT Options</label>
                        <small class="text-gray">Number of available CTOT alternatives when a new OBT is requested.</small>
                        <input id="CtotOption" class="form-control " type="number" min="1" max="5" />
                        <span class="text-danger option-error"></span>
                    </div>
                </div>
                <div class="form-group">
                    <div class="form-group col-auto">
                        <label class="control-label" for="CtotOption">New CTOT Buffer Time (Minutes)</label>
                        <small class="text-gray">The new CTOT derived from the requested OBT must not be earlier than the current time plus the configured CTOT buffer time.</small>
                        <input id="CtotBuffer" class="form-control " type="number" min="1" max="300" />
                        <span class="text-danger buffer-error"></span>
                    </div>
                </div>

            </div>
        </div>
        <hr />
        <button type="button" class="btn btn-default" onclick="location.reload();">Reset</button>
   
        <button type="button" class="btn btn-primary" style="float:right;" id="SaveConfig">Save Configuration</button>@**@
        <!-- /.row -->
    </div>
</div>

