﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Security.Claims;
using System.Text;
using System.Text.Encodings.Web;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using ATFAS.Areas.Identity.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.WebUtilities;
using Microsoft.Extensions.Logging;
using System.IdentityModel.Tokens.Jwt;
using DataLayer.EfCode;
using DataLayer.ExtraAuthClasses;
using ATFAS.Data;
using ATFAS.Models;
using ATFAS.Controllers;

namespace ATFAS.Areas.Identity.Pages.Account
{
    [AllowAnonymous]
    public class ExternalLoginModel : PageModel
    {
        private readonly SignInManager<AppUser> _signInManager;
        private readonly UserManager<AppUser> _userManager;
        private readonly IEmailSender _emailSender;
        private readonly ILogger<ExternalLoginModel> _logger;
        private readonly ExtraAuthorizeDbContext _context;
        private readonly ATFASContext _atfasContext;

        public ExternalLoginModel(
            SignInManager<AppUser> signInManager,
            UserManager<AppUser> userManager,
            ILogger<ExternalLoginModel> logger,
            IEmailSender emailSender,
            ExtraAuthorizeDbContext context,
            ATFASContext atfastContext)
        {
            _signInManager = signInManager;
            _userManager = userManager;
            _logger = logger;
            _emailSender = emailSender;
            _context = context;
            _atfasContext = atfastContext;
        }

        [BindProperty]
        public InputModel Input { get; set; }

        public string ProviderDisplayName { get; set; }

        public string ReturnUrl { get; set; }

        [TempData]
        public string ErrorMessage { get; set; }

        public class InputModel
        {
            [Required, DataType(DataType.Text), Display(Name = "First Name")]
            public string FirstName { get; set; }

            [Required, DataType(DataType.Text), Display(Name = "Last Name")]
            public string LastName { get; set; }

            [Required]
            [StringLength(40, ErrorMessage = "The {0} must be at least {2} and at max {1} characters long.", MinimumLength = 2)]
            [Display(Name = "Username")]
            public string Username { get; set; }

            [Required]
            [EmailAddress]
            [Display(Name = "Email")]
            public string Email { get; set; }

            [Phone]
            [Display(Name = "Phone Number")]
            public string PhoneNumber { get; set; }
        }

        public IActionResult OnGet(string returnUrl = null)
        {
            returnUrl = returnUrl ?? Url.Content("/");
            if (!User.Identity.IsAuthenticated)
            {
                string provider = _signInManager.GetExternalAuthenticationSchemesAsync().Result.FirstOrDefault().Name;
                return OnPost(provider, returnUrl);
            }
            return Redirect(returnUrl);
            //return RedirectToPage("./Login");
        }

        public IActionResult OnPost(string provider, string returnUrl = null)
        {
            // Request a redirect to the external login provider.
            var redirectUrl = Url.Page("./ExternalLogin", pageHandler: "Callback", values: new { returnUrl });
            var properties = _signInManager.ConfigureExternalAuthenticationProperties(provider, redirectUrl);
            return new ChallengeResult(provider, properties);
        }

        public async Task<IActionResult> OnGetCallbackAsync(string returnUrl = null, string remoteError = null)
        {
            returnUrl = returnUrl ?? Url.Content("~/");
            if (remoteError != null)
            {
                ErrorMessage = $"Error from external provider: {remoteError}";
                return RedirectToPage("./Login", new { ReturnUrl = returnUrl });
            }
            var info = await _signInManager.GetExternalLoginInfoAsync();
            if (info == null)
            {
                ErrorMessage = "Error loading external login information.";
                return RedirectToPage("./Login", new { ReturnUrl = returnUrl });
            }

            // Sign in the user with this external login provider if the user already has a login.
            var result = await _signInManager.ExternalLoginSignInAsync(info.LoginProvider, info.ProviderKey, isPersistent: false, bypassTwoFactor: true);
            if (result.Succeeded)
            {
                _logger.LogInformation("{Name} logged in with {LoginProvider} provider.", info.Principal.Identity.Name, info.LoginProvider);
                return LocalRedirect(returnUrl);
            }
            if (result.IsNotAllowed)
            {
                return RedirectToPage("./RegisterConfirmation", new { username = "SSO-" + info.Principal.FindFirstValue("preferred_username") });
            }
            if (result.IsLockedOut)
            {
                return RedirectToPage("./Lockout");
            }
            else
            {
                // If the user does not have an account, then ask the user to create an account.
                ReturnUrl = returnUrl;
                ProviderDisplayName = info.ProviderDisplayName;
                if (info.Principal.HasClaim(c => c.Type == ClaimTypes.Email))
                {
                    Input = new InputModel
                    {
                        FirstName = info.Principal.FindFirstValue(ClaimTypes.GivenName),
                        LastName = info.Principal.FindFirstValue(ClaimTypes.Surname),
                        Username = "SSO-" + info.Principal.FindFirstValue("preferred_username"),
                        Email = info.Principal.FindFirstValue(ClaimTypes.Email),
                        PhoneNumber = info.Principal.FindFirstValue("telephone")
                    };
                }
                return Page();
            }
        }

        public async Task<IActionResult> OnPostConfirmationAsync(string returnUrl = null)
        {
            returnUrl = returnUrl ?? Url.Content("~/");
            // Get the information about the user from the external login provider
            var info = await _signInManager.GetExternalLoginInfoAsync();
            if (info == null)
            {
                ErrorMessage = "Error loading external login information during confirmation.";
                return RedirectToPage("./Login", new { ReturnUrl = returnUrl });
            }

            if (ModelState.IsValid)
            {
                var user = new AppUser
                {
                    UserName = Input.Username,
                    Email = Input.Email,
                    FirstName = Input.FirstName,
                    LastName = Input.LastName,
                    PhoneNumber = Input.PhoneNumber,
                    EmailConfirmed = true
                };

                var result = await _userManager.CreateAsync(user);
                if (result.Succeeded)
                {
                    result = await _userManager.AddLoginAsync(user, info);                  
                    if (result.Succeeded)
                    {
                        UpdateUserRole(user.Id, info);
                        UpdateUserProfile(user.Id, info);
                        AddUsersubscription(user);

                        _logger.LogInformation("User created an account using {Name} provider.", info.LoginProvider);

                        //var userId = await _userManager.GetUserIdAsync(user);
                        //var code = await _userManager.GenerateEmailConfirmationTokenAsync(user);
                        //code = WebEncoders.Base64UrlEncode(Encoding.UTF8.GetBytes(code));
                        //var callbackUrl = Url.Page(
                        //    "/Account/ConfirmEmail",
                        //    pageHandler: null,
                        //    values: new { area = "Identity", userId = userId, code = code },
                        //    protocol: Request.Scheme);

                        //await _emailSender.SendEmailAsync(Input.Email, "Confirm your email",
                        //    $"Please confirm your account by <a href='{HtmlEncoder.Default.Encode(callbackUrl)}'>clicking here</a>.");

                        // If account confirmation is required, we need to show the link if we don't have a real email sender
                        //if (_userManager.Options.SignIn.RequireConfirmedAccount)
                        //{
                        //    return RedirectToPage("./RegisterConfirmation", new { username = user.UserName });
                        //}

                        await _signInManager.SignInAsync(user, isPersistent: false, info.LoginProvider);

                        return LocalRedirect(returnUrl);
                    }
                }
                foreach (var error in result.Errors)
                {
                    ModelState.AddModelError(string.Empty, error.Description);
                }
            }

            ProviderDisplayName = info.ProviderDisplayName;
            ReturnUrl = returnUrl;
            return Page();
        }

        private void UpdateUserRole(string userId, ExternalLoginInfo info)
        {
            // Add roles to user
            List<string> roleNames = info.Principal.FindAll("clientRoles")
                .Select(x => x.Value).ToList();
            List<RoleToPermissions> roleNameFromDb = _context.RolesToPermissions
                .Where(x => roleNames.Contains(x.RoleName)).ToList();
            foreach (RoleToPermissions rtp in roleNameFromDb)
            {
                UserToRole userToRole = new UserToRole(userId, rtp);
                _context.Add<UserToRole>(userToRole);
            }
            _context.SaveChanges();
        }
        private void AddUsersubscription(AppUser user) {
            UserProfileController userprofile = new UserProfileController(_userManager, _atfasContext, _context);
            userprofile.CreateOrUpdateProfile("Subscribe ADP", "Subscribe to ADP E-mails", user.Id, false);
            userprofile.CreateOrUpdateProfile("Subscribe CTOT AFTN", "Subscribe to CTOT AFTN Messages", user.Id, false);
            userprofile.CreateOrUpdateProfile("Subscribe CTOT Email", "Subscribe to CTOT E-mails", user.Id, false);
        }

        private void UpdateUserProfile(string userId, ExternalLoginInfo info)
        {
            // Add User Profile
            string airlineCode = info.Principal.FindFirstValue("AirlineCode");
            string airportCode = info.Principal.FindFirstValue("AirportCode");
            string airlineAftn = info.Principal.FindFirstValue("AirlineAftn");

            if (airlineCode != null)
            {
                UserProfile airlineCodeProfile = new UserProfile
                {
                    UserId = userId,
                    ProfileName = "User Airline Codes",
                    ProfileValue = airlineCode,
                    IsActive = false
                };
                _atfasContext.Add<UserProfile>(airlineCodeProfile);
            }
            if (airportCode != null)
            {
                UserProfile airportCodeProfile = new UserProfile
                {
                    UserId = userId,
                    ProfileName = "User Airport Codes",
                    ProfileValue = airportCode,
                    IsActive = false
                };
                _atfasContext.Add<UserProfile>(airportCodeProfile);
            }
           // if (airlineAftn != null)
           // {
                UserProfile airlineAftnProfile = new UserProfile
                {
                    UserId = userId,
                    ProfileName = "User AFTN Addresses",
                    ProfileValue = (airlineAftn == null) ? "" : airlineAftn,
                    IsActive = false
                };
                _atfasContext.Add<UserProfile>(airlineAftnProfile);
           // }
            _atfasContext.SaveChanges();
        }

    }
}
