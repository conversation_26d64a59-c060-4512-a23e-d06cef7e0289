﻿@page
@model RegisterConfirmationModel
@{
    ViewData["Title"] = "Register Complete";
}

<h1>@ViewData["Title"]</h1>
@{
    if (@Model.DisplayConfirmAccountLink)
    {
<p>
    This app does not currently have a real email sender registered, see <a href="https://aka.ms/aspaccountconf">these docs</a> for how to configure a real email sender.
    Normally this would be emailed: <a id="confirm-link" href="@Model.EmailConfirmationUrl">Click here to confirm your account</a>
</p>
    }
    else
    {
<p>
        Please wait for approval.
</p>
    }
}
