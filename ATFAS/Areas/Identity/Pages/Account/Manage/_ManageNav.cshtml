﻿@inject SignInManager<AppUser> SignInManager
@using FeatureAuthorize
@using PermissionParts
@inject UserManager<AppUser> userManager
@{
    var hasExternalLogins = (await SignInManager.GetExternalAuthenticationSchemesAsync()).Any();
    var user = await userManager.GetUserAsync(User);
    var hasPassword = await userManager.HasPasswordAsync(user);
}
<ul class="nav nav-pills flex-column">
    <li class="nav-item"><a class="nav-link @ManageNavPages.IndexNavClass(ViewContext)" id="profile" asp-page="./Index">Profile</a></li>
    @*<li class="nav-item"><a class="nav-link @ManageNavPages.EmailNavClass(ViewContext)" id="email" asp-page="./Email">Email</a></li>*@
    @if (hasPassword)
    {
        <li class="nav-item"><a class="nav-link @ManageNavPages.ChangePasswordNavClass(ViewContext)" id="change-password" asp-page="./ChangePassword">Password</a></li>
    }
    <li class="nav-item"><a class="nav-link @ManageNavPages.SubscribeNavClass(ViewContext)" id="subscribe" asp-page="./Subscribe">Subscription</a></li>
    @*@if (User.UserHasThisPermission(Permissions.AirlineAftn) || User.UserHasThisPermission(Permissions.AirportAftn) || User.UserHasThisPermission(Permissions.AnspAftn))
    {
        <li class="nav-item"><a class="nav-link @ManageNavPages.AftnNavClass(ViewContext)" id="aftn" asp-page="./AftnAddress">AFTN Addresses</a></li>
    }*@
    @*@if (hasExternalLogins
        {
            <li id="external-logins" class="nav-item"><a id="external-login" class="nav-link @ManageNavPages.ExternalLoginsNavClass(ViewContext)" asp-page="./ExternalLogins">External logins</a></li>
        }
        <li class="nav-item"><a class="nav-link @ManageNavPages.TwoFactorAuthenticationNavClass(ViewContext)" id="two-factor" asp-page="./TwoFactorAuthentication">Two-factor authentication</a></li>
        <li class="nav-item"><a class="nav-link @ManageNavPages.PersonalDataNavClass(ViewContext)" id="personal-data" asp-page="./PersonalData">Personal data</a></li>*@
</ul>
