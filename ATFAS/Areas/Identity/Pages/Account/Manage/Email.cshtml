﻿@page
@model EmailModel
@{
    ViewData["Title"] = "Manage Email";
    ViewData["ActivePage"] = ManageNavPages.Email;
}

<h4>@ViewData["Title"]</h4>
<partial name="_StatusMessage" model="Model.StatusMessage" />
<div class="row">
    <div class="col-md-6">
        <form id="email-form" method="post">
            <div asp-validation-summary="All" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Email"></label>
                @if (Model.IsEmailConfirmed)
                {
                    <div class="input-group">
                        <input asp-for="Email" class="form-control" disabled />
                        <div class="input-group-append">
                            <span class="input-group-text text-success font-weight-bold">✓</span>
                        </div>
                    </div>
                }
                 else
                {
                    <input asp-for="Email" class="form-control" disabled />
                    <button id="email-verification" type="submit" asp-page-handler="SendVerificationEmail" class="btn btn-link">Send verification email</button>
                }
            </div>
            <div class="form-group">
                <label asp-for="Input.NewEmail"></label>
                <input asp-for="Input.NewEmail" class="form-control" />
                <span asp-validation-for="Input.NewEmail" class="text-danger"></span>
            </div>
            <button id="change-email-button" type="submit" asp-page-handler="ChangeEmail" class="btn btn-primary">Change email</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
