﻿@page
@using FeatureAuthorize
@using PermissionParts
@model SubscribeModel

@{
    ViewData["Title"] = "Subscription";
    ViewData["ActivePage"] = ManageNavPages.Subscription;
}

<h4>@ViewData["Title"]</h4>
<partial name="_StatusMessage" model="Model.StatusMessage" />
<div class="row">
    <div class="col-md-6">
        <form id="subscribe-form" method="post">
            <br />
            @*    @for (var i = 0; i < Model.Inputs.Count(); i++)
            {
            if ((@Model.Inputs[i].Name.Contains("AFTN")) && (User.UserHasThisPermission(Permissions.AirlineAftn) || User.UserHasThisPermission(Permissions.AirportAftn) || User.UserHasThisPermission(Permissions.AnspAftn))
            || !(@Model.Inputs[i].Name.Contains("AFTN")))
            {
            <input asp-for="@Model.Inputs[i].Name" type="hidden" />
            <div class="custom-control custom-switch">
            <input type="checkbox" class="custom-control-input text-uppercase" id="@Model.Inputs[i].Name" asp-for="@Model.Inputs[i].IsActive">
            <label class="custom-control-label" for="@Model.Inputs[i].Name">@Model.Inputs[i].Label</label>
            </div>
            if (@Model.Inputs[i].Name.Contains("AFTN")) /* && @Model.Inputs[i].IsActive*/
            {
            <small class="form-text text-muted aftn-section" style="margin-bottom: 0.5em; @(Model.Inputs[i].IsActive ?  "display:block" : "display:none") ">
            * Input multiple values by using comma (,)
            </small>
            <div class="form-group aftn-section" style="@(Model.Inputs[i].IsActive ?  "display:block" : "display:none")">
            <input asp-for="@Model.Inputs[i].ProfileNameAftn" type="hidden" />
            <input asp-for="@Model.Inputs[i].ProfileValueAftn" class="form-control text-uppercase" id="aftnInput" />
            <small id="AftnHelp" class="form-text"></small>
            </div>
            }
            <hr />
            }

            } *@

            @for (var i = 0; i < Model.Inputs.Count; i++)
            {
                var input = Model.Inputs[i];

                
                    <!-- Hidden field for Name -->
                    <input asp-for="Inputs[i].Name" type="hidden" />

                    <!-- Toggle -->
                <div class="custom-control custom-switch">
                        <input type="checkbox"
                               class="custom-control-input text-uppercase"
                               asp-for="Inputs[i].IsActive"
                               id="@input.Name" />
                        <label class="custom-control-label" for="@input.Name">@input.Label</label>
                    </div>
                    if (@Model.Inputs[i].Name.Contains("AFTN")) /* && @Model.Inputs[i].IsActive*/
                    {
                        <small class="form-text text-muted aftn-section" style="margin-bottom: 0.5em; @(Model.Inputs[i].IsActive ?  "display:block" : "display:none") ">
                            * Input multiple values by using comma (,)
                        </small>
                        <div class="form-group aftn-section" style="@(Model.Inputs[i].IsActive ?  "display:block" : "display:none")">
                            <input asp-for="@Model.Inputs[i].ProfileNameAftn" type="hidden" />
                            <input asp-for="@Model.Inputs[i].ProfileValueAftn" class="form-control text-uppercase" id="aftnInput" />
                            <small id="AftnHelp" class="form-text"></small>
                        </div>
                    }
               

                <hr />

            }



            <button id="subscribe-button" type="submit" class="btn btn-primary">Save</button>
        </form>
    </div>
</div>
@section Stylesheets {
    <style>
        .form-control.invalid {
            border-color: red;
        }

        .form-control + small {
            color: red;
            opacity: 0;
            height: 0;
            transition: opacity .4s ease-out;
        }

        .form-control.invalid + small {
            opacity: 1;
            height: auto;
            margin-bottom: 20px;
            transition: opacity .4s ease-out;
        }
    </style>
}

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
    <script src="~/js/regex-validation.js"></script>
    <script>
        var initAFTN = "";
        $(document).ready(function () {
            initAFTN = $('#aftnInput').val();
            validateAftnEvent();
            toggleAftnSubscribeEvent();

        });

        function toggleAftnSubscribeEvent() {
            $('input[id*="AFTN"]').on('change', function () {
                var countr = $('#aftnInput').val().length;
             
                if ($(this).is(":checked")) {
                    $('#aftnInput').val(initAFTN);
                    $('.aftn-section').show();

                    if ($('#aftnInput').val().length> 0)  {
                        if (regexValidate.aftn($('#aftnInput').val().toUpperCase())) {
                            $('#aftnInput').removeClass('invalid').addClass('valid');
                            $('#subscribe-button').attr("disabled", false);
                        } else {
                            $('#aftnInput').removeClass('valid').addClass('invalid');
                            $('#AftnHelp').text('Invalid AFTN input format');
                            $('#subscribe-button').attr("disabled", true);
                        }
                    }
                    else { $('#AftnHelp').text('AFTN could not be NULL'); $('#subscribe-button').attr("disabled", true); }
                }
                else {
                    $('.aftn-section').hide();
                    $('#aftnInput').removeClass('invalid').addClass('valid');
                    $('#subscribe-button').attr("disabled", false);
                }
            });
        }
        function validateAftnEvent() {
            $('#aftnInput').on('keyup', function () {
                console.log('input =' + $(this).val());
                if ($(this).val().length > 0 && $(this).val() != "") {
                    if (regexValidate.aftn($(this).val().toUpperCase())) {
                        $(this).removeClass('invalid').addClass('valid');
                        $('#subscribe-button').attr("disabled", false);
                    } else {
                        $(this).removeClass('valid').addClass('invalid');
                        $('#AftnHelp').text('Invalid AFTN input format');
                        $('#subscribe-button').attr("disabled", true);
                    }
                }
                else {
                    $(this).removeClass('invalid').addClass('valid');
                    $('#subscribe-button').attr("disabled", true);
                }
            });
        }
        //function validateAftn() {

        //        if (regexValidate.aftn($(this).val().toUpperCase())) {
        //            $(this).removeClass('invalid').addClass('valid');
        //            $('#subscribe-button').attr("disabled", false);
        //        } else {
        //            $(this).removeClass('valid').addClass('invalid');
        //            $('#subscribe-button').attr("disabled", true);
        //        }

        //}
    </script>
}
