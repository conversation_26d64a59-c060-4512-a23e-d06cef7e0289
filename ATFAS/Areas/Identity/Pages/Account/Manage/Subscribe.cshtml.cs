﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Encodings.Web;
using System.Linq;
using System.Threading.Tasks;
using ATFAS.Areas.Identity.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.WebUtilities;
using ATFAS.Data;
using Microsoft.EntityFrameworkCore;
using DataLayer.EfCode;
using PermissionParts;

namespace ATFAS.Areas.Identity.Pages.Account.Manage
{
    public partial class SubscribeModel : PageModel
    {
        private readonly UserManager<AppUser> _userManager;
        private readonly ATFASContext _atfasContext;
        private readonly ExtraAuthorizeDbContext _extraContext;
        //private readonly SignInManager<AppUser> _signInManager;
        //private readonly IEmailSender _emailSender;

        public SubscribeModel(UserManager<AppUser> userManager,ATFASContext atfasContext, ExtraAuthorizeDbContext extraContext)
        {
            _userManager = userManager;
            _atfasContext = atfasContext;
            _extraContext = extraContext;
        }

        // public string Username { get; set; }

        //public string Email { get; set; }

        //public bool IsEmailConfirmed { get; set; }

        [TempData]
        public string StatusMessage { get; set; }

        [BindProperty]
        public List<InputModel> Inputs { get; set; }

        public class InputModel
        {
            public bool IsActive { get; set; }
            public string Label { get; set; }
            public string Name { get; set; }
            public string ProfileValueAftn { get; set; }
            public string ProfileNameAftn{ get; set; }
           // public bool IsAftnSubscribe { get; set; }
        }
 
        private async Task LoadAsync(AppUser user)
        {
            var userProfiles = await _atfasContext.UserProfile.Where(p => p.UserId.Equals(user.Id)).ToListAsync();
            var subscribes = userProfiles.Where(p =>  p.ProfileName.Contains("Subscribe")).ToList();
            var addresse = userProfiles.Where(p => p.ProfileName.Contains("User AFTN Addresses")).FirstOrDefault();
            var roles = _extraContext.UserToRoles.Where(x => x.UserId.Equals(user.Id)).Select(x => x.RoleName).ToList();
            var permissions = GetPermissionInRole(roles);

            Inputs = new List<InputModel>();
            foreach (var itm in subscribes) {
                if (permissions.Contains(Permissions.AirlineAftn) || permissions.Contains(Permissions.AirportAftn) || permissions.Contains(Permissions.AnspAftn)) //
                {
                    Inputs.Add(new InputModel()
                    {
                        IsActive = itm.IsActive,
                        Label = itm.ProfileValue,
                        Name = itm.ProfileName.Replace(" ", "-"),
                        //IsAftnSubscribe = itm.ProfileName.Contains("AFTN"),

                        ProfileNameAftn = (itm.ProfileName.Contains("AFTN")) ? addresse.ProfileName : "",
                        ProfileValueAftn = (itm.ProfileName.Contains("AFTN")) ? addresse.ProfileValue : ""
                    });
                }
                else 
                {
                    if (!itm.ProfileName.Contains("AFTN"))
                    {
                        Inputs.Add(new InputModel()
                        {
                            IsActive = itm.IsActive,
                            Label = itm.ProfileValue,
                            Name = itm.ProfileName.Replace(" ", "-"),
                            //IsAftnSubscribe = itm.ProfileName.Contains("AFTN"),

                            ProfileNameAftn = (itm.ProfileName.Contains("AFTN")) ? addresse.ProfileName : "",
                            ProfileValueAftn = (itm.ProfileName.Contains("AFTN")) ? addresse.ProfileValue : ""
                        });
                    }
                }
                  
            }
        }
        private List<Permissions> GetPermissionInRole(List<string> roles)
        {
            List<Permissions> permissions = new List<Permissions>();
            foreach (var role in roles)
            {
                var pem = _extraContext.RolesToPermissions.Where(x => x.RoleName.Equals(role)).Select(x => x.PermissionsInRole).FirstOrDefault();
                permissions.AddRange(pem);
            }
            return permissions;
        }

        public async Task<IActionResult> OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            await LoadAsync(user);
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            var userProfiles = await _atfasContext.UserProfile.Where(p => p.UserId.Equals(user.Id)).ToListAsync();
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            if (!ModelState.IsValid)
            {
                await LoadAsync(user);
                return Page();
            }

            foreach (var itm in Inputs) {
                var name = itm.Name.Replace("-", " ");
                itm.ProfileNameAftn = (itm.ProfileNameAftn == null) ? "" : itm.ProfileNameAftn;
                var profile = _atfasContext.UserProfile.Where(p => p.UserId.Equals(user.Id)&&p.ProfileName.Equals(name)).FirstOrDefault();                        
                profile.IsActive = itm.IsActive;   
            }
            // AFTN Address
            var address = "";
            var sub = Inputs.Find(i => i.Name.Contains("AFTN"));
            var input = Inputs.Find(a => a.ProfileNameAftn.Contains("AFTN"));
            if (sub != null  && input != null) 
            {
                if (sub.IsActive)
                    address = input.ProfileValueAftn.ToUpper() ?? "";

               var aftn = _atfasContext.UserProfile.Where(a => a.UserId.Equals(user.Id) && a.ProfileName.Contains("User AFTN Addresses")).FirstOrDefault();
                aftn.ProfileValue = address;              
            }
          
           
            var result = await _atfasContext.SaveChangesAsync();
           
            if (result < 0) //if error
            {
                //foreach (var error in result.Errors)
                //{
                //    ModelState.AddModelError(string.Empty, error.Description);
                //}
                StatusMessage = "Update user profile failed";
                return RedirectToPage();
            }

           // await _signInManager.RefreshSignInAsync(user);
            StatusMessage = "Your profile has been updated";
            return RedirectToPage();
        }
    }
}
