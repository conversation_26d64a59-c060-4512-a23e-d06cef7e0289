﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Encodings.Web;
using System.Linq;
using System.Threading.Tasks;
using ATFAS.Areas.Identity.Data;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Identity.UI.Services;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using Microsoft.AspNetCore.WebUtilities;
using ATFAS.Data;
using Microsoft.EntityFrameworkCore;

namespace ATFAS.Areas.Identity.Pages.Account.Manage
{
    public partial class AftnAddressModel : PageModel
    {
        private readonly UserManager<AppUser> _userManager;
        private readonly ATFASContext _atfasContext;
        //private readonly SignInManager<AppUser> _signInManager;
        //private readonly IEmailSender _emailSender;

        public AftnAddressModel(UserManager<AppUser> userManager, ATFASContext atfasContext)
        {
            _userManager = userManager;
            _atfasContext = atfasContext;
        }

        // public string Username { get; set; }

        //public string Email { get; set; }

        //public bool IsEmailConfirmed { get; set; }

        [TempData]
        public string StatusMessage { get; set; }

        [BindProperty]
        public InputModel Input { get; set; }

        public class InputModel
        {
            public string Name { get; set; }
            public string Value { get; set; }
        }

        private async Task LoadAsync(AppUser user)
        {
            var aftn = await _atfasContext.UserProfile.Where(p => p.UserId.Equals(user.Id) && p.ProfileName.Contains("User AFTN Addresses")).FirstOrDefaultAsync();
            Input = new InputModel()
            {
                Value = aftn.ProfileValue,
                Name = aftn.ProfileName
            };

        }

        public async Task<IActionResult> OnGetAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            await LoadAsync(user);
            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            var user = await _userManager.GetUserAsync(User);
            if (user == null)
            {
                return NotFound($"Unable to load user with ID '{_userManager.GetUserId(User)}'.");
            }

            if (!ModelState.IsValid)
            {
                await LoadAsync(user);
                return Page();
            }
            var profile = _atfasContext.UserProfile.Where(p => p.UserId.Equals(user.Id) && p.ProfileName.Equals(Input.Name)).FirstOrDefault();
            profile.ProfileValue = Input.Value;

            var result = await _atfasContext.SaveChangesAsync();

            if (result < 1)
            {
                //foreach (var error in result.Errors)
                //{
                //    ModelState.AddModelError(string.Empty, error.Description);
                //}
                StatusMessage = "Update user profile failed";
                return RedirectToPage();
            }

            // await _signInManager.RefreshSignInAsync(user);
            StatusMessage = "Your profile has been updated";
            return RedirectToPage();
        }
    }
}
