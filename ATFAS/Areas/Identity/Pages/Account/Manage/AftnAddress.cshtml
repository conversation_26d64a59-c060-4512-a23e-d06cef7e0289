﻿@page
@model AftnAddressModel
@{
    ViewData["Title"] = "AFTN Addresses";
    ViewData["ActivePage"] = ManageNavPages.AftnAddress;
}

<h4>@ViewData["Title"]</h4>
<partial name="_StatusMessage" model="Model.StatusMessage" />
<div class="row">
    <div class="col-md-6">
        <form id="subscribe-form" method="post">
            <small class="form-text text-danger" style="margin-bottom: 0.5em;">
                * Input multiple values by using comma (,)
            </small>
            <div class="form-group">
                <input asp-for="@Model.Input.Name" type="hidden" />
                <input asp-for="@Model.Input.Value" class="form-control" />
            </div>
            <br />
            <br />
            <button id="aftn-button" type="submit" class="btn btn-primary">Save</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}
