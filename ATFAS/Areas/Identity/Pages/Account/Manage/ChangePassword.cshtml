﻿@page
@model ChangePasswordModel
@{
    ViewData["Title"] = "Change password";
    ViewData["ActivePage"] = ManageNavPages.ChangePassword;
}

<h4>@ViewData["Title"]</h4>
<partial name="_StatusMessage" for="StatusMessage" />
<div class="row">
    <div class="col-md-6">
        <form id="change-password-form" method="post">
            <div asp-validation-summary="All" class="text-danger"></div>
            <div class="form-group">
                <label asp-for="Input.OldPassword"></label>
                <input asp-for="Input.OldPassword" class="form-control" />
                <span asp-validation-for="Input.OldPassword" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Input.NewPassword"></label>
                <input asp-for="Input.NewPassword" class="form-control" />
                <span asp-validation-for="Input.NewPassword" class="text-danger"></span>
            </div>
            <div class="form-group">
                <label asp-for="Input.ConfirmPassword"></label>
                <input asp-for="Input.ConfirmPassword" class="form-control" />
                <span asp-validation-for="Input.ConfirmPassword" class="text-danger"></span>
            </div>
            <button type="submit" class="btn btn-primary">Update password</button>
        </form>
    </div>
</div>

@section Scripts {
    <partial name="_ValidationScriptsPartial" />
}