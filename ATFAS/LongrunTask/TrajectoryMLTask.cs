﻿using System;
using System.Collections.Generic;
using Microsoft.Data.SqlClient;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using ATFAS.Data;
using ATFAS.Models;
using ATFAS.ViewModels;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.ML;
using Microsoft.ML.Data;
using Microsoft.ML.Trainers;

namespace ATFAS.LongRunningTask
{
    /*LOCAL ONLY NO GITTTTTT*/
    public class TrajectoryMLTask
    {
        private readonly IWebHostEnvironment _hostingEnvironment;
        //private static string _appPath => Path.GetDirectoryName(Environment.GetCommandLineArgs()[0]);
        private readonly IConfiguration _configuration;
        private readonly ATFASContext _context;
        private static MLContext _mlContext;
        private static PredictionEngine<TrajectoryPredictModel, TrajectoryPredictionResult> _predEngine;
        private static ITransformer _trainedModel;
        static IDataView _trainDataView;
        Guid currentRequest;
        static string _ipaddress;
        static string _username;
        // private static string _modelPath => Path.Combine(_appPath, "..", "..", "..", "Models", "model.zip");

        public TrajectoryMLTask(IConfiguration configuration, ATFASContext ctx, IWebHostEnvironment hostingEnvironment,string ip,string user)
        {
            _configuration = configuration;
            _context = ctx;
            _hostingEnvironment = hostingEnvironment;
            _ipaddress = ip;
            _username = user;

        }

        public void RunTask(Guid requestId)
        {
            currentRequest = requestId;

            //Your actual long running task code goes here.

            //To run task in prallel

            //Method 1
            ThreadStart tsMethod1 = new ThreadStart(TrajectoryMLPredict);
            Thread tMethod1 = new Thread(tsMethod1);
            tMethod1.Start();

            Debug.WriteLine("Is thread 1 is alive : {0}",
                                          tMethod1.IsAlive);

            ////Method 2
            //ThreadStart tsMethod2 = new ThreadStart(Method2);
            //Thread tMethod2 = new Thread(tsMethod2);
            //tMethod2.Start();




        }

        public void TrajectoryMLPredict()
        {
            var status = new ProgressStatus();
            var stopwatch = new Stopwatch();
            stopwatch.Start();
           
            _mlContext = new MLContext();
            DatabaseLoader loader = _mlContext.Data.CreateDatabaseLoader<TrajectoryPredictModel>();

            string connectionString = _configuration.GetConnectionString("ATFASContext");
            string sqlCommand = @"SELECT    F.Callsign
	                                        ,F.AirportDeparture
	                                        ,F.AirportArrival
                                            ,T.AltitudeFt
	                                        , CONVERT(varchar(7), DATEPART(dw,StartTime)) AS DayOfaWeek
                                            ,T.Heading
                                            ,T.SpeedKn
	                                        ,CONCAT(PositionLine.ToString(),'|',AltitudeFt,'|',Heading,'|',SpeedKn,'|', DATEDIFF(minute, T.StartTime, T.EndTime)) AS PositionLong  
                                FROM FlightTrajectory AS T
                                INNER JOIN Flight AS F ON T.FlightId = F.Id 
                                WHERE T.FlightSourceId = 2";
            DatabaseSource dbSource = new DatabaseSource(SqlClientFactory.Instance, connectionString, sqlCommand,300); // add timeout
           

            IDataView data = loader.Load(dbSource);
            var split = _mlContext.Data.TrainTestSplit(data, testFraction: 0.1);
            _trainDataView = split.TrainSet;

            //var test = data.GetColumn<TrajectoryPredictModel>("PositionLong");


            var pipeline = ProcessData();
            var trainingPipeline = BuildAndTrainModel(_trainDataView, pipeline);
            var track = Evaluate(_trainDataView.Schema, split.TestSet, _hostingEnvironment);

            //createLog();

            stopwatch.Stop();
            status = track;
            status.Status = "done";
            status.Duration =  (int)stopwatch.Elapsed.TotalMinutes + " Minutes " + stopwatch.Elapsed.Seconds+ " Seconds"; 
            ProgressTracker.add(currentRequest, status);
        }
        //[HttpGet]
        //public async Task<IActionResult> get(IFormFile inputfile)
        //{


        //    return RedirectToAction("Index", "TrajectoryPredict");
        //}
        public static IEstimator<ITransformer> ProcessData()
        {

            // STEP 2: Common data process configuration with pipeline data transformations

            var pipeline = _mlContext.Transforms.Conversion.MapValueToKey(inputColumnName: "PositionLong", outputColumnName: "Label") //เปลี่ยนจากตัวอักษรเป็นตัวเลข
                            .Append(_mlContext.Transforms.Text.FeaturizeText(inputColumnName: "Callsign", outputColumnName: "CallsignFeaturized"))
                            .Append(_mlContext.Transforms.Text.FeaturizeText(inputColumnName: "AirportDeparture", outputColumnName: "DepartureFeaturized"))
                            .Append(_mlContext.Transforms.Text.FeaturizeText(inputColumnName: "AirportArrival", outputColumnName: "ArrivalFeaturized"))
                            .Append(_mlContext.Transforms.Text.FeaturizeText(inputColumnName: "DayOfaWeek", outputColumnName: "DayFeaturized"))
                            .Append(_mlContext.Transforms.Concatenate("Features", "CallsignFeaturized", "DepartureFeaturized", "ArrivalFeaturized", "DayFeaturized"))    //               
                            .AppendCacheCheckpoint(_mlContext); //ใช้สำหรับข้อมูลขนาดเล็กหรือขนาดปานกลางเท่านั้น


            return pipeline;
        }
        public static IEstimator<ITransformer> BuildAndTrainModel(IDataView trainingDataView, IEstimator<ITransformer> pipeline)
        {
            // STEP 3: Create the training algorithm/trainer   
            // Define trainer options.
            var options = new SdcaMaximumEntropyMulticlassTrainer.Options
            {
                // Make the convergence tolerance tighter.
                ConvergenceTolerance = 0.05f,
                // Increase the maximum number of passes over training data.
                MaximumNumberOfIterations = 40, //30
                LabelColumnName = "Label",
                FeatureColumnName = "Features"
            };
            var trainingPipeline = pipeline.Append(_mlContext.MulticlassClassification.Trainers.SdcaMaximumEntropy(options))
                   .Append(_mlContext.Transforms.Conversion.MapKeyToValue("PredictedLabel"));
            //var trainingPipeline = pipeline.Append(_mlContext.MulticlassClassification.Trainers.SdcaMaximumEntropy("Label", "Features"))
            //        .Append(_mlContext.Transforms.Conversion.MapKeyToValue("PredictedLabel"));
            /*var trainingPipeline = pipeline.Append(_mlContext.Cl.Trainers.LbfgsMaximumEntropy("Label", "Features"))
                   .Append(_mlContext.Transforms.Conversion.MapKeyToValue("PredictedLabel"));*/

            // STEP 4: Train the model fitting to the DataSet
            _trainedModel = trainingPipeline.Fit(trainingDataView);

            // * !! SAMPLE PREDICTION !! *//
            _predEngine = _mlContext.Model.CreatePredictionEngine<TrajectoryPredictModel, TrajectoryPredictionResult>(_trainedModel);

            TrajectoryPredictModel issue = new TrajectoryPredictModel()
            {
                //Title = "WebSockets communication is slow in my machine",
                //Description = "The WebSockets communication used under the covers by SignalR looks like is going slow in my development machine.."
                Callsign = "NOK123",
                AirportDeparture = "VTBD",
                AirportArrival = "VTCC",
                DayOfaWeek = "3",
            };
            // </SnippetCreateTestIssue1>

            // <SnippetPredict>
            var prediction = _predEngine.Predict(issue);


            return trainingPipeline;
        }
        public static ProgressStatus Evaluate(DataViewSchema trainingDataViewSchema, IDataView testDataView, IWebHostEnvironment hostingEnvironment)
        {          
            var result = new ProgressStatus();
            // STEP 5:  Evaluate the model in order to get the model's accuracy metrics
            Console.WriteLine($"=============== Evaluating to get model's accuracy metrics - Starting time: {DateTime.Now.ToString()} ===============");

            //Load the test dataset into the IDataView         
            //var testDataView = _mlContext.Data.LoadFromTextFile<GitHubIssue>(_testDataPath, hasHeader: true);


            //Evaluate the model on a test dataset and calculate metrics of the model on the test data.       
            var testMetrics = _mlContext.MulticlassClassification.Evaluate(_trainedModel.Transform(testDataView));
  
            result.MicroAccuracy = testMetrics.MacroAccuracy;
            result.MacroAccuracy = testMetrics.MacroAccuracy;
            result.LogLoss = testMetrics.LogLoss;
            result.LogLossReduction = testMetrics.LogLossReduction;


            //Console.WriteLine($"=============== Evaluating to get model's accuracy metrics - Ending time: {DateTime.Now.ToString()} ===============");
            //// <SnippetDisplayMetrics>
            //Console.WriteLine($"*************************************************************************************************************");
            //Console.WriteLine($"*       Metrics for Multi-class Classification model - Test Data     ");
            //Console.WriteLine($"*------------------------------------------------------------------------------------------------------------");
            //Console.WriteLine($"*       MicroAccuracy:    {testMetrics.MicroAccuracy:0.###}");
            //Console.WriteLine($"*       MacroAccuracy:    {testMetrics.MacroAccuracy:0.###}");
            //Console.WriteLine($"*       LogLoss:          {testMetrics.LogLoss:#.###}");
            //Console.WriteLine($"*       LogLossReduction: {testMetrics.LogLossReduction:#.###}");
            //Console.WriteLine($"*************************************************************************************************************");
            // </SnippetDisplayMetrics>

            // Save the new model to .ZIP file
            // <SnippetCallSaveModel>
            SaveModelAsFile(_mlContext, trainingDataViewSchema, _trainedModel, hostingEnvironment);
            //var log = createLog();
            return result;
        }
        private static void SaveModelAsFile(MLContext mlContext, DataViewSchema trainingDataViewSchema, ITransformer model, IWebHostEnvironment hostingEnvironment)
        {

            string staticPath = hostingEnvironment.WebRootPath;
            string _modelPath = Path.Combine(staticPath, "model", "model.zip");
            mlContext.Model.Save(model, trainingDataViewSchema, _modelPath);


            Console.WriteLine("The model is saved to {0}", _modelPath);
        }

      

        //[HttpPost]
        //public async Task<IActionResult> Upload(IFormFile inputfile)
        //{


        //    return RedirectToAction("Index", "TrajectoryPredict");
        //}

    }
}
