﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace ATFAS.Migrations
{
    public partial class addCTOTBOBCAT : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "CTOTBOBCAT",
                columns: table => new
                {
                    Id = table.Column<int>(type: "int", nullable: false)
                        .Annotation("SqlServer:Identity", "1, 1"),
                    FPLId = table.Column<int>(type: "int", nullable: false),
                    AircraftId = table.Column<string>(type: "varchar(50)", maxLength: 50, nullable: true),
                    Departure = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true),
                    Arrival = table.Column<string>(type: "varchar(100)", maxLength: 100, nullable: true),
                    EOBT = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ETOT = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ELDT = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EIBT = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CTOT = table.Column<DateTime>(type: "datetime2", nullable: true),
                    CTO = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Timestamp = table.Column<DateTime>(type: "datetime2", nullable: false),
                    IsCancelled = table.Column<bool>(type: "bit", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_CTOTBOBCAT", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "CTOTBOBCAT");
        }
    }
}
