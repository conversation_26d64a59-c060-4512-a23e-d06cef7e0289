﻿// <auto-generated />
using System;
using ATFAS.Data;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using NetTopologySuite.Geometries;

namespace ATFAS.Migrations
{
    [DbContext(typeof(ATFASContext))]
    [Migration("20250616073710_addATFMUCommentinCTOTForwarding")]
    partial class addATFMUCommentinCTOTForwarding
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .UseIdentityColumns()
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "5.0.1");

            modelBuilder.Entity("ATFAS.Areas.Identity.Data.AppUser", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("FirstName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime?>("LastAccessed")
                        .HasColumnType("datetime2");

                    b.Property<string>("LastName")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset");

                    b.Property<string>("NormalizedEmail")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedUserName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit");

                    b.Property<string>("UserName")
                        .IsRequired()
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedEmail")
                        .HasDatabaseName("EmailIndex");

                    b.HasIndex("NormalizedUserName")
                        .IsUnique()
                        .HasDatabaseName("UserNameIndex")
                        .HasFilter("[NormalizedUserName] IS NOT NULL");

                    b.ToTable("AspNetUsers");
                });

            modelBuilder.Entity("ATFAS.Models.ActionLog", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("EventDetails")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EventSource")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EventType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Ip")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("TimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("ActionLog");
                });

            modelBuilder.Entity("ATFAS.Models.Adp", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AdpContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AdpHeader")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FileIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("TimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("Uid")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Adp");
                });

            modelBuilder.Entity("ATFAS.Models.AdpConstraint", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("AdpDataId")
                        .HasColumnType("int");

                    b.Property<string>("Detail")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("IsConfirmed")
                        .HasColumnType("int");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remark")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AdpDataId");

                    b.ToTable("AdpConstraint");
                });

            modelBuilder.Entity("ATFAS.Models.AdpData", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AirspaceStatusBriefing")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<string>("AirspaceStatusBriefingFileId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("AtfmuId")
                        .HasColumnType("int");

                    b.Property<bool>("IsPublished")
                        .HasColumnType("bit");

                    b.Property<DateTime>("IssueTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("MeasureId")
                        .HasColumnType("int");

                    b.Property<string>("OtherInformation")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<int>("Revision")
                        .HasColumnType("int");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("ValidTime")
                        .HasColumnType("datetime2");

                    b.Property<string>("WeatherBriefing")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<string>("WeatherBriefingFileId")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("AtfmuId");

                    b.ToTable("AdpData");
                });

            modelBuilder.Entity("ATFAS.Models.AdpMeasure", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("AdpDataId")
                        .HasColumnType("int");

                    b.Property<string>("Detail")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<int?>("GDPID")
                        .HasColumnType("int");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MeasureType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Remark")
                        .HasMaxLength(2500)
                        .HasColumnType("nvarchar(2500)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("AdpDataId");

                    b.ToTable("AdpMeasure");
                });

            modelBuilder.Entity("ATFAS.Models.Airport", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("DESCRIPTION")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("DESIGNATOR")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("DT_CREATE")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DT_UPDATE")
                        .HasColumnType("datetime2");

                    b.Property<string>("FIR")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("FIX")
                        .HasColumnType("int");

                    b.Property<Geometry>("GEOPOINT")
                        .HasColumnType("geography");

                    b.Property<string>("LAT_DEGREE")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("LON_DEGREE")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<bool>("ROWDELETE")
                        .HasColumnType("bit");

                    b.Property<int>("TAXIIN")
                        .HasColumnType("int");

                    b.Property<int>("TAXIOUT")
                        .HasColumnType("int");

                    b.Property<string>("USER_CREATE")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("USER_UPDATE")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("Airport");
                });

            modelBuilder.Entity("ATFAS.Models.Announcement", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Catagory")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("FileIds")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Header")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Ip")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MessageContent")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("StartDate")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserAllowed")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Announcement");
                });

            modelBuilder.Entity("ATFAS.Models.Atfmu", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AftnAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Latitude")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Location")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Longitude")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Responsible")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Atfmu");
                });

            modelBuilder.Entity("ATFAS.Models.CTOTBOBCAT", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AircraftId")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Arrival")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("CTO")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("CTOT")
                        .HasColumnType("datetime2");

                    b.Property<string>("Departure")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("EIBT")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ELDT")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("EOBT")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("ETOT")
                        .HasColumnType("datetime2");

                    b.Property<int>("FPLId")
                        .HasColumnType("int");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Timestamp")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("CTOTBOBCAT");
                });

            modelBuilder.Entity("ATFAS.Models.CTOTForwarding", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("ATFMUComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AircraftId")
                        .HasMaxLength(50)
                        .HasColumnType("VARCHAR(50)");

                    b.Property<string>("AircraftType")
                        .HasMaxLength(50)
                        .HasColumnType("VARCHAR(50)");

                    b.Property<string>("Arrival")
                        .HasMaxLength(100)
                        .HasColumnType("VARCHAR(100)");

                    b.Property<string>("COMMENT")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<DateTime?>("CTOT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Departure")
                        .HasMaxLength(100)
                        .HasColumnType("VARCHAR(100)");

                    b.Property<DateTime?>("EIBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ELDT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("EOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ETOT")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("FPLId")
                        .HasColumnType("int");

                    b.Property<string>("Ip")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsUpdated")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("NEWEOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Originator")
                        .HasMaxLength(50)
                        .HasColumnType("VARCHAR(50)");

                    b.Property<string>("REASON")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("REGCAUSE")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("REGUL")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("RawMessage")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<int?>("Status")
                        .HasColumnType("int");

                    b.Property<DateTime?>("TimeStamp")
                        .HasColumnType("datetime");

                    b.Property<string>("UserComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("CTOTForwarding");
                });

            modelBuilder.Entity("ATFAS.Models.Capacity", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("CapacityPerHr")
                        .HasColumnType("int");

                    b.Property<bool>("IsArr")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDep")
                        .HasColumnType("bit");

                    b.Property<bool>("IsEntry")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOccupancy")
                        .HasColumnType("bit");

                    b.Property<bool>("IsOverall")
                        .HasColumnType("bit");

                    b.Property<string>("Point")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("TimeSaved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.Property<int>("TrafficAreaId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("Point");

                    b.HasIndex("TrafficAreaId");

                    b.ToTable("Capacity");
                });

            modelBuilder.Entity("ATFAS.Models.CapacityEvent", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("CapacityId")
                        .HasColumnType("int");

                    b.Property<int>("CapacityPerHr")
                        .HasColumnType("int");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Reason")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime>("TimeSaved")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("getutcdate()");

                    b.HasKey("Id");

                    b.HasIndex("CapacityId");

                    b.ToTable("CapacityEvent");
                });

            modelBuilder.Entity("ATFAS.Models.CapacityInterval", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("CapacityId")
                        .HasColumnType("int");

                    b.Property<int>("CapacityPerHr")
                        .HasColumnType("int");

                    b.Property<int?>("EndDay")
                        .HasColumnType("int");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("StartDay")
                        .HasColumnType("int");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("smalldatetime");

                    b.HasKey("Id");

                    b.HasIndex("CapacityId");

                    b.ToTable("CapacityInterval");
                });

            modelBuilder.Entity("ATFAS.Models.Fileupload", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<byte[]>("Content")
                        .HasColumnType("varbinary(max)");

                    b.Property<string>("Extension")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Filename")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Filepath")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<long>("Size")
                        .HasColumnType("bigint");

                    b.Property<DateTime?>("TimeStamp")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.ToTable("Fileupload");
                });

            modelBuilder.Entity("ATFAS.Models.Fix", b =>
                {
                    b.Property<int>("ID")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<bool>("BOUNDARY")
                        .HasColumnType("bit");

                    b.Property<string>("DESIGNATOR")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("DT_CREATE")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DT_UPDATE")
                        .HasColumnType("datetime2");

                    b.Property<string>("FIR")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("FIR2")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("FIXTYPE")
                        .HasColumnType("int");

                    b.Property<Point>("GEOPOINT")
                        .HasColumnType("geography");

                    b.Property<string>("LAT_DEGREE")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("LON_DEGREE")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("MaxLevelFt")
                        .HasColumnType("int");

                    b.Property<int>("MinLevelFt")
                        .HasColumnType("int");

                    b.Property<int>("RFDPID")
                        .HasColumnType("int");

                    b.Property<bool>("ROWDELETE")
                        .HasColumnType("bit");

                    b.Property<int>("RadiusNm")
                        .HasColumnType("int");

                    b.Property<string>("USER_CREATE")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("USER_UPDATE")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("ID");

                    b.ToTable("Fix");
                });

            modelBuilder.Entity("ATFAS.Models.Flight", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<DateTime?>("AIBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ALDT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("AOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ATOT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("AWUT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Additional")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AircraftType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("AirportAlternate")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("AirportArrival")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("AirportDeparture")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime?>("BOBETO")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("BOBFL")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("BOBWP")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("CIBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("CLDT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("COBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("CTOT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Callsign")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("DateOfFlight")
                        .HasColumnType("smalldatetime");

                    b.Property<TimeSpan?>("EET")
                        .HasColumnType("time");

                    b.Property<DateTime?>("EIBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("EIBTByDep")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("EIBTBySur")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("EIBTByTMCS")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ELDT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ELDTByDep")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ELDTBySur")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ELDTByTMCS")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("EOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("EOBTAirline")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ETOSID")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ETOSTAR")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("ETOT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Equipage")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<string>("FlightRule")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<int>("FlightSourceId")
                        .HasColumnType("int");

                    b.Property<string>("FlightType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("GUFI")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime?>("InboundTime")
                        .HasColumnType("smalldatetime");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExclude")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExempt")
                        .HasColumnType("bit");

                    b.Property<bool>("IsManaged")
                        .HasColumnType("bit");

                    b.Property<string>("LevelInitial")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NAV")
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("Number")
                        .HasColumnType("int");

                    b.Property<string>("Originator")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("OutboundTime")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("REG")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<long?>("RFDPID")
                        .HasColumnType("bigint");

                    b.Property<string>("Route")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoutePortion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RunwayArrival")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("RunwayDeparture")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<DateTime?>("SIBT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("SID")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("SLDT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("SOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("STAR")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("STOT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("SpeedInitial")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Squawk")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime?>("TIBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TLDT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TTOT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime>("TimeFiling")
                        .HasColumnType("datetime2");

                    b.Property<string>("WakeTurbulanceCategory")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.HasKey("Id");

                    b.HasIndex("FlightSourceId");

                    b.ToTable("Flight");
                });

            modelBuilder.Entity("ATFAS.Models.FlightSched", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AircraftType")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Callsign")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("DOF")
                        .HasColumnType("datetime2");

                    b.Property<string>("Destination")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Origin")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<DateTime>("SIST")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("STA")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("STD")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("STOT")
                        .HasColumnType("datetime2");

                    b.Property<string>("Source")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("flight")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("sector")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("FlightSched");
                });

            modelBuilder.Entity("ATFAS.Models.FlightSource", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Source")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("FlightSource");
                });

            modelBuilder.Entity("ATFAS.Models.FlightState", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("State")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("FlightState");
                });

            modelBuilder.Entity("ATFAS.Models.FlightTrajectory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .UseIdentityColumn();

                    b.Property<string>("AltitudeFt")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ElapseInSecond")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2");

                    b.Property<int>("FlightId")
                        .HasColumnType("int");

                    b.Property<int>("FlightSourceId")
                        .HasColumnType("int");

                    b.Property<string>("FlightState")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Heading")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<MultiLineString>("PositionLine")
                        .HasColumnType("geography");

                    b.Property<string>("SpeedKn")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeGenerated")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("FlightId");

                    b.HasIndex("FlightSourceId");

                    b.ToTable("FlightTrajectory");
                });

            modelBuilder.Entity("ATFAS.Models.ForwardSlotMessageReview", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AircraftId")
                        .HasMaxLength(50)
                        .HasColumnType("VARCHAR(50)");

                    b.Property<string>("Arrival")
                        .HasMaxLength(100)
                        .HasColumnType("VARCHAR(100)");

                    b.Property<string>("COMMENT")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<DateTime?>("CTOT")
                        .HasColumnType("smalldatetime");

                    b.Property<int?>("CTOTForwardingID")
                        .HasColumnType("int");

                    b.Property<string>("Departure")
                        .HasMaxLength(100)
                        .HasColumnType("VARCHAR(100)");

                    b.Property<DateTime?>("EOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("FilingTime")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("IOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<bool>("IsReviewed")
                        .HasColumnType("bit");

                    b.Property<string>("MessageType")
                        .HasMaxLength(50)
                        .HasColumnType("VARCHAR(50)");

                    b.Property<DateTime?>("NEWCTOT")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Originator")
                        .HasMaxLength(50)
                        .HasColumnType("VARCHAR(50)");

                    b.Property<string>("REASON")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("REGCAUSE")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("REGUL")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<string>("RawMessage")
                        .HasColumnType("VARCHAR(MAX)");

                    b.Property<TimeSpan?>("TaxiTime")
                        .HasColumnType("time");

                    b.Property<DateTime?>("TimeReviewed")
                        .HasColumnType("smalldatetime");

                    b.HasKey("Id");

                    b.ToTable("ForwardSlotMessageReview");
                });

            modelBuilder.Entity("ATFAS.Models.GDP", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int>("CapacityPerHr")
                        .HasColumnType("int");

                    b.Property<int>("CapacityRecoveryPerHr")
                        .HasColumnType("int");

                    b.Property<string>("Comment")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Designator")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime>("EndRecoveryTime")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("ExemptADEP")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ExemptADES")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("ExemptAirlines")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<int>("IntervalMin")
                        .HasColumnType("int");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsExecuted")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFPL")
                        .HasColumnType("bit");

                    b.Property<bool>("IsIFR")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSCH")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVFR")
                        .HasColumnType("bit");

                    b.Property<int?>("LowerFlightLevel")
                        .HasColumnType("int");

                    b.Property<string>("OnlyADEP")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("OnlyADES")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("OnlyAirlines")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("Point")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("RadiusNm")
                        .HasColumnType("int");

                    b.Property<string>("Regcause")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("Regul")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("TimeCancelled")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("TimeExecuted")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeSaved")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("TimeUpdated")
                        .HasColumnType("datetime2");

                    b.Property<int>("TrafficAreaId")
                        .HasColumnType("int");

                    b.Property<int?>("UpperFlightLevel")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TrafficAreaId");

                    b.ToTable("GDP");
                });

            modelBuilder.Entity("ATFAS.Models.GDPFlight", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<DateTime?>("CIBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("CIBTSaved")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("CLDT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("CLDTSaved")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("COBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("COBTSaved")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("CTOT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime?>("CTOTSaved")
                        .HasColumnType("smalldatetime");

                    b.Property<string>("Comment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("FlightId")
                        .HasColumnType("int");

                    b.Property<string>("Ip")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("IsCancelled")
                        .HasColumnType("bit");

                    b.Property<bool>("IsGdpExempt")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSent")
                        .HasColumnType("bit");

                    b.Property<bool>("IsTrial")
                        .HasColumnType("bit");

                    b.Property<DateTime?>("NewEOBT")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime>("TimeCancelled")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeSaved")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeSent")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeUpdated")
                        .HasColumnType("datetime2");

                    b.Property<string>("Username")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.HasIndex("FlightId");

                    b.ToTable("GDPFlight");
                });

            modelBuilder.Entity("ATFAS.Models.GeneralConfiguration", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<TimeSpan>("CtotAirlineTime")
                        .HasColumnType("time");

                    b.Property<int>("DefaultCtotOptions")
                        .HasColumnType("int");

                    b.Property<TimeSpan>("DefaultCtotTime")
                        .HasColumnType("time");

                    b.Property<string>("DefaultSWAirportList")
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan>("DefaultTaxiTime")
                        .HasColumnType("time");

                    b.Property<string>("ForwardComment")
                        .HasColumnType("nvarchar(max)");

                    b.Property<TimeSpan>("NewCtotBufferTime")
                        .HasColumnType("time");

                    b.Property<int>("SlotImprovementLeadTime")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("GeneralConfiguration");
                });

            modelBuilder.Entity("ATFAS.Models.Movie", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Genre")
                        .IsRequired()
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)");

                    b.Property<decimal>("Price")
                        .HasColumnType("decimal(18,2)");

                    b.Property<string>("Rating")
                        .IsRequired()
                        .HasMaxLength(5)
                        .HasColumnType("nvarchar(5)");

                    b.Property<DateTime>("ReleaseDate")
                        .HasColumnType("datetime2");

                    b.Property<string>("Title")
                        .IsRequired()
                        .HasMaxLength(60)
                        .HasColumnType("nvarchar(60)");

                    b.HasKey("Id");

                    b.ToTable("Movie");
                });

            modelBuilder.Entity("ATFAS.Models.Pointofcontact", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Address")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Airline")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("AirlineCode")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("EmailAddress")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Lastname")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("Id");

                    b.ToTable("Pointofcontact");
                });

            modelBuilder.Entity("ATFAS.Models.SAMMessage", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Addresses")
                        .IsRequired()
                        .HasMaxLength(2500)
                        .HasColumnType("VARCHAR(2500)");

                    b.Property<int>("CTOT_id")
                        .HasColumnType("int");

                    b.Property<DateTime>("DistributeTime")
                        .HasColumnType("datetime2");

                    b.Property<bool>("ISDistributed")
                        .HasColumnType("bit");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasColumnType("VARCHAR(MAX)");

                    b.HasKey("Id");

                    b.ToTable("SAMMessage");
                });

            modelBuilder.Entity("ATFAS.Models.StaticAirspace", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("AirspaceType")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Designator")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("DesignatorIcao")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Geometry>("Geography")
                        .HasColumnType("geography");

                    b.Property<int>("LowerLimitFt")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("UpperLimitFt")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("StaticAirspace");
                });

            modelBuilder.Entity("ATFAS.Models.TDSituationAwareness", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .UseIdentityColumn();

                    b.Property<string>("Arrival")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Capacity")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CapacityArr")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CapacityDep")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Departure")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Designator")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TimeInterval")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("TimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<int>("TrafficAreaId")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("TDSituationAwareness");
                });

            modelBuilder.Entity("ATFAS.Models.TrafficArea", b =>
                {
                    b.Property<int>("Id")
                        .HasColumnType("int");

                    b.Property<string>("Type")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("TrafficArea");
                });

            modelBuilder.Entity("ATFAS.Models.TrafficDemand", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<int?>("AheadHour")
                        .HasColumnType("int");

                    b.Property<string>("Designator")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<DateTime?>("EndTime")
                        .HasColumnType("smalldatetime");

                    b.Property<int>("IntervalMin")
                        .HasColumnType("int");

                    b.Property<bool>("IsATFM")
                        .HasColumnType("bit");

                    b.Property<bool>("IsATSMSG")
                        .HasColumnType("bit");

                    b.Property<bool>("IsArr")
                        .HasColumnType("bit");

                    b.Property<bool>("IsCombined")
                        .HasColumnType("bit");

                    b.Property<bool>("IsDep")
                        .HasColumnType("bit");

                    b.Property<bool>("IsFPL")
                        .HasColumnType("bit");

                    b.Property<bool>("IsIFR")
                        .HasColumnType("bit");

                    b.Property<bool>("IsPassed")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSCH")
                        .HasColumnType("bit");

                    b.Property<bool>("IsSUR")
                        .HasColumnType("bit");

                    b.Property<bool>("IsVFR")
                        .HasColumnType("bit");

                    b.Property<int?>("LowerFlightLevel")
                        .HasColumnType("int");

                    b.Property<string>("Point")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int?>("RadiusNm")
                        .HasColumnType("int");

                    b.Property<DateTime?>("StartTime")
                        .HasColumnType("smalldatetime");

                    b.Property<DateTime>("TimeSaved")
                        .HasColumnType("datetime2");

                    b.Property<int>("TrafficAreaId")
                        .HasColumnType("int");

                    b.Property<int?>("UpperFlightLevel")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("TrafficAreaId");

                    b.ToTable("TrafficDemand");
                });

            modelBuilder.Entity("ATFAS.Models.Trajectory", b =>
                {
                    b.Property<long>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bigint")
                        .UseIdentityColumn();

                    b.Property<int>("AltitudeFt")
                        .HasColumnType("int");

                    b.Property<int>("FlightId")
                        .HasColumnType("int");

                    b.Property<int>("FlightSourceId")
                        .HasColumnType("int");

                    b.Property<int>("FlightStateId")
                        .HasColumnType("int");

                    b.Property<double>("Heading")
                        .HasColumnType("float");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<LineString>("PositionLine")
                        .HasColumnType("geography");

                    b.Property<double>("SpeedKn")
                        .HasColumnType("float");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("TimeGenerated")
                        .HasColumnType("datetime2");

                    b.HasKey("Id");

                    b.HasIndex("FlightId");

                    b.HasIndex("FlightSourceId");

                    b.HasIndex("FlightStateId");

                    b.ToTable("Trajectory");
                });

            modelBuilder.Entity("ATFAS.Models.UserDefinedAirspace", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("Designator")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("GeoType")
                        .IsRequired()
                        .HasColumnType("nvarchar(50)");

                    b.Property<Geometry>("Geography")
                        .HasColumnType("geography");

                    b.Property<int>("LowerLimitFt")
                        .HasColumnType("int");

                    b.Property<string>("Name")
                        .IsRequired()
                        .HasMaxLength(250)
                        .HasColumnType("nvarchar(250)");

                    b.Property<int>("UpperLimitFt")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("UserDefinedAirspace");
                });

            modelBuilder.Entity("ATFAS.Models.UserNotification", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("LatestNotiId")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("LatestNotiTimeStamp")
                        .HasColumnType("datetime2");

                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.ToTable("UserNotification");
                });

            modelBuilder.Entity("ATFAS.Models.UserProfile", b =>
                {
                    b.Property<string>("UserId")
                        .HasMaxLength(450)
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ProfileName")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit");

                    b.Property<string>("ProfileValue")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "ProfileName");

                    b.ToTable("UserProfile");
                });

            modelBuilder.Entity("GDPGDPFlight", b =>
                {
                    b.Property<int>("GDPFlightsId")
                        .HasColumnType("int");

                    b.Property<int>("GDPsId")
                        .HasColumnType("int");

                    b.HasKey("GDPFlightsId", "GDPsId");

                    b.HasIndex("GDPsId");

                    b.ToTable("GDPGDPFlight");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRole", b =>
                {
                    b.Property<string>("Id")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("ConcurrencyStamp")
                        .IsConcurrencyToken()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.Property<string>("NormalizedName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)");

                    b.HasKey("Id");

                    b.HasIndex("NormalizedName")
                        .IsUnique()
                        .HasDatabaseName("RoleNameIndex")
                        .HasFilter("[NormalizedName] IS NOT NULL");

                    b.ToTable("AspNetRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RoleId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetRoleClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .UseIdentityColumn();

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserClaims");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderKey")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("UserId")
                        .IsRequired()
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("LoginProvider", "ProviderKey");

                    b.HasIndex("UserId");

                    b.ToTable("AspNetUserLogins");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("RoleId")
                        .HasColumnType("nvarchar(450)");

                    b.HasKey("UserId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("AspNetUserRoles");
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.Property<string>("UserId")
                        .HasColumnType("nvarchar(450)");

                    b.Property<string>("LoginProvider")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Name")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)");

                    b.HasKey("UserId", "LoginProvider", "Name");

                    b.ToTable("AspNetUserTokens");
                });

            modelBuilder.Entity("ATFAS.Models.AdpConstraint", b =>
                {
                    b.HasOne("ATFAS.Models.AdpData", "AdpData")
                        .WithMany("ADPConstraints")
                        .HasForeignKey("AdpDataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdpData");
                });

            modelBuilder.Entity("ATFAS.Models.AdpData", b =>
                {
                    b.HasOne("ATFAS.Models.Atfmu", "Atfmu")
                        .WithMany()
                        .HasForeignKey("AtfmuId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Atfmu");
                });

            modelBuilder.Entity("ATFAS.Models.AdpMeasure", b =>
                {
                    b.HasOne("ATFAS.Models.AdpData", "AdpData")
                        .WithMany("ADPMeasures")
                        .HasForeignKey("AdpDataId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AdpData");
                });

            modelBuilder.Entity("ATFAS.Models.Capacity", b =>
                {
                    b.HasOne("ATFAS.Models.TrafficArea", "TrafficArea")
                        .WithMany()
                        .HasForeignKey("TrafficAreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TrafficArea");
                });

            modelBuilder.Entity("ATFAS.Models.CapacityEvent", b =>
                {
                    b.HasOne("ATFAS.Models.Capacity", "Capacity")
                        .WithMany("CapacityEvents")
                        .HasForeignKey("CapacityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capacity");
                });

            modelBuilder.Entity("ATFAS.Models.CapacityInterval", b =>
                {
                    b.HasOne("ATFAS.Models.Capacity", "Capacity")
                        .WithMany("CapacityIntervals")
                        .HasForeignKey("CapacityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capacity");
                });

            modelBuilder.Entity("ATFAS.Models.Flight", b =>
                {
                    b.HasOne("ATFAS.Models.FlightSource", "FlightSource")
                        .WithMany()
                        .HasForeignKey("FlightSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FlightSource");
                });

            modelBuilder.Entity("ATFAS.Models.FlightTrajectory", b =>
                {
                    b.HasOne("ATFAS.Models.Flight", "Flight")
                        .WithMany()
                        .HasForeignKey("FlightId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ATFAS.Models.FlightSource", "FlightSource")
                        .WithMany()
                        .HasForeignKey("FlightSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Flight");

                    b.Navigation("FlightSource");
                });

            modelBuilder.Entity("ATFAS.Models.GDP", b =>
                {
                    b.HasOne("ATFAS.Models.TrafficArea", "TrafficArea")
                        .WithMany()
                        .HasForeignKey("TrafficAreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TrafficArea");
                });

            modelBuilder.Entity("ATFAS.Models.GDPFlight", b =>
                {
                    b.HasOne("ATFAS.Models.Flight", "Flight")
                        .WithMany()
                        .HasForeignKey("FlightId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Flight");
                });

            modelBuilder.Entity("ATFAS.Models.TrafficDemand", b =>
                {
                    b.HasOne("ATFAS.Models.TrafficArea", "TrafficArea")
                        .WithMany()
                        .HasForeignKey("TrafficAreaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TrafficArea");
                });

            modelBuilder.Entity("ATFAS.Models.Trajectory", b =>
                {
                    b.HasOne("ATFAS.Models.Flight", "Flight")
                        .WithMany()
                        .HasForeignKey("FlightId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ATFAS.Models.FlightSource", "FlightSource")
                        .WithMany()
                        .HasForeignKey("FlightSourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ATFAS.Models.FlightState", "FlightState")
                        .WithMany()
                        .HasForeignKey("FlightStateId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Flight");

                    b.Navigation("FlightSource");

                    b.Navigation("FlightState");
                });

            modelBuilder.Entity("ATFAS.Models.UserProfile", b =>
                {
                    b.HasOne("ATFAS.Areas.Identity.Data.AppUser", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("GDPGDPFlight", b =>
                {
                    b.HasOne("ATFAS.Models.GDPFlight", null)
                        .WithMany()
                        .HasForeignKey("GDPFlightsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ATFAS.Models.GDP", null)
                        .WithMany()
                        .HasForeignKey("GDPsId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityRoleClaim<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserClaim<string>", b =>
                {
                    b.HasOne("ATFAS.Areas.Identity.Data.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserLogin<string>", b =>
                {
                    b.HasOne("ATFAS.Areas.Identity.Data.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserRole<string>", b =>
                {
                    b.HasOne("Microsoft.AspNetCore.Identity.IdentityRole", null)
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("ATFAS.Areas.Identity.Data.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("Microsoft.AspNetCore.Identity.IdentityUserToken<string>", b =>
                {
                    b.HasOne("ATFAS.Areas.Identity.Data.AppUser", null)
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ATFAS.Models.AdpData", b =>
                {
                    b.Navigation("ADPConstraints");

                    b.Navigation("ADPMeasures");
                });

            modelBuilder.Entity("ATFAS.Models.Capacity", b =>
                {
                    b.Navigation("CapacityEvents");

                    b.Navigation("CapacityIntervals");
                });
#pragma warning restore 612, 618
        }
    }
}
