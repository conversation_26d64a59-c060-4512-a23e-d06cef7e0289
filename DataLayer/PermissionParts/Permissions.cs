﻿// Copyright (c) 2018 <PERSON>, GitHub: <PERSON><PERSON><PERSON>, web: http://www.thereformedprogrammer.net/
// Licensed under MIT license. See License.txt in the project root for license information.

using System;
using System.ComponentModel.DataAnnotations;

namespace PermissionParts
{
    public enum Permissions : short
    {
        NotSet = 0, //error condition

        ////Here is an example of very detailed control over something
        //[Display(GroupName = "Stock", Name = "Read", Description = "Can read stock")]
        //StockRead = 10,
        //[Display(GroupName = "Stock", Name = "Add new", Description = "Can add a new stock item")]
        //StockAddNew = 13,
        //[Display(GroupName = "Stock", Name = "Remove", Description = "Can read sales data")]
        //StockRemove = 14,

        //[Display(GroupName = "Sales", Name = "Read", Description = "Can delete a stock item")]
        //SalesRead = 20,
        //[Display(GroupName = "Sales", Name = "Sell", Description = "Can sell items from stock")]
        //SalesSell = 21,
        //[Display(GroupName = "Sales", Name = "Return", Description = "Can return an item to stock")]
        //SalesReturn = 22,

        ////[Display(GroupName = "Employees", Name = "Read", Description = "Can read company employees")]
        ////EmployeeRead = 30,
        [Display(GroupName = "UserDefault", Name = "Basic Permission", Description = "Basic Permission for every user")]
        UserDefault = 01,

        [Display(GroupName = "UserAdmin", Name = "Read users", Description = "Can list User")]
        UserRead = 10,
        //This is an example of grouping multiple actions under one permission
        [Display(GroupName = "UserAdmin", Name = "Alter user", Description = "Can do anything to the User")]
        UserChange = 11,
        [Display(GroupName = "UserAdmin", Name = "Read Roles", Description = "Can list Role")]
        RoleRead = 12,
        [Display(GroupName = "UserAdmin", Name = "Change Role", Description = "Can create, update or delete a Role")]
        RoleChange = 13,

        [Display(GroupName = "CtotOperation", Name = "Config Ctot", Description = "Can config Ctot Parameter")]
        CtotConfig = 20,
        [Display(GroupName = "CtotOperation", Name = "Change Ctot", Description = "Can change or delete Ctot Flight")]
        CtotChange = 21,
        [Display(GroupName = "CtotOperation", Name = "Request Ctot", Description = "Can Request New CTOT")]
        CtotRequest = 22,
        [Display(GroupName = "CtotOperation", Name = "Notice Ctot", Description = "Can Display CTOT Notification")]
        CtotNotice = 23,


        [Display(GroupName = "UserProfile", Name = "Airline Codes", Description = "Can update User Profile Airline Codes")]
        AirlineCode = 30,
        [Display(GroupName = "UserProfile", Name = "Airport Codes", Description = "Can update User Profile Airport Codes")]
        AirportCode = 31,

        [Display(GroupName = "AFTN User", Name = "Airport", Description = "Obtain AFTN for Airport Users")]
        AirportAftn= 40,
        [Display(GroupName = "AFTN User", Name = "Airline", Description = "Obtain AFTN for Airline Users")]
        AirlineAftn = 41,
        [Display(GroupName = "AFTN User", Name = "ANSP", Description = "Obtain AFTN for ANSP Users")]
        AnspAftn = 42,

        [Display(GroupName = "Announcement", Name = "Alter Announcement", Description = "Can Do Anything to Announcement")]
        AlterAnnounce = 50,
        [Display(GroupName = "Announcement", Name = "Update Announcement", Description = "Can Update Announcement")]
        UpdateAnnounce = 51,

        [Display(GroupName = "ADP", Name = "Alter ADP", Description = "Can do anything to ADP")]
        ADPChange = 60,

        [Display(GroupName = "Airsapces", Name = "Alter Static Airspaces", Description = "Can do anything to Static Airspaces")]
        StaticAirspaceChange = 70,
        [Display(GroupName = "Airsapces", Name = "Alter User Defined Airspaces", Description = "Can do anything to User Defined Airspaces")]
        UserDefinedAirspaceChange = 71,

        [Display(GroupName = "FlightSched", Name = "Upload Flight Schedule", Description = "Can Upload Flight Schedule to ATFAS System")]
        UploadFlightSched = 80,

        [Display(GroupName = "Management", Name = "Alter Point Of Contact", Description = "Can Do Anything to Point Of Contact Information")]
        ManagePoc = 90,
        [Display(GroupName = "Management", Name = "Alter ATFM Unit", Description = "Can Do Anything to ATFM Unit Information")]
        ManageAtfmu = 91,
        [Display(GroupName = "Management", Name = "Alter Resources", Description = "Can do anything to Resources Configuration")]
        ManageResources = 92,


        //[Display(GroupName = "Test", Name = "Read", Description = "Test Read")]
        //TestRead = 60,
        //[Display(GroupName = "Test", Name = "Write", Description = "Test Write")]
        //TestWrite = 61,

        //[Display(GroupName = "Impersonation", Name = "Impersonate - straight", Description = "Impersonate user using their permissions")]
        //Impersonate = 70,
        //[Display(GroupName = "Impersonation", Name = "Impersonate - enhanced", Description = "Impersonate user using current permissions")]
        //ImpersonateKeepOwnPermissions = 71,

        //This is an example of what to do with permission you don't used anymore.
        //You don't want its number to be reused as it could cause problems 
        //Just mark it as obsolete and the PermissionDisplay code won't show it
        //[Obsolete]
        //[Display(GroupName = "Old", Name = "Not used", Description = "example of old permission")]
        //OldPermissionNotUsed = 100,

        //This is an example of a permission linked to a optional (paid for?) feature
        //The code that turns roles to permissions can
        //remove this permission if the user isn't allowed to access this feature
        //[LinkedToModule(PaidForModules.Feature1)]
        //[Display(GroupName = "Features", Name = "Feature1", Description = "Can access feature1")]
        //Feature1Access = 1000,
        //[LinkedToModule(PaidForModules.Feature2)]
        //[Display(GroupName = "Features", Name = "Feature2", Description = "Can access feature2")]
        //Feature2Access = 1001,

        [Display(GroupName = "SuperAdmin", Name = "AccessAll", Description = "This allows the user to access every feature")]
        AccessAll = Int16.MaxValue, 
    }
}
